/**
 * Base Agent Definition.
 *
 * This module defines the BaseAgent class, which serves as the foundational component for all agents
 * within the library. It includes fundamental methods for setting environment variables, managing agent
 * status, and abstract methods for task execution which must be implemented by subclasses to handle
 * specific tasks.
 *
 * @module baseAgent
 */
import { Task } from '..';
import { TeamStore } from '../stores';
import { BaseTool } from '../tools/baseTool';
import { AGENT_STATUS_enum } from '../utils/enums';
import { AgentLoopResult } from '../utils/llm.types';
import { DefaultPrompts } from '../utils/prompts';
import { LangChainChatModel } from '../utils/agents';
/** LLM configuration options */
export interface LLMConfig {
    /** LLM service provider */
    provider: string;
    /** Model name/version */
    model: string;
    /** Maximum number of retries for failed requests */
    maxRetries: number;
    /** Base URL for API requests */
    apiBaseUrl?: string;
    /** API configuration object */
    configuration?: {
        /** Base path for API requests */
        baseURL: string;
    };
    /** Anthropic API URL */
    anthropicApiUrl?: string;
    /** Google base URL */
    baseUrl?: string;
    /** Mistral endpoint */
    endpoint?: string;
    /** API key */
    apiKey?: string;
    /** temperature */
    temperature?: number;
    /** top_p */
    topP?: number;
    /** frequency_penalty */
    frequencyPenalty?: number;
    /** presence_penalty */
    presencePenalty?: number;
    /** n */
    n?: number;
    /** stream */
    stream?: boolean;
}
/** Environment variables */
export interface Env {
    [key: string]: string;
}
/** Base agent constructor parameters */
export interface BaseAgentParams {
    /** Agent's unique identifier */
    id?: string;
    /** Agent's name */
    name: string;
    /** Agent's role description */
    role: string;
    /** Agent's goal */
    goal: string;
    /** Agent's background information */
    background: string;
    /** Available tools for the agent */
    tools?: BaseTool[];
    /** LLM configuration */
    llmConfig?: Partial<LLMConfig>;
    /** Maximum number of iterations */
    maxIterations?: number;
    /** Whether to force a final answer */
    forceFinalAnswer?: boolean;
    /** Custom prompt templates */
    promptTemplates?: DefaultPrompts;
    /** Environment variables */
    env?: Env;
    /** Kanban tools to enable */
    kanbanTools?: string[];
    /** LLM instance */
    llmInstance?: LangChainChatModel;
}
/** Base agent class */
export declare abstract class BaseAgent {
    /** Unique identifier */
    readonly id: string;
    /** Agent's name */
    readonly name: string;
    /** Agent's role description */
    readonly role: string;
    /** Agent's goal */
    readonly goal: string;
    /** Agent's background information */
    readonly background: string;
    /** Maximum number of iterations */
    readonly maxIterations: number;
    /** Store instance */
    protected store: TeamStore | null;
    /** Environment variables */
    protected env: Env;
    /** System message for LLM */
    llmSystemMessage: string | null;
    /** Whether to force a final answer */
    forceFinalAnswer: boolean;
    /** Prompt templates */
    promptTemplates: DefaultPrompts;
    /** LLM configuration */
    llmConfig: LLMConfig;
    /** Current agent status */
    status: AGENT_STATUS_enum;
    /** Available tools */
    tools: BaseTool[];
    /**
     * Creates a new BaseAgent instance
     * @param params - Agent initialization parameters
     */
    constructor({ id, name, role, goal, background, tools, llmConfig, maxIterations, forceFinalAnswer, promptTemplates, env, }: BaseAgentParams);
    initialize(store: TeamStore, env: Env): void;
    updateEnv(env: Env): void;
    /**
     * Normalizes LLM configuration based on provider
     * @param llmConfig - Raw LLM configuration
     * @returns Normalized LLM configuration
     */
    protected normalizeLlmConfig(llmConfig: Partial<LLMConfig>): LLMConfig;
    /**
     * Sets the store instance
     * @param store - Store instance
     */
    setStore(store: TeamStore): void;
    /**
     * Sets the agent's status
     * @param status - New status
     */
    setStatus(status: AGENT_STATUS_enum): void;
    /**
     * Sets environment variables
     * @param env - Environment variables
     */
    setEnv(env: Env): void;
    /**
     * Process a task
     * @param task - The task to process
     * @param inputs - Optional task inputs
     * @param context - Optional task context
     */
    workOnTask(_task: Task, _inputs?: Record<string, unknown>, _context?: string): Promise<AgentLoopResult>;
    /**
     * Process feedback for a task
     * @param task - The task to process feedback for
     * @param feedbackList - The feedback list
     */
    abstract workOnFeedback(_task: Task, _feedbackList: Array<{
        content: string;
    }>, _context: string): Promise<AgentLoopResult>;
    /**
     * Resume work on a task
     * @param task - Task to resume
     */
    abstract workOnTaskResume(task: Task): Promise<void>;
    /**
     * Reset the agent
     */
    reset(): void;
    abstract getCleanedAgent(): Partial<BaseAgent>;
}
