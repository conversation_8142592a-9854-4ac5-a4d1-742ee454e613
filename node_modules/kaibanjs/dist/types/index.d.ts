/**
 * API module for the Library.
 *
 * This module defines the primary classes used throughout the library, encapsulating
 * the core functionalities of agents, tasks, and team coordination. It serves as the
 * public interface for the library, allowing external applications to interact with
 * and utilize the main features provided.
 *
 * Classes:
 * - Agent: Represents an entity capable of performing tasks using specific AI models.
 *   Agents have properties such as name, role, and the tools they use, and are capable
 *   of executing tasks based on these properties.
 * - Task: Defines a specific activity or job that an agent can perform. Tasks are
 *   characterized by descriptions, expected outcomes, and their deliverability status.
 * - Team: Manages a group of agents and orchestrates the execution of tasks. It is
 *   responsible for coordinating the agents to achieve collective goals effectively.
 */
import { BaseAgent, Env } from './agents';
import { AGENT_STATUS_enum, TASK_STATUS_enum } from './utils/enums';
import { TeamStore } from './stores/teamStore.types';
import { ZodSchema } from 'zod';
import { WorkflowResult, WorkflowStats } from './types/logs';
import { BaseTool } from './tools/baseTool';
import { Lang<PERSON>hainChatModel, LLMConfig } from './utils/agents';
import { DefaultPrompts } from './utils/prompts';
import { TaskFeedback, TaskResult, TaskStats } from './stores/taskStore.types';
import { AgentLoopResult } from './utils/llm.types';
/**
 * Interface for Agent configuration
 */
export interface IAgentParams {
    type?: string;
    name: string;
    role: string;
    goal: string;
    background: string;
    tools?: BaseTool[];
    llmConfig?: LLMConfig;
    maxIterations?: number;
    forceFinalAnswer?: boolean;
    promptTemplates?: DefaultPrompts;
    llmInstance?: LangChainChatModel;
}
/**
 * Interface for Task configuration
 */
export interface ITaskParams {
    title?: string;
    id?: string;
    description: string;
    expectedOutput: string;
    agent: Agent;
    dependencies?: string[];
    isDeliverable?: boolean;
    externalValidationRequired?: boolean;
    outputSchema?: ZodSchema | null;
    allowParallelExecution?: boolean;
    referenceId?: string;
}
/**
 * Interface for Team configuration
 */
export interface ITeamParams {
    name: string;
    agents: Agent[];
    tasks: Task[];
    logLevel?: string;
    inputs?: Record<string, unknown>;
    env?: Env;
    insights?: string;
    memory?: boolean;
}
export declare class Agent {
    agentInstance: BaseAgent;
    type: string;
    constructor({ type, ...config }: IAgentParams);
    createAgent(type: string | undefined, config: IAgentParams): BaseAgent;
    workOnTask(task: Task, inputs: Record<string, unknown>, context: string): Promise<AgentLoopResult>;
    workOnTaskResume(task: Task): Promise<void>;
    workOnFeedback(task: Task, feedbackList: Array<{
        content: string;
    }>, context: string): Promise<AgentLoopResult>;
    setStatus(status: AGENT_STATUS_enum): void;
    initialize(store: TeamStore, env: Env): void;
    updateEnv(env: Env): void;
    reset(): void;
    get id(): string;
    get name(): string;
    get role(): string;
    get goal(): string;
    get background(): string;
    get tools(): BaseTool[];
    get status(): string;
    set status(status: AGENT_STATUS_enum);
    get llmConfig(): LLMConfig;
    get llmSystemMessage(): string | null;
    get forceFinalAnswer(): boolean;
    get promptTemplates(): DefaultPrompts;
}
export declare class Task {
    id: string;
    title: string;
    description: string;
    isDeliverable: boolean;
    agent: Agent;
    status: TASK_STATUS_enum;
    result: TaskResult | null;
    stats: TaskStats | null;
    duration: number | null;
    dependencies: string[];
    interpolatedTaskDescription: string | null;
    feedbackHistory: TaskFeedback[];
    externalValidationRequired: boolean;
    outputSchema: ZodSchema | null;
    expectedOutput: string;
    allowParallelExecution: boolean;
    referenceId?: string;
    inputs?: Record<string, unknown>;
    store?: TeamStore;
    constructor({ title, id, description, expectedOutput, agent, dependencies, isDeliverable, externalValidationRequired, outputSchema, allowParallelExecution, referenceId, }: ITaskParams);
}
/**
 * Represents a team of AI agents working on a set of tasks.
 * This class provides methods to control the workflow, interact with tasks,
 * and observe the state of the team's operations.
 */
export declare class Team {
    store: TeamStore;
    /**
     * Creates a new Team instance.
     *
     * @param config - The configuration object for the team.
     */
    constructor({ name, agents, tasks, logLevel, inputs, env, insights, memory, }: ITeamParams);
    /**
     * Pauses the team's workflow.
     * This method temporarily halts the workflow, allowing for manual intervention or adjustments.
     */
    pause(): Promise<void>;
    /**
     * Resumes the team's workflow.
     * This method continues the workflow after it has been paused.
     */
    resume(): Promise<void>;
    /**
     * Stops the team's workflow.
     * This method stops the workflow, preventing unknown further task execution.
     */
    stop(): Promise<void>;
    /**
     * Starts the team's workflow.
     * This method initiates the process of agents working on tasks.
     *
     * @param inputs - Optional inputs to override or supplement the initial inputs.
     * @returns A promise that resolves when the workflow completes or rejects on error.
     */
    start(inputs?: Record<string, unknown>): Promise<WorkflowResult>;
    /**
     * Provides direct access to the underlying store.
     * This method is intended for advanced users who need more control over the state.
     * More DX friendly for NodeJS Developers
     *
     * @returns The store object.
     */
    getStore(): TeamStore;
    /**
     * Provides direct access to the underlying store.
     * This method is intended for advanced users who need more control over the state.
     * More DX friendly for React Developers
     *
     * @returns The store object.
     */
    useStore(): TeamStore;
    /**
     * Enhanced subscribeToChanges to listen for specific properties
     *
     * @param listener - Function to call when properties change
     * @param properties - Array of property names to monitor
     * @returns Unsubscribe function
     */
    subscribeToChanges(listener: (changes: Record<string, unknown>) => void, properties?: string[]): () => void;
    /**
     * Provides feedback on a specific task.
     * This method is crucial for the Human-in-the-Loop (HITL) functionality,
     * allowing for human intervention and guidance in the AI workflow.
     *
     * @param taskId - The ID of the task to provide feedback on.
     * @param feedbackContent - The feedback to be incorporated into the task.
     */
    provideFeedback(taskId: string, feedbackContent: string): void;
    /**
     * Marks a task as validated.
     * This method is used in the HITL process to approve a task that required validation.
     *
     * @param taskId - The ID of the task to be marked as validated.
     */
    validateTask(taskId: string): void;
    /**
     * Subscribes to changes in the workflow status.
     * This method allows real-time monitoring of the overall workflow progress.
     *
     * @param callback - A function to be called when the workflow status changes.
     * @returns A function to unsubscribe from the status changes.
     */
    onWorkflowStatusChange(callback: (status: string) => void): () => void;
    /**
     * Retrieves tasks filtered by a specific status.
     *
     * @param status - The status to filter tasks by. Should be one of TASK_STATUS_enum values.
     * @returns An array of tasks with the specified status.
     */
    getTasksByStatus(status: string): Task[];
    /**
     * Retrieves the current status of the workflow.
     * This method provides a snapshot of the workflow's current state.
     *
     * @returns The current workflow status.
     */
    getWorkflowStatus(): string;
    /**
     * Retrieves the final result of the workflow.
     * This method should be called only after the workflow has finished.
     *
     * @returns The workflow result if finished, null otherwise.
     */
    getWorkflowResult(): unknown;
    /**
     * Retrieves all tasks in the team's workflow.
     * This method provides a comprehensive view of all tasks and their current states.
     *
     * @returns An array of all tasks.
     */
    getTasks(): Task[];
    /**
     * Retrieves the workflow completion statistics.
     * This method finds the completion log in the workflow logs and returns the associated statistics.
     *
     * @returns The workflow completion statistics, or null if no completion log is found.
     */
    getWorkflowStats(): WorkflowStats | null;
}
