import { Zod<PERSON>rror, ZodSchema } from 'zod';
import { FEEDBACK_STATUS_enum, WORKFLOW_STATUS_enum } from '../../utils/enums';
import { ParsedLLMOutput } from '../../utils/llm.types';
import { CostResult, LLMUsageStats } from '../../utils/llmCostCalculator';
export type Feedback = {
    content: string;
    status: FEEDBACK_STATUS_enum;
    timestamp: number;
};
export type LLMOutput = {
    parsedLLMOutput: ParsedLLMOutput;
    llmOutput: string;
    outputSchema?: ZodSchema;
    outputSchemaErrors?: ZodError;
};
export type WorkflowBaseMetadata = {
    message?: string;
};
export type CostMetadata = WorkflowBaseMetadata & {
    costDetails: CostResult;
    llmUsageStats: LLMUsageStats;
};
export interface WorkflowStats {
    startTime: number;
    endTime: number;
    duration: number;
    llmUsageStats: {
        inputTokens: number;
        outputTokens: number;
        callsCount: number;
        callsErrorCount: number;
        parsingErrors: number;
    };
    iterationCount: number;
    costDetails: {
        costInputTokens: number;
        costOutputTokens: number;
        totalCost: number;
    };
    teamName: string;
    taskCount: number;
    agentCount: number;
}
export interface WorkflowResult {
    status: WORKFLOW_STATUS_enum;
    result: unknown | null;
    stats: WorkflowStats | null;
}
export interface BaseWorkflowLog {
    timestamp: number;
    logDescription: string;
    logType: 'WorkflowStatusUpdate' | 'AgentStatusUpdate' | 'TaskStatusUpdate';
}
export type WorkflowLogMetadata = WorkflowBaseMetadata;
