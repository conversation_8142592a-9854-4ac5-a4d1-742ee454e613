{"name": "kaibanjs", "version": "0.21.1", "description": "AI Multi-Agent library for Javascript Developers.", "main": "./dist/bundle.cjs", "types": "./dist/bundle.d.ts", "module": "./dist/bundle.mjs", "unpkg": "./dist/bundle.js", "files": ["dist", "LICENSE", "README.md", "package.json", "xscripts/cli.mjs"], "exports": {".": {"import": {"types": "./dist/bundle.d.ts", "default": "./dist/bundle.mjs"}, "require": {"types": "./dist/bundle.d.ts", "default": "./dist/bundle.cjs"}}}, "scripts": {"build": "npx rollup -c", "build:test": "NODE_ENV=development TEST_ENV=mocked-llm-apis npx rollup -c", "dev": "NODE_ENV=development npx rollup -c -w", "tsc": "NODE_ENV=development npx rollup -c", "test": "npm run build:test && npm run test:integration", "test:watch": "TEST_ENV=mocked-llm-apis jest --testPathPattern='tests/e2e' --watch", "test:debug": "TEST_ENV=mocked-llm-apis node --inspect-brk node_modules/.bin/jest --runInBand --verbose --testPathPattern='tests/e2e'", "test:prod": "npm run build && jest --testPathPattern='tests/e2e'", "test:integration": "TEST_ENV=mocked-llm-apis jest --testPathPattern='tests/e2e' --verbose", "test:e2e": "TEST_ENV=real-llm-apis jest --testPathPattern='tests/e2e'", "test:unit": "jest --testPathPattern='tests/unit' --watch", "test:unit:debug": "node --inspect-brk node_modules/.bin/jest --runInBand --verbose --testPathPattern='tests/unit'", "test:types": "npx tsc", "play:react": "cd playground/react && npm run dev", "play:sb": "cd playground/react && npm run storybook", "play:node": "cd playground/nodejs && node index.js", "code-snapshot": "node xscripts/codeSnapshot.js", "lint:check": "eslint . --format stylish", "lint:fix": "eslint . --fix --format stylish", "format:check": "prettier --check .", "format:fix": "prettier --write . --list-different", "prepare": "husky", "cli": "node ./xscripts/cli.mjs"}, "bin": {"kaiban": "./xscripts/cli.mjs"}, "packageManager": "npm@9.8.1", "repository": {"type": "git", "url": "git+https://github.com/kaiban-ai/KaibanJS"}, "author": "AI Champions", "license": "MIT", "homepage": "https://kaibanjs.com", "bugs": {"url": "https://github.com/kaiban-ai/KaibanJS/issues"}, "keywords": ["ai agents", "multi-agent systems", "agentic", "javascript", "framework", "node.js", "kaibanjs", "langchain", "workflow automation", "task orchestration", "autonomous agents", "collaborative ai", "ai-powered applications", "human-ai interaction", "ai workflow", "large language models", "llm", "prompt engineering", "react", "nodejs", "nextjs"], "dependencies": {"@langchain/anthropic": "^0.3.19", "@langchain/community": "0.3.41", "@langchain/core": "0.3.49", "@langchain/deepseek": "^0.0.1", "@langchain/google-genai": "^0.2.5", "@langchain/mistralai": "^0.2.0", "@langchain/openai": "^0.5.7", "@telemetrydeck/sdk": "^2.0.4", "ansis": "^3.3.2", "chalk": "^5.3.0", "dependency-graph": "^1.0.0", "dotenv": "^16.4.5", "figlet": "^1.7.0", "langchain": "0.3.24", "loglevel": "^1.9.1", "ora": "^8.1.0", "p-queue": "^8.1.0", "readline": "^1.3.0", "uuid": "10.0.0", "zod-to-json-schema": "^3.22.3", "zustand": "4.5.2"}, "devDependencies": {"@babel/core": "^7.24.9", "@babel/plugin-syntax-import-meta": "^7.10.4", "@babel/preset-env": "^7.24.8", "@babel/preset-typescript": "^7.27.0", "@eslint/js": "^9.11.0", "@langchain/mcp-adapters": "^0.4.5", "@langchain/tavily": "^0.1.1", "@rollup/plugin-babel": "6.0.4", "@rollup/plugin-commonjs": "26.0.1", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-node-resolve": "^15.3.1", "@rollup/plugin-replace": "5.0.7", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.2", "@types/init-package-json": "^1.10.3", "@types/jest": "^29.5.14", "@typescript-eslint/eslint-plugin": "^8.6.0", "@typescript-eslint/parser": "^8.6.0", "babel-jest": "^29.7.0", "dotenv": "16.4.5", "eslint": "^9.11.0", "eslint-plugin-jest": "^28.8.3", "eslint-plugin-react": "^7.36.1", "globals": "^15.9.0", "husky": "^9.1.6", "init-package-json": "^6.0.3", "jest": "29.7.0", "lint-staged": "^15.2.10", "prettier": "2.3.2", "rollup": "4.21.1", "rollup-plugin-dts": "6.1.1", "rollup-plugin-esbuild": "^6.2.1", "rollup-plugin-node-polyfills": "^0.2.1", "rollup-plugin-peer-deps-external": "2.2.4", "terser-webpack-plugin": "^5.3.14", "ts-loader": "^9.5.2", "tslib": "^2.7.0", "typescript": "^5.5.4", "typescript-eslint": "^8.6.0"}, "engines": {"node": ">=12.0.0"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["prettier --write --list-different", "eslint --cache --fix --format stylish"], "*.{html,css,scss,sass,less,ejs,mjs,cjs}": ["prettier --write --list-different"]}}