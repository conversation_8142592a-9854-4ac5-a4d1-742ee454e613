/**
 * @licstart The following is the entire license notice for the
 * JavaScript code in this page
 *
 * Copyright 2024 Mozilla Foundation
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * @licend The above is the entire license notice for the
 * JavaScript code in this page
 */var t={9306:(t,e,i)=>{var s=i(4901),n=i(6823),r=TypeError;t.exports=function(t){if(s(t))return t;throw new r(n(t)+" is not a function")}},3506:(t,e,i)=>{var s=i(3925),n=String,r=TypeError;t.exports=function(t){if(s(t))return t;throw new r("Can't set "+n(t)+" as a prototype")}},7080:(t,e,i)=>{var s=i(4402).has;t.exports=function(t){s(t);return t}},679:(t,e,i)=>{var s=i(1625),n=TypeError;t.exports=function(t,e){if(s(e,t))return t;throw new n("Incorrect invocation")}},8551:(t,e,i)=>{var s=i(34),n=String,r=TypeError;t.exports=function(t){if(s(t))return t;throw new r(n(t)+" is not an object")}},7811:t=>{t.exports="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof DataView},7394:(t,e,i)=>{var s=i(4576),n=i(6706),r=i(2195),a=s.ArrayBuffer,o=s.TypeError;t.exports=a&&n(a.prototype,"byteLength","get")||function(t){if("ArrayBuffer"!==r(t))throw new o("ArrayBuffer expected");return t.byteLength}},3238:(t,e,i)=>{var s=i(4576),n=i(7476),r=i(7394),a=s.ArrayBuffer,o=a&&a.prototype,l=o&&n(o.slice);t.exports=function(t){if(0!==r(t))return!1;if(!l)return!1;try{l(t,0,0);return!1}catch(t){return!0}}},5169:(t,e,i)=>{var s=i(3238),n=TypeError;t.exports=function(t){if(s(t))throw new n("ArrayBuffer is detached");return t}},5636:(t,e,i)=>{var s=i(4576),n=i(9504),r=i(6706),a=i(7696),o=i(5169),l=i(7394),h=i(4483),c=i(1548),d=s.structuredClone,u=s.ArrayBuffer,p=s.DataView,g=Math.min,f=u.prototype,m=p.prototype,b=n(f.slice),v=r(f,"resizable","get"),y=r(f,"maxByteLength","get"),w=n(m.getInt8),A=n(m.setInt8);t.exports=(c||h)&&function(t,e,i){var s,n=l(t),r=void 0===e?n:a(e),f=!v||!v(t);o(t);if(c){t=d(t,{transfer:[t]});if(n===r&&(i||f))return t}if(n>=r&&(!i||f))s=b(t,0,r);else{var m=i&&!f&&y?{maxByteLength:y(t)}:void 0;s=new u(r,m);for(var x=new p(t),_=new p(s),E=g(r,n),S=0;S<E;S++)A(_,S,w(x,S))}c||h(t);return s}},4644:(t,e,i)=>{var s,n,r,a=i(7811),o=i(3724),l=i(4576),h=i(4901),c=i(34),d=i(9297),u=i(6955),p=i(6823),g=i(6699),f=i(6840),m=i(2106),b=i(1625),v=i(2787),y=i(2967),w=i(8227),A=i(3392),x=i(1181),_=x.enforce,E=x.get,S=l.Int8Array,C=S&&S.prototype,T=l.Uint8ClampedArray,M=T&&T.prototype,P=S&&v(S),D=C&&v(C),k=Object.prototype,R=l.TypeError,I=w("toStringTag"),L=A("TYPED_ARRAY_TAG"),O="TypedArrayConstructor",N=a&&!!y&&"Opera"!==u(l.opera),B=!1,H={Int8Array:1,Uint8Array:1,Uint8ClampedArray:1,Int16Array:2,Uint16Array:2,Int32Array:4,Uint32Array:4,Float32Array:4,Float64Array:8},U={BigInt64Array:8,BigUint64Array:8},getTypedArrayConstructor=function(t){var e=v(t);if(c(e)){var i=E(e);return i&&d(i,O)?i[O]:getTypedArrayConstructor(e)}},isTypedArray=function(t){if(!c(t))return!1;var e=u(t);return d(H,e)||d(U,e)};for(s in H)(r=(n=l[s])&&n.prototype)?_(r)[O]=n:N=!1;for(s in U)(r=(n=l[s])&&n.prototype)&&(_(r)[O]=n);if(!N||!h(P)||P===Function.prototype){P=function TypedArray(){throw new R("Incorrect invocation")};if(N)for(s in H)l[s]&&y(l[s],P)}if(!N||!D||D===k){D=P.prototype;if(N)for(s in H)l[s]&&y(l[s].prototype,D)}N&&v(M)!==D&&y(M,D);if(o&&!d(D,I)){B=!0;m(D,I,{configurable:!0,get:function(){return c(this)?this[L]:void 0}});for(s in H)l[s]&&g(l[s],L,s)}t.exports={NATIVE_ARRAY_BUFFER_VIEWS:N,TYPED_ARRAY_TAG:B&&L,aTypedArray:function(t){if(isTypedArray(t))return t;throw new R("Target is not a typed array")},aTypedArrayConstructor:function(t){if(h(t)&&(!y||b(P,t)))return t;throw new R(p(t)+" is not a typed array constructor")},exportTypedArrayMethod:function(t,e,i,s){if(o){if(i)for(var n in H){var r=l[n];if(r&&d(r.prototype,t))try{delete r.prototype[t]}catch(i){try{r.prototype[t]=e}catch(t){}}}D[t]&&!i||f(D,t,i?e:N&&C[t]||e,s)}},exportTypedArrayStaticMethod:function(t,e,i){var s,n;if(o){if(y){if(i)for(s in H)if((n=l[s])&&d(n,t))try{delete n[t]}catch(t){}if(P[t]&&!i)return;try{return f(P,t,i?e:N&&P[t]||e)}catch(t){}}for(s in H)!(n=l[s])||n[t]&&!i||f(n,t,e)}},getTypedArrayConstructor,isView:function isView(t){if(!c(t))return!1;var e=u(t);return"DataView"===e||d(H,e)||d(U,e)},isTypedArray,TypedArray:P,TypedArrayPrototype:D}},5370:(t,e,i)=>{var s=i(6198);t.exports=function(t,e,i){for(var n=0,r=arguments.length>2?i:s(e),a=new t(r);r>n;)a[n]=e[n++];return a}},9617:(t,e,i)=>{var s=i(5397),n=i(5610),r=i(6198),createMethod=function(t){return function(e,i,a){var o=s(e),l=r(o);if(0===l)return!t&&-1;var h,c=n(a,l);if(t&&i!=i){for(;l>c;)if((h=o[c++])!=h)return!0}else for(;l>c;c++)if((t||c in o)&&o[c]===i)return t||c||0;return!t&&-1}};t.exports={includes:createMethod(!0),indexOf:createMethod(!1)}},4527:(t,e,i)=>{var s=i(3724),n=i(4376),r=TypeError,a=Object.getOwnPropertyDescriptor,o=s&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(t){return t instanceof TypeError}}();t.exports=o?function(t,e){if(n(t)&&!a(t,"length").writable)throw new r("Cannot set read only .length");return t.length=e}:function(t,e){return t.length=e}},7680:(t,e,i)=>{var s=i(9504);t.exports=s([].slice)},7628:(t,e,i)=>{var s=i(6198);t.exports=function(t,e){for(var i=s(t),n=new e(i),r=0;r<i;r++)n[r]=t[i-r-1];return n}},9928:(t,e,i)=>{var s=i(6198),n=i(1291),r=RangeError;t.exports=function(t,e,i,a){var o=s(t),l=n(i),h=l<0?o+l:l;if(h>=o||h<0)throw new r("Incorrect index");for(var c=new e(o),d=0;d<o;d++)c[d]=d===h?a:t[d];return c}},6319:(t,e,i)=>{var s=i(8551),n=i(9539);t.exports=function(t,e,i,r){try{return r?e(s(i)[0],i[1]):e(i)}catch(e){n(t,"throw",e)}}},2195:(t,e,i)=>{var s=i(9504),n=s({}.toString),r=s("".slice);t.exports=function(t){return r(n(t),8,-1)}},6955:(t,e,i)=>{var s=i(2140),n=i(4901),r=i(2195),a=i(8227)("toStringTag"),o=Object,l="Arguments"===r(function(){return arguments}());t.exports=s?r:function(t){var e,i,s;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(i=function(t,e){try{return t[e]}catch(t){}}(e=o(t),a))?i:l?r(e):"Object"===(s=r(e))&&n(e.callee)?"Arguments":s}},7740:(t,e,i)=>{var s=i(9297),n=i(5031),r=i(7347),a=i(4913);t.exports=function(t,e,i){for(var o=n(e),l=a.f,h=r.f,c=0;c<o.length;c++){var d=o[c];s(t,d)||i&&s(i,d)||l(t,d,h(e,d))}}},2211:(t,e,i)=>{var s=i(9039);t.exports=!s((function(){function F(){}F.prototype.constructor=null;return Object.getPrototypeOf(new F)!==F.prototype}))},2529:t=>{t.exports=function(t,e){return{value:t,done:e}}},6699:(t,e,i)=>{var s=i(3724),n=i(4913),r=i(6980);t.exports=s?function(t,e,i){return n.f(t,e,r(1,i))}:function(t,e,i){t[e]=i;return t}},6980:t=>{t.exports=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}}},4659:(t,e,i)=>{var s=i(3724),n=i(4913),r=i(6980);t.exports=function(t,e,i){s?n.f(t,e,r(0,i)):t[e]=i}},2106:(t,e,i)=>{var s=i(283),n=i(4913);t.exports=function(t,e,i){i.get&&s(i.get,e,{getter:!0});i.set&&s(i.set,e,{setter:!0});return n.f(t,e,i)}},6840:(t,e,i)=>{var s=i(4901),n=i(4913),r=i(283),a=i(9433);t.exports=function(t,e,i,o){o||(o={});var l=o.enumerable,h=void 0!==o.name?o.name:e;s(i)&&r(i,h,o);if(o.global)l?t[e]=i:a(e,i);else{try{o.unsafe?t[e]&&(l=!0):delete t[e]}catch(t){}l?t[e]=i:n.f(t,e,{value:i,enumerable:!1,configurable:!o.nonConfigurable,writable:!o.nonWritable})}return t}},6279:(t,e,i)=>{var s=i(6840);t.exports=function(t,e,i){for(var n in e)s(t,n,e[n],i);return t}},9433:(t,e,i)=>{var s=i(4576),n=Object.defineProperty;t.exports=function(t,e){try{n(s,t,{value:e,configurable:!0,writable:!0})}catch(i){s[t]=e}return e}},3724:(t,e,i)=>{var s=i(9039);t.exports=!s((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]}))},4483:(t,e,i)=>{var s,n,r,a,o=i(4576),l=i(9429),h=i(1548),c=o.structuredClone,d=o.ArrayBuffer,u=o.MessageChannel,p=!1;if(h)p=function(t){c(t,{transfer:[t]})};else if(d)try{u||(s=l("worker_threads"))&&(u=s.MessageChannel);if(u){n=new u;r=new d(2);a=function(t){n.port1.postMessage(null,[t])};if(2===r.byteLength){a(r);0===r.byteLength&&(p=a)}}}catch(t){}t.exports=p},4055:(t,e,i)=>{var s=i(4576),n=i(34),r=s.document,a=n(r)&&n(r.createElement);t.exports=function(t){return a?r.createElement(t):{}}},6837:t=>{var e=TypeError;t.exports=function(t){if(t>9007199254740991)throw e("Maximum allowed index exceeded");return t}},5002:t=>{t.exports={IndexSizeError:{s:"INDEX_SIZE_ERR",c:1,m:1},DOMStringSizeError:{s:"DOMSTRING_SIZE_ERR",c:2,m:0},HierarchyRequestError:{s:"HIERARCHY_REQUEST_ERR",c:3,m:1},WrongDocumentError:{s:"WRONG_DOCUMENT_ERR",c:4,m:1},InvalidCharacterError:{s:"INVALID_CHARACTER_ERR",c:5,m:1},NoDataAllowedError:{s:"NO_DATA_ALLOWED_ERR",c:6,m:0},NoModificationAllowedError:{s:"NO_MODIFICATION_ALLOWED_ERR",c:7,m:1},NotFoundError:{s:"NOT_FOUND_ERR",c:8,m:1},NotSupportedError:{s:"NOT_SUPPORTED_ERR",c:9,m:1},InUseAttributeError:{s:"INUSE_ATTRIBUTE_ERR",c:10,m:1},InvalidStateError:{s:"INVALID_STATE_ERR",c:11,m:1},SyntaxError:{s:"SYNTAX_ERR",c:12,m:1},InvalidModificationError:{s:"INVALID_MODIFICATION_ERR",c:13,m:1},NamespaceError:{s:"NAMESPACE_ERR",c:14,m:1},InvalidAccessError:{s:"INVALID_ACCESS_ERR",c:15,m:1},ValidationError:{s:"VALIDATION_ERR",c:16,m:0},TypeMismatchError:{s:"TYPE_MISMATCH_ERR",c:17,m:1},SecurityError:{s:"SECURITY_ERR",c:18,m:1},NetworkError:{s:"NETWORK_ERR",c:19,m:1},AbortError:{s:"ABORT_ERR",c:20,m:1},URLMismatchError:{s:"URL_MISMATCH_ERR",c:21,m:1},QuotaExceededError:{s:"QUOTA_EXCEEDED_ERR",c:22,m:1},TimeoutError:{s:"TIMEOUT_ERR",c:23,m:1},InvalidNodeTypeError:{s:"INVALID_NODE_TYPE_ERR",c:24,m:1},DataCloneError:{s:"DATA_CLONE_ERR",c:25,m:1}}},8727:t=>{t.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},6193:(t,e,i)=>{var s=i(4215);t.exports="NODE"===s},2839:(t,e,i)=>{var s=i(4576).navigator,n=s&&s.userAgent;t.exports=n?String(n):""},9519:(t,e,i)=>{var s,n,r=i(4576),a=i(2839),o=r.process,l=r.Deno,h=o&&o.versions||l&&l.version,c=h&&h.v8;c&&(n=(s=c.split("."))[0]>0&&s[0]<4?1:+(s[0]+s[1]));!n&&a&&(!(s=a.match(/Edge\/(\d+)/))||s[1]>=74)&&(s=a.match(/Chrome\/(\d+)/))&&(n=+s[1]);t.exports=n},4215:(t,e,i)=>{var s=i(4576),n=i(2839),r=i(2195),userAgentStartsWith=function(t){return n.slice(0,t.length)===t};t.exports=userAgentStartsWith("Bun/")?"BUN":userAgentStartsWith("Cloudflare-Workers")?"CLOUDFLARE":userAgentStartsWith("Deno/")?"DENO":userAgentStartsWith("Node.js/")?"NODE":s.Bun&&"string"==typeof Bun.version?"BUN":s.Deno&&"object"==typeof Deno.version?"DENO":"process"===r(s.process)?"NODE":s.window&&s.document?"BROWSER":"REST"},8574:(t,e,i)=>{var s=i(9504),n=Error,r=s("".replace),a=String(new n("zxcasd").stack),o=/\n\s*at [^:]*:[^\n]*/,l=o.test(a);t.exports=function(t,e){if(l&&"string"==typeof t&&!n.prepareStackTrace)for(;e--;)t=r(t,o,"");return t}},6518:(t,e,i)=>{var s=i(4576),n=i(7347).f,r=i(6699),a=i(6840),o=i(9433),l=i(7740),h=i(2796);t.exports=function(t,e){var i,c,d,u,p,g=t.target,f=t.global,m=t.stat;if(i=f?s:m?s[g]||o(g,{}):s[g]&&s[g].prototype)for(c in e){u=e[c];d=t.dontCallGetSet?(p=n(i,c))&&p.value:i[c];if(!h(f?c:g+(m?".":"#")+c,t.forced)&&void 0!==d){if(typeof u==typeof d)continue;l(u,d)}(t.sham||d&&d.sham)&&r(u,"sham",!0);a(i,c,u,t)}}},9039:t=>{t.exports=function(t){try{return!!t()}catch(t){return!0}}},8745:(t,e,i)=>{var s=i(616),n=Function.prototype,r=n.apply,a=n.call;t.exports="object"==typeof Reflect&&Reflect.apply||(s?a.bind(r):function(){return a.apply(r,arguments)})},6080:(t,e,i)=>{var s=i(7476),n=i(9306),r=i(616),a=s(s.bind);t.exports=function(t,e){n(t);return void 0===e?t:r?a(t,e):function(){return t.apply(e,arguments)}}},616:(t,e,i)=>{var s=i(9039);t.exports=!s((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")}))},9565:(t,e,i)=>{var s=i(616),n=Function.prototype.call;t.exports=s?n.bind(n):function(){return n.apply(n,arguments)}},350:(t,e,i)=>{var s=i(3724),n=i(9297),r=Function.prototype,a=s&&Object.getOwnPropertyDescriptor,o=n(r,"name"),l=o&&"something"===function something(){}.name,h=o&&(!s||s&&a(r,"name").configurable);t.exports={EXISTS:o,PROPER:l,CONFIGURABLE:h}},6706:(t,e,i)=>{var s=i(9504),n=i(9306);t.exports=function(t,e,i){try{return s(n(Object.getOwnPropertyDescriptor(t,e)[i]))}catch(t){}}},7476:(t,e,i)=>{var s=i(2195),n=i(9504);t.exports=function(t){if("Function"===s(t))return n(t)}},9504:(t,e,i)=>{var s=i(616),n=Function.prototype,r=n.call,a=s&&n.bind.bind(r,r);t.exports=s?a:function(t){return function(){return r.apply(t,arguments)}}},9429:(t,e,i)=>{var s=i(4576),n=i(6193);t.exports=function(t){if(n){try{return s.process.getBuiltinModule(t)}catch(t){}try{return Function('return require("'+t+'")')()}catch(t){}}}},7751:(t,e,i)=>{var s=i(4576),n=i(4901);t.exports=function(t,e){return arguments.length<2?(i=s[t],n(i)?i:void 0):s[t]&&s[t][e];var i}},1767:t=>{t.exports=function(t){return{iterator:t,next:t.next,done:!1}}},8646:(t,e,i)=>{var s=i(9565),n=i(8551),r=i(1767),a=i(851);t.exports=function(t,e){e&&"string"==typeof t||n(t);var i=a(t);return r(n(void 0!==i?s(i,t):t))}},851:(t,e,i)=>{var s=i(6955),n=i(5966),r=i(4117),a=i(6269),o=i(8227)("iterator");t.exports=function(t){if(!r(t))return n(t,o)||n(t,"@@iterator")||a[s(t)]}},81:(t,e,i)=>{var s=i(9565),n=i(9306),r=i(8551),a=i(6823),o=i(851),l=TypeError;t.exports=function(t,e){var i=arguments.length<2?o(t):e;if(n(i))return r(s(i,t));throw new l(a(t)+" is not iterable")}},5966:(t,e,i)=>{var s=i(9306),n=i(4117);t.exports=function(t,e){var i=t[e];return n(i)?void 0:s(i)}},3789:(t,e,i)=>{var s=i(9306),n=i(8551),r=i(9565),a=i(1291),o=i(1767),l="Invalid size",h=RangeError,c=TypeError,d=Math.max,SetRecord=function(t,e){this.set=t;this.size=d(e,0);this.has=s(t.has);this.keys=s(t.keys)};SetRecord.prototype={getIterator:function(){return o(n(r(this.keys,this.set)))},includes:function(t){return r(this.has,this.set,t)}};t.exports=function(t){n(t);var e=+t.size;if(e!=e)throw new c(l);var i=a(e);if(i<0)throw new h(l);return new SetRecord(t,i)}},4576:function(t){var check=function(t){return t&&t.Math===Math&&t};t.exports=check("object"==typeof globalThis&&globalThis)||check("object"==typeof window&&window)||check("object"==typeof self&&self)||check("object"==typeof global&&global)||check("object"==typeof this&&this)||function(){return this}()||Function("return this")()},9297:(t,e,i)=>{var s=i(9504),n=i(8981),r=s({}.hasOwnProperty);t.exports=Object.hasOwn||function hasOwn(t,e){return r(n(t),e)}},421:t=>{t.exports={}},397:(t,e,i)=>{var s=i(7751);t.exports=s("document","documentElement")},5917:(t,e,i)=>{var s=i(3724),n=i(9039),r=i(4055);t.exports=!s&&!n((function(){return 7!==Object.defineProperty(r("div"),"a",{get:function(){return 7}}).a}))},7055:(t,e,i)=>{var s=i(9504),n=i(9039),r=i(2195),a=Object,o=s("".split);t.exports=n((function(){return!a("z").propertyIsEnumerable(0)}))?function(t){return"String"===r(t)?o(t,""):a(t)}:a},3167:(t,e,i)=>{var s=i(4901),n=i(34),r=i(2967);t.exports=function(t,e,i){var a,o;r&&s(a=e.constructor)&&a!==i&&n(o=a.prototype)&&o!==i.prototype&&r(t,o);return t}},3706:(t,e,i)=>{var s=i(9504),n=i(4901),r=i(7629),a=s(Function.toString);n(r.inspectSource)||(r.inspectSource=function(t){return a(t)});t.exports=r.inspectSource},1181:(t,e,i)=>{var s,n,r,a=i(8622),o=i(4576),l=i(34),h=i(6699),c=i(9297),d=i(7629),u=i(6119),p=i(421),g="Object already initialized",f=o.TypeError,m=o.WeakMap;if(a||d.state){var b=d.state||(d.state=new m);b.get=b.get;b.has=b.has;b.set=b.set;s=function(t,e){if(b.has(t))throw new f(g);e.facade=t;b.set(t,e);return e};n=function(t){return b.get(t)||{}};r=function(t){return b.has(t)}}else{var v=u("state");p[v]=!0;s=function(t,e){if(c(t,v))throw new f(g);e.facade=t;h(t,v,e);return e};n=function(t){return c(t,v)?t[v]:{}};r=function(t){return c(t,v)}}t.exports={set:s,get:n,has:r,enforce:function(t){return r(t)?n(t):s(t,{})},getterFor:function(t){return function(e){var i;if(!l(e)||(i=n(e)).type!==t)throw new f("Incompatible receiver, "+t+" required");return i}}}},4209:(t,e,i)=>{var s=i(8227),n=i(6269),r=s("iterator"),a=Array.prototype;t.exports=function(t){return void 0!==t&&(n.Array===t||a[r]===t)}},4376:(t,e,i)=>{var s=i(2195);t.exports=Array.isArray||function isArray(t){return"Array"===s(t)}},1108:(t,e,i)=>{var s=i(6955);t.exports=function(t){var e=s(t);return"BigInt64Array"===e||"BigUint64Array"===e}},4901:t=>{var e="object"==typeof document&&document.all;t.exports=void 0===e&&void 0!==e?function(t){return"function"==typeof t||t===e}:function(t){return"function"==typeof t}},2796:(t,e,i)=>{var s=i(9039),n=i(4901),r=/#|\.prototype\./,isForced=function(t,e){var i=o[a(t)];return i===h||i!==l&&(n(e)?s(e):!!e)},a=isForced.normalize=function(t){return String(t).replace(r,".").toLowerCase()},o=isForced.data={},l=isForced.NATIVE="N",h=isForced.POLYFILL="P";t.exports=isForced},4117:t=>{t.exports=function(t){return null==t}},34:(t,e,i)=>{var s=i(4901);t.exports=function(t){return"object"==typeof t?null!==t:s(t)}},3925:(t,e,i)=>{var s=i(34);t.exports=function(t){return s(t)||null===t}},6395:t=>{t.exports=!1},757:(t,e,i)=>{var s=i(7751),n=i(4901),r=i(1625),a=i(7040),o=Object;t.exports=a?function(t){return"symbol"==typeof t}:function(t){var e=s("Symbol");return n(e)&&r(e.prototype,o(t))}},507:(t,e,i)=>{var s=i(9565);t.exports=function(t,e,i){for(var n,r,a=i?t:t.iterator,o=t.next;!(n=s(o,a)).done;)if(void 0!==(r=e(n.value)))return r}},2652:(t,e,i)=>{var s=i(6080),n=i(9565),r=i(8551),a=i(6823),o=i(4209),l=i(6198),h=i(1625),c=i(81),d=i(851),u=i(9539),p=TypeError,Result=function(t,e){this.stopped=t;this.result=e},g=Result.prototype;t.exports=function(t,e,i){var f,m,b,v,y,w,A,x=i&&i.that,_=!(!i||!i.AS_ENTRIES),E=!(!i||!i.IS_RECORD),S=!(!i||!i.IS_ITERATOR),C=!(!i||!i.INTERRUPTED),T=s(e,x),stop=function(t){f&&u(f,"normal",t);return new Result(!0,t)},callFn=function(t){if(_){r(t);return C?T(t[0],t[1],stop):T(t[0],t[1])}return C?T(t,stop):T(t)};if(E)f=t.iterator;else if(S)f=t;else{if(!(m=d(t)))throw new p(a(t)+" is not iterable");if(o(m)){for(b=0,v=l(t);v>b;b++)if((y=callFn(t[b]))&&h(g,y))return y;return new Result(!1)}f=c(t,m)}w=E?t.next:f.next;for(;!(A=n(w,f)).done;){try{y=callFn(A.value)}catch(t){u(f,"throw",t)}if("object"==typeof y&&y&&h(g,y))return y}return new Result(!1)}},9539:(t,e,i)=>{var s=i(9565),n=i(8551),r=i(5966);t.exports=function(t,e,i){var a,o;n(t);try{if(!(a=r(t,"return"))){if("throw"===e)throw i;return i}a=s(a,t)}catch(t){o=!0;a=t}if("throw"===e)throw i;if(o)throw a;n(a);return i}},9462:(t,e,i)=>{var s=i(9565),n=i(2360),r=i(6699),a=i(6279),o=i(8227),l=i(1181),h=i(5966),c=i(7657).IteratorPrototype,d=i(2529),u=i(9539),p=o("toStringTag"),g="IteratorHelper",f="WrapForValidIterator",m=l.set,createIteratorProxyPrototype=function(t){var e=l.getterFor(t?f:g);return a(n(c),{next:function next(){var i=e(this);if(t)return i.nextHandler();try{var s=i.done?void 0:i.nextHandler();return d(s,i.done)}catch(t){i.done=!0;throw t}},return:function(){var i=e(this),n=i.iterator;i.done=!0;if(t){var r=h(n,"return");return r?s(r,n):d(void 0,!0)}if(i.inner)try{u(i.inner.iterator,"normal")}catch(t){return u(n,"throw",t)}n&&u(n,"normal");return d(void 0,!0)}})},b=createIteratorProxyPrototype(!0),v=createIteratorProxyPrototype(!1);r(v,p,"Iterator Helper");t.exports=function(t,e){var i=function Iterator(i,s){if(s){s.iterator=i.iterator;s.next=i.next}else s=i;s.type=e?f:g;s.nextHandler=t;s.counter=0;s.done=!1;m(this,s)};i.prototype=e?b:v;return i}},713:(t,e,i)=>{var s=i(9565),n=i(9306),r=i(8551),a=i(1767),o=i(9462),l=i(6319),h=o((function(){var t=this.iterator,e=r(s(this.next,t));if(!(this.done=!!e.done))return l(t,this.mapper,[e.value,this.counter++],!0)}));t.exports=function map(t){r(this);n(t);return new h(a(this),{mapper:t})}},7657:(t,e,i)=>{var s,n,r,a=i(9039),o=i(4901),l=i(34),h=i(2360),c=i(2787),d=i(6840),u=i(8227),p=i(6395),g=u("iterator"),f=!1;[].keys&&("next"in(r=[].keys())?(n=c(c(r)))!==Object.prototype&&(s=n):f=!0);!l(s)||a((function(){var t={};return s[g].call(t)!==t}))?s={}:p&&(s=h(s));o(s[g])||d(s,g,(function(){return this}));t.exports={IteratorPrototype:s,BUGGY_SAFARI_ITERATORS:f}},6269:t=>{t.exports={}},6198:(t,e,i)=>{var s=i(8014);t.exports=function(t){return s(t.length)}},283:(t,e,i)=>{var s=i(9504),n=i(9039),r=i(4901),a=i(9297),o=i(3724),l=i(350).CONFIGURABLE,h=i(3706),c=i(1181),d=c.enforce,u=c.get,p=String,g=Object.defineProperty,f=s("".slice),m=s("".replace),b=s([].join),v=o&&!n((function(){return 8!==g((function(){}),"length",{value:8}).length})),y=String(String).split("String"),w=t.exports=function(t,e,i){"Symbol("===f(p(e),0,7)&&(e="["+m(p(e),/^Symbol\(([^)]*)\).*$/,"$1")+"]");i&&i.getter&&(e="get "+e);i&&i.setter&&(e="set "+e);(!a(t,"name")||l&&t.name!==e)&&(o?g(t,"name",{value:e,configurable:!0}):t.name=e);v&&i&&a(i,"arity")&&t.length!==i.arity&&g(t,"length",{value:i.arity});try{i&&a(i,"constructor")&&i.constructor?o&&g(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var s=d(t);a(s,"source")||(s.source=b(y,"string"==typeof e?e:""));return t};Function.prototype.toString=w((function toString(){return r(this)&&u(this).source||h(this)}),"toString")},741:t=>{var e=Math.ceil,i=Math.floor;t.exports=Math.trunc||function trunc(t){var s=+t;return(s>0?i:e)(s)}},6043:(t,e,i)=>{var s=i(9306),n=TypeError,PromiseCapability=function(t){var e,i;this.promise=new t((function(t,s){if(void 0!==e||void 0!==i)throw new n("Bad Promise constructor");e=t;i=s}));this.resolve=s(e);this.reject=s(i)};t.exports.f=function(t){return new PromiseCapability(t)}},2603:(t,e,i)=>{var s=i(655);t.exports=function(t,e){return void 0===t?arguments.length<2?"":e:s(t)}},4149:t=>{var e=RangeError;t.exports=function(t){if(t==t)return t;throw new e("NaN is not allowed")}},2360:(t,e,i)=>{var s,n=i(8551),r=i(6801),a=i(8727),o=i(421),l=i(397),h=i(4055),c=i(6119),d="prototype",u="script",p=c("IE_PROTO"),EmptyConstructor=function(){},scriptTag=function(t){return"<"+u+">"+t+"</"+u+">"},NullProtoObjectViaActiveX=function(t){t.write(scriptTag(""));t.close();var e=t.parentWindow.Object;t=null;return e},NullProtoObject=function(){try{s=new ActiveXObject("htmlfile")}catch(t){}NullProtoObject="undefined"!=typeof document?document.domain&&s?NullProtoObjectViaActiveX(s):function(){var t,e=h("iframe"),i="java"+u+":";e.style.display="none";l.appendChild(e);e.src=String(i);(t=e.contentWindow.document).open();t.write(scriptTag("document.F=Object"));t.close();return t.F}():NullProtoObjectViaActiveX(s);for(var t=a.length;t--;)delete NullProtoObject[d][a[t]];return NullProtoObject()};o[p]=!0;t.exports=Object.create||function create(t,e){var i;if(null!==t){EmptyConstructor[d]=n(t);i=new EmptyConstructor;EmptyConstructor[d]=null;i[p]=t}else i=NullProtoObject();return void 0===e?i:r.f(i,e)}},6801:(t,e,i)=>{var s=i(3724),n=i(8686),r=i(4913),a=i(8551),o=i(5397),l=i(1072);e.f=s&&!n?Object.defineProperties:function defineProperties(t,e){a(t);for(var i,s=o(e),n=l(e),h=n.length,c=0;h>c;)r.f(t,i=n[c++],s[i]);return t}},4913:(t,e,i)=>{var s=i(3724),n=i(5917),r=i(8686),a=i(8551),o=i(6969),l=TypeError,h=Object.defineProperty,c=Object.getOwnPropertyDescriptor,d="enumerable",u="configurable",p="writable";e.f=s?r?function defineProperty(t,e,i){a(t);e=o(e);a(i);if("function"==typeof t&&"prototype"===e&&"value"in i&&p in i&&!i[p]){var s=c(t,e);if(s&&s[p]){t[e]=i.value;i={configurable:u in i?i[u]:s[u],enumerable:d in i?i[d]:s[d],writable:!1}}}return h(t,e,i)}:h:function defineProperty(t,e,i){a(t);e=o(e);a(i);if(n)try{return h(t,e,i)}catch(t){}if("get"in i||"set"in i)throw new l("Accessors not supported");"value"in i&&(t[e]=i.value);return t}},7347:(t,e,i)=>{var s=i(3724),n=i(9565),r=i(8773),a=i(6980),o=i(5397),l=i(6969),h=i(9297),c=i(5917),d=Object.getOwnPropertyDescriptor;e.f=s?d:function getOwnPropertyDescriptor(t,e){t=o(t);e=l(e);if(c)try{return d(t,e)}catch(t){}if(h(t,e))return a(!n(r.f,t,e),t[e])}},8480:(t,e,i)=>{var s=i(1828),n=i(8727).concat("length","prototype");e.f=Object.getOwnPropertyNames||function getOwnPropertyNames(t){return s(t,n)}},3717:(t,e)=>{e.f=Object.getOwnPropertySymbols},2787:(t,e,i)=>{var s=i(9297),n=i(4901),r=i(8981),a=i(6119),o=i(2211),l=a("IE_PROTO"),h=Object,c=h.prototype;t.exports=o?h.getPrototypeOf:function(t){var e=r(t);if(s(e,l))return e[l];var i=e.constructor;return n(i)&&e instanceof i?i.prototype:e instanceof h?c:null}},1625:(t,e,i)=>{var s=i(9504);t.exports=s({}.isPrototypeOf)},1828:(t,e,i)=>{var s=i(9504),n=i(9297),r=i(5397),a=i(9617).indexOf,o=i(421),l=s([].push);t.exports=function(t,e){var i,s=r(t),h=0,c=[];for(i in s)!n(o,i)&&n(s,i)&&l(c,i);for(;e.length>h;)n(s,i=e[h++])&&(~a(c,i)||l(c,i));return c}},1072:(t,e,i)=>{var s=i(1828),n=i(8727);t.exports=Object.keys||function keys(t){return s(t,n)}},8773:(t,e)=>{var i={}.propertyIsEnumerable,s=Object.getOwnPropertyDescriptor,n=s&&!i.call({1:2},1);e.f=n?function propertyIsEnumerable(t){var e=s(this,t);return!!e&&e.enumerable}:i},2967:(t,e,i)=>{var s=i(6706),n=i(34),r=i(7750),a=i(3506);t.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var t,e=!1,i={};try{(t=s(Object.prototype,"__proto__","set"))(i,[]);e=i instanceof Array}catch(t){}return function setPrototypeOf(i,s){r(i);a(s);if(!n(i))return i;e?t(i,s):i.__proto__=s;return i}}():void 0)},4270:(t,e,i)=>{var s=i(9565),n=i(4901),r=i(34),a=TypeError;t.exports=function(t,e){var i,o;if("string"===e&&n(i=t.toString)&&!r(o=s(i,t)))return o;if(n(i=t.valueOf)&&!r(o=s(i,t)))return o;if("string"!==e&&n(i=t.toString)&&!r(o=s(i,t)))return o;throw new a("Can't convert object to primitive value")}},5031:(t,e,i)=>{var s=i(7751),n=i(9504),r=i(8480),a=i(3717),o=i(8551),l=n([].concat);t.exports=s("Reflect","ownKeys")||function ownKeys(t){var e=r.f(o(t)),i=a.f;return i?l(e,i(t)):e}},8235:(t,e,i)=>{var s=i(9504),n=i(9297),r=SyntaxError,a=parseInt,o=String.fromCharCode,l=s("".charAt),h=s("".slice),c=s(/./.exec),d={'\\"':'"',"\\\\":"\\","\\/":"/","\\b":"\b","\\f":"\f","\\n":"\n","\\r":"\r","\\t":"\t"},u=/^[\da-f]{4}$/i,p=/^[\u0000-\u001F]$/;t.exports=function(t,e){for(var i=!0,s="";e<t.length;){var g=l(t,e);if("\\"===g){var f=h(t,e,e+2);if(n(d,f)){s+=d[f];e+=2}else{if("\\u"!==f)throw new r('Unknown escape sequence: "'+f+'"');var m=h(t,e+=2,e+4);if(!c(u,m))throw new r("Bad Unicode escape at: "+e);s+=o(a(m,16));e+=4}}else{if('"'===g){i=!1;e++;break}if(c(p,g))throw new r("Bad control character in string literal at: "+e);s+=g;e++}}if(i)throw new r("Unterminated string at: "+e);return{value:s,end:e}}},1103:t=>{t.exports=function(t){try{return{error:!1,value:t()}}catch(t){return{error:!0,value:t}}}},7750:(t,e,i)=>{var s=i(4117),n=TypeError;t.exports=function(t){if(s(t))throw new n("Can't call method on "+t);return t}},9286:(t,e,i)=>{var s=i(4402),n=i(8469),r=s.Set,a=s.add;t.exports=function(t){var e=new r;n(t,(function(t){a(e,t)}));return e}},3440:(t,e,i)=>{var s=i(7080),n=i(4402),r=i(9286),a=i(5170),o=i(3789),l=i(8469),h=i(507),c=n.has,d=n.remove;t.exports=function difference(t){var e=s(this),i=o(t),n=r(e);a(e)<=i.size?l(e,(function(t){i.includes(t)&&d(n,t)})):h(i.getIterator(),(function(t){c(e,t)&&d(n,t)}));return n}},4402:(t,e,i)=>{var s=i(9504),n=Set.prototype;t.exports={Set,add:s(n.add),has:s(n.has),remove:s(n.delete),proto:n}},8750:(t,e,i)=>{var s=i(7080),n=i(4402),r=i(5170),a=i(3789),o=i(8469),l=i(507),h=n.Set,c=n.add,d=n.has;t.exports=function intersection(t){var e=s(this),i=a(t),n=new h;r(e)>i.size?l(i.getIterator(),(function(t){d(e,t)&&c(n,t)})):o(e,(function(t){i.includes(t)&&c(n,t)}));return n}},4449:(t,e,i)=>{var s=i(7080),n=i(4402).has,r=i(5170),a=i(3789),o=i(8469),l=i(507),h=i(9539);t.exports=function isDisjointFrom(t){var e=s(this),i=a(t);if(r(e)<=i.size)return!1!==o(e,(function(t){if(i.includes(t))return!1}),!0);var c=i.getIterator();return!1!==l(c,(function(t){if(n(e,t))return h(c,"normal",!1)}))}},3838:(t,e,i)=>{var s=i(7080),n=i(5170),r=i(8469),a=i(3789);t.exports=function isSubsetOf(t){var e=s(this),i=a(t);return!(n(e)>i.size)&&!1!==r(e,(function(t){if(!i.includes(t))return!1}),!0)}},8527:(t,e,i)=>{var s=i(7080),n=i(4402).has,r=i(5170),a=i(3789),o=i(507),l=i(9539);t.exports=function isSupersetOf(t){var e=s(this),i=a(t);if(r(e)<i.size)return!1;var h=i.getIterator();return!1!==o(h,(function(t){if(!n(e,t))return l(h,"normal",!1)}))}},8469:(t,e,i)=>{var s=i(9504),n=i(507),r=i(4402),a=r.Set,o=r.proto,l=s(o.forEach),h=s(o.keys),c=h(new a).next;t.exports=function(t,e,i){return i?n({iterator:h(t),next:c},e):l(t,e)}},4916:(t,e,i)=>{var s=i(7751),createSetLike=function(t){return{size:t,has:function(){return!1},keys:function(){return{next:function(){return{done:!0}}}}}};t.exports=function(t){var e=s("Set");try{(new e)[t](createSetLike(0));try{(new e)[t](createSetLike(-1));return!1}catch(t){return!0}}catch(t){return!1}}},5170:(t,e,i)=>{var s=i(6706),n=i(4402);t.exports=s(n.proto,"size","get")||function(t){return t.size}},3650:(t,e,i)=>{var s=i(7080),n=i(4402),r=i(9286),a=i(3789),o=i(507),l=n.add,h=n.has,c=n.remove;t.exports=function symmetricDifference(t){var e=s(this),i=a(t).getIterator(),n=r(e);o(i,(function(t){h(e,t)?c(n,t):l(n,t)}));return n}},4204:(t,e,i)=>{var s=i(7080),n=i(4402).add,r=i(9286),a=i(3789),o=i(507);t.exports=function union(t){var e=s(this),i=a(t).getIterator(),l=r(e);o(i,(function(t){n(l,t)}));return l}},6119:(t,e,i)=>{var s=i(5745),n=i(3392),r=s("keys");t.exports=function(t){return r[t]||(r[t]=n(t))}},7629:(t,e,i)=>{var s=i(6395),n=i(4576),r=i(9433),a="__core-js_shared__",o=t.exports=n[a]||r(a,{});(o.versions||(o.versions=[])).push({version:"3.39.0",mode:s?"pure":"global",copyright:"© 2014-2024 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.39.0/LICENSE",source:"https://github.com/zloirock/core-js"})},5745:(t,e,i)=>{var s=i(7629);t.exports=function(t,e){return s[t]||(s[t]=e||{})}},1548:(t,e,i)=>{var s=i(4576),n=i(9039),r=i(9519),a=i(4215),o=s.structuredClone;t.exports=!!o&&!n((function(){if("DENO"===a&&r>92||"NODE"===a&&r>94||"BROWSER"===a&&r>97)return!1;var t=new ArrayBuffer(8),e=o(t,{transfer:[t]});return 0!==t.byteLength||8!==e.byteLength}))},4495:(t,e,i)=>{var s=i(9519),n=i(9039),r=i(4576).String;t.exports=!!Object.getOwnPropertySymbols&&!n((function(){var t=Symbol("symbol detection");return!r(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&s&&s<41}))},5610:(t,e,i)=>{var s=i(1291),n=Math.max,r=Math.min;t.exports=function(t,e){var i=s(t);return i<0?n(i+e,0):r(i,e)}},5854:(t,e,i)=>{var s=i(2777),n=TypeError;t.exports=function(t){var e=s(t,"number");if("number"==typeof e)throw new n("Can't convert number to bigint");return BigInt(e)}},7696:(t,e,i)=>{var s=i(1291),n=i(8014),r=RangeError;t.exports=function(t){if(void 0===t)return 0;var e=s(t),i=n(e);if(e!==i)throw new r("Wrong length or index");return i}},5397:(t,e,i)=>{var s=i(7055),n=i(7750);t.exports=function(t){return s(n(t))}},1291:(t,e,i)=>{var s=i(741);t.exports=function(t){var e=+t;return e!=e||0===e?0:s(e)}},8014:(t,e,i)=>{var s=i(1291),n=Math.min;t.exports=function(t){var e=s(t);return e>0?n(e,9007199254740991):0}},8981:(t,e,i)=>{var s=i(7750),n=Object;t.exports=function(t){return n(s(t))}},9590:(t,e,i)=>{var s=i(1291),n=RangeError;t.exports=function(t){var e=s(t);if(e<0)throw new n("The argument can't be less than 0");return e}},2777:(t,e,i)=>{var s=i(9565),n=i(34),r=i(757),a=i(5966),o=i(4270),l=i(8227),h=TypeError,c=l("toPrimitive");t.exports=function(t,e){if(!n(t)||r(t))return t;var i,l=a(t,c);if(l){void 0===e&&(e="default");i=s(l,t,e);if(!n(i)||r(i))return i;throw new h("Can't convert object to primitive value")}void 0===e&&(e="number");return o(t,e)}},6969:(t,e,i)=>{var s=i(2777),n=i(757);t.exports=function(t){var e=s(t,"string");return n(e)?e:e+""}},2140:(t,e,i)=>{var s={};s[i(8227)("toStringTag")]="z";t.exports="[object z]"===String(s)},655:(t,e,i)=>{var s=i(6955),n=String;t.exports=function(t){if("Symbol"===s(t))throw new TypeError("Cannot convert a Symbol value to a string");return n(t)}},6823:t=>{var e=String;t.exports=function(t){try{return e(t)}catch(t){return"Object"}}},3392:(t,e,i)=>{var s=i(9504),n=0,r=Math.random(),a=s(1..toString);t.exports=function(t){return"Symbol("+(void 0===t?"":t)+")_"+a(++n+r,36)}},7040:(t,e,i)=>{var s=i(4495);t.exports=s&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},8686:(t,e,i)=>{var s=i(3724),n=i(9039);t.exports=s&&n((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))},2812:t=>{var e=TypeError;t.exports=function(t,i){if(t<i)throw new e("Not enough arguments");return t}},8622:(t,e,i)=>{var s=i(4576),n=i(4901),r=s.WeakMap;t.exports=n(r)&&/native code/.test(String(r))},8227:(t,e,i)=>{var s=i(4576),n=i(5745),r=i(9297),a=i(3392),o=i(4495),l=i(7040),h=s.Symbol,c=n("wks"),d=l?h.for||h:h&&h.withoutSetter||a;t.exports=function(t){r(c,t)||(c[t]=o&&r(h,t)?h[t]:d("Symbol."+t));return c[t]}},6573:(t,e,i)=>{var s=i(3724),n=i(2106),r=i(3238),a=ArrayBuffer.prototype;s&&!("detached"in a)&&n(a,"detached",{configurable:!0,get:function detached(){return r(this)}})},7936:(t,e,i)=>{var s=i(6518),n=i(5636);n&&s({target:"ArrayBuffer",proto:!0},{transferToFixedLength:function transferToFixedLength(){return n(this,arguments.length?arguments[0]:void 0,!1)}})},8100:(t,e,i)=>{var s=i(6518),n=i(5636);n&&s({target:"ArrayBuffer",proto:!0},{transfer:function transfer(){return n(this,arguments.length?arguments[0]:void 0,!0)}})},4114:(t,e,i)=>{var s=i(6518),n=i(8981),r=i(6198),a=i(4527),o=i(6837);s({target:"Array",proto:!0,arity:1,forced:i(9039)((function(){return 4294967297!==[].push.call({length:4294967296},1)}))||!function(){try{Object.defineProperty([],"length",{writable:!1}).push()}catch(t){return t instanceof TypeError}}()},{push:function push(t){var e=n(this),i=r(e),s=arguments.length;o(i+s);for(var l=0;l<s;l++){e[i]=arguments[l];i++}a(e,i);return i}})},8111:(t,e,i)=>{var s=i(6518),n=i(4576),r=i(679),a=i(8551),o=i(4901),l=i(2787),h=i(2106),c=i(4659),d=i(9039),u=i(9297),p=i(8227),g=i(7657).IteratorPrototype,f=i(3724),m=i(6395),b="constructor",v="Iterator",y=p("toStringTag"),w=TypeError,A=n[v],x=m||!o(A)||A.prototype!==g||!d((function(){A({})})),_=function Iterator(){r(this,g);if(l(this)===g)throw new w("Abstract class Iterator not directly constructable")},defineIteratorPrototypeAccessor=function(t,e){f?h(g,t,{configurable:!0,get:function(){return e},set:function(e){a(this);if(this===g)throw new w("You can't redefine this property");u(this,t)?this[t]=e:c(this,t,e)}}):g[t]=e};u(g,y)||defineIteratorPrototypeAccessor(y,v);!x&&u(g,b)&&g[b]!==Object||defineIteratorPrototypeAccessor(b,_);_.prototype=g;s({global:!0,constructor:!0,forced:x},{Iterator:_})},9314:(t,e,i)=>{var s=i(6518),n=i(9565),r=i(8551),a=i(1767),o=i(4149),l=i(9590),h=i(9462),c=i(6395),d=h((function(){for(var t,e=this.iterator,i=this.next;this.remaining;){this.remaining--;t=r(n(i,e));if(this.done=!!t.done)return}t=r(n(i,e));if(!(this.done=!!t.done))return t.value}));s({target:"Iterator",proto:!0,real:!0,forced:c},{drop:function drop(t){r(this);var e=l(o(+t));return new d(a(this),{remaining:e})}})},1148:(t,e,i)=>{var s=i(6518),n=i(2652),r=i(9306),a=i(8551),o=i(1767);s({target:"Iterator",proto:!0,real:!0},{every:function every(t){a(this);r(t);var e=o(this),i=0;return!n(e,(function(e,s){if(!t(e,i++))return s()}),{IS_RECORD:!0,INTERRUPTED:!0}).stopped}})},2489:(t,e,i)=>{var s=i(6518),n=i(9565),r=i(9306),a=i(8551),o=i(1767),l=i(9462),h=i(6319),c=i(6395),d=l((function(){for(var t,e,i=this.iterator,s=this.predicate,r=this.next;;){t=a(n(r,i));if(this.done=!!t.done)return;e=t.value;if(h(i,s,[e,this.counter++],!0))return e}}));s({target:"Iterator",proto:!0,real:!0,forced:c},{filter:function filter(t){a(this);r(t);return new d(o(this),{predicate:t})}})},531:(t,e,i)=>{var s=i(6518),n=i(9565),r=i(9306),a=i(8551),o=i(1767),l=i(8646),h=i(9462),c=i(9539),d=i(6395),u=h((function(){for(var t,e,i=this.iterator,s=this.mapper;;){if(e=this.inner)try{if(!(t=a(n(e.next,e.iterator))).done)return t.value;this.inner=null}catch(t){c(i,"throw",t)}t=a(n(this.next,i));if(this.done=!!t.done)return;try{this.inner=l(s(t.value,this.counter++),!1)}catch(t){c(i,"throw",t)}}}));s({target:"Iterator",proto:!0,real:!0,forced:d},{flatMap:function flatMap(t){a(this);r(t);return new u(o(this),{mapper:t,inner:null})}})},1701:(t,e,i)=>{var s=i(6518),n=i(713);s({target:"Iterator",proto:!0,real:!0,forced:i(6395)},{map:n})},3579:(t,e,i)=>{var s=i(6518),n=i(2652),r=i(9306),a=i(8551),o=i(1767);s({target:"Iterator",proto:!0,real:!0},{some:function some(t){a(this);r(t);var e=o(this),i=0;return n(e,(function(e,s){if(t(e,i++))return s()}),{IS_RECORD:!0,INTERRUPTED:!0}).stopped}})},1689:(t,e,i)=>{var s=i(6518),n=i(4576),r=i(8745),a=i(7680),o=i(6043),l=i(9306),h=i(1103),c=n.Promise,d=!1;s({target:"Promise",stat:!0,forced:!c||!c.try||h((function(){c.try((function(t){d=8===t}),8)})).error||!d},{try:function(t){var e=arguments.length>1?a(arguments,1):[],i=o.f(this),s=h((function(){return r(l(t),void 0,e)}));(s.error?i.reject:i.resolve)(s.value);return i.promise}})},4628:(t,e,i)=>{var s=i(6518),n=i(6043);s({target:"Promise",stat:!0},{withResolvers:function withResolvers(){var t=n.f(this);return{promise:t.promise,resolve:t.resolve,reject:t.reject}}})},7642:(t,e,i)=>{var s=i(6518),n=i(3440);s({target:"Set",proto:!0,real:!0,forced:!i(4916)("difference")},{difference:n})},8004:(t,e,i)=>{var s=i(6518),n=i(9039),r=i(8750);s({target:"Set",proto:!0,real:!0,forced:!i(4916)("intersection")||n((function(){return"3,2"!==String(Array.from(new Set([1,2,3]).intersection(new Set([3,2]))))}))},{intersection:r})},3853:(t,e,i)=>{var s=i(6518),n=i(4449);s({target:"Set",proto:!0,real:!0,forced:!i(4916)("isDisjointFrom")},{isDisjointFrom:n})},5876:(t,e,i)=>{var s=i(6518),n=i(3838);s({target:"Set",proto:!0,real:!0,forced:!i(4916)("isSubsetOf")},{isSubsetOf:n})},2475:(t,e,i)=>{var s=i(6518),n=i(8527);s({target:"Set",proto:!0,real:!0,forced:!i(4916)("isSupersetOf")},{isSupersetOf:n})},5024:(t,e,i)=>{var s=i(6518),n=i(3650);s({target:"Set",proto:!0,real:!0,forced:!i(4916)("symmetricDifference")},{symmetricDifference:n})},1698:(t,e,i)=>{var s=i(6518),n=i(4204);s({target:"Set",proto:!0,real:!0,forced:!i(4916)("union")},{union:n})},7467:(t,e,i)=>{var s=i(7628),n=i(4644),r=n.aTypedArray,a=n.exportTypedArrayMethod,o=n.getTypedArrayConstructor;a("toReversed",(function toReversed(){return s(r(this),o(this))}))},4732:(t,e,i)=>{var s=i(4644),n=i(9504),r=i(9306),a=i(5370),o=s.aTypedArray,l=s.getTypedArrayConstructor,h=s.exportTypedArrayMethod,c=n(s.TypedArrayPrototype.sort);h("toSorted",(function toSorted(t){void 0!==t&&r(t);var e=o(this),i=a(l(e),e);return c(i,t)}))},9577:(t,e,i)=>{var s=i(9928),n=i(4644),r=i(1108),a=i(1291),o=i(5854),l=n.aTypedArray,h=n.getTypedArrayConstructor,c=n.exportTypedArrayMethod,d=!!function(){try{new Int8Array(1).with(2,{valueOf:function(){throw 8}})}catch(t){return 8===t}}();c("with",{with:function(t,e){var i=l(this),n=a(t),c=r(i)?o(e):+e;return s(i,h(i),n,c)}}.with,!d)},8992:(t,e,i)=>{i(8111)},4743:(t,e,i)=>{i(9314)},3215:(t,e,i)=>{i(1148)},4520:(t,e,i)=>{i(2489)},670:(t,e,i)=>{i(531)},1454:(t,e,i)=>{i(1701)},7550:(t,e,i)=>{i(3579)},8335:(t,e,i)=>{var s=i(6518),n=i(3724),r=i(4576),a=i(7751),o=i(9504),l=i(9565),h=i(4901),c=i(34),d=i(4376),u=i(9297),p=i(655),g=i(6198),f=i(4659),m=i(9039),b=i(8235),v=i(4495),y=r.JSON,w=r.Number,A=r.SyntaxError,x=y&&y.parse,_=a("Object","keys"),E=Object.getOwnPropertyDescriptor,S=o("".charAt),C=o("".slice),T=o(/./.exec),M=o([].push),P=/^\d$/,D=/^[1-9]$/,k=/^[\d-]$/,R=/^[\t\n\r ]$/,internalize=function(t,e,i,s){var n,r,a,o,h,p=t[e],f=s&&p===s.value,m=f&&"string"==typeof s.source?{source:s.source}:{};if(c(p)){var b=d(p),v=f?s.nodes:b?[]:{};if(b){n=v.length;a=g(p);for(o=0;o<a;o++)internalizeProperty(p,o,internalize(p,""+o,i,o<n?v[o]:void 0))}else{r=_(p);a=g(r);for(o=0;o<a;o++){h=r[o];internalizeProperty(p,h,internalize(p,h,i,u(v,h)?v[h]:void 0))}}}return l(i,t,e,p,m)},internalizeProperty=function(t,e,i){if(n){var s=E(t,e);if(s&&!s.configurable)return}void 0===i?delete t[e]:f(t,e,i)},Node=function(t,e,i,s){this.value=t;this.end=e;this.source=i;this.nodes=s},Context=function(t,e){this.source=t;this.index=e};Context.prototype={fork:function(t){return new Context(this.source,t)},parse:function(){var t=this.source,e=this.skip(R,this.index),i=this.fork(e),s=S(t,e);if(T(k,s))return i.number();switch(s){case"{":return i.object();case"[":return i.array();case'"':return i.string();case"t":return i.keyword(!0);case"f":return i.keyword(!1);case"n":return i.keyword(null)}throw new A('Unexpected character: "'+s+'" at: '+e)},node:function(t,e,i,s,n){return new Node(e,s,t?null:C(this.source,i,s),n)},object:function(){for(var t=this.source,e=this.index+1,i=!1,s={},n={};e<t.length;){e=this.until(['"',"}"],e);if("}"===S(t,e)&&!i){e++;break}var r=this.fork(e).string(),a=r.value;e=r.end;e=this.until([":"],e)+1;e=this.skip(R,e);r=this.fork(e).parse();f(n,a,r);f(s,a,r.value);e=this.until([",","}"],r.end);var o=S(t,e);if(","===o){i=!0;e++}else if("}"===o){e++;break}}return this.node(1,s,this.index,e,n)},array:function(){for(var t=this.source,e=this.index+1,i=!1,s=[],n=[];e<t.length;){e=this.skip(R,e);if("]"===S(t,e)&&!i){e++;break}var r=this.fork(e).parse();M(n,r);M(s,r.value);e=this.until([",","]"],r.end);if(","===S(t,e)){i=!0;e++}else if("]"===S(t,e)){e++;break}}return this.node(1,s,this.index,e,n)},string:function(){var t=this.index,e=b(this.source,this.index+1);return this.node(0,e.value,t,e.end)},number:function(){var t=this.source,e=this.index,i=e;"-"===S(t,i)&&i++;if("0"===S(t,i))i++;else{if(!T(D,S(t,i)))throw new A("Failed to parse number at: "+i);i=this.skip(P,i+1)}"."===S(t,i)&&(i=this.skip(P,i+1));if("e"===S(t,i)||"E"===S(t,i)){i++;"+"!==S(t,i)&&"-"!==S(t,i)||i++;if(i===(i=this.skip(P,i)))throw new A("Failed to parse number's exponent value at: "+i)}return this.node(0,w(C(t,e,i)),e,i)},keyword:function(t){var e=""+t,i=this.index,s=i+e.length;if(C(this.source,i,s)!==e)throw new A("Failed to parse value at: "+i);return this.node(0,t,i,s)},skip:function(t,e){for(var i=this.source;e<i.length&&T(t,S(i,e));e++);return e},until:function(t,e){e=this.skip(R,e);for(var i=S(this.source,e),s=0;s<t.length;s++)if(t[s]===i)return e;throw new A('Unexpected character: "'+i+'" at: '+e)}};var I=m((function(){var t,e="9007199254740993";x(e,(function(e,i,s){t=s.source}));return t!==e})),L=v&&!m((function(){return 1/x("-0 \t")!=-1/0}));s({target:"JSON",stat:!0,forced:I},{parse:function parse(t,e){return L&&!h(e)?x(t):function(t,e){t=p(t);var i=new Context(t,0,""),s=i.parse(),n=s.value,r=i.skip(R,s.end);if(r<t.length)throw new A('Unexpected extra character: "'+S(t,r)+'" after the parsed data at: '+r);return h(e)?internalize({"":n},"",e,s):n}(t,e)}})},5247:(t,e,i)=>{i(1689)},4979:(t,e,i)=>{var s=i(6518),n=i(4576),r=i(7751),a=i(6980),o=i(4913).f,l=i(9297),h=i(679),c=i(3167),d=i(2603),u=i(5002),p=i(8574),g=i(3724),f=i(6395),m="DOMException",b=r("Error"),v=r(m),y=function DOMException(){h(this,w);var t=arguments.length,e=d(t<1?void 0:arguments[0]),i=d(t<2?void 0:arguments[1],"Error"),s=new v(e,i),n=new b(e);n.name=m;o(s,"stack",a(1,p(n.stack,1)));c(s,this,y);return s},w=y.prototype=v.prototype,A="stack"in new b(m),x="stack"in new v(1,2),_=v&&g&&Object.getOwnPropertyDescriptor(n,m),E=!(!_||_.writable&&_.configurable),S=A&&!E&&!x;s({global:!0,constructor:!0,forced:f||S},{DOMException:S?y:v});var C=r(m),T=C.prototype;if(T.constructor!==C){f||o(T,"constructor",a(1,C));for(var M in u)if(l(u,M)){var P=u[M],D=P.s;l(C,D)||o(C,D,a(6,P.c))}}},4603:(t,e,i)=>{var s=i(6840),n=i(9504),r=i(655),a=i(2812),o=URLSearchParams,l=o.prototype,h=n(l.append),c=n(l.delete),d=n(l.forEach),u=n([].push),p=new o("a=1&a=2&b=3");p.delete("a",1);p.delete("b",void 0);p+""!="a=2"&&s(l,"delete",(function(t){var e=arguments.length,i=e<2?void 0:arguments[1];if(e&&void 0===i)return c(this,t);var s=[];d(this,(function(t,e){u(s,{key:e,value:t})}));a(e,1);for(var n,o=r(t),l=r(i),p=0,g=0,f=!1,m=s.length;p<m;){n=s[p++];if(f||n.key===o){f=!0;c(this,n.key)}else g++}for(;g<m;)(n=s[g++]).key===o&&n.value===l||h(this,n.key,n.value)}),{enumerable:!0,unsafe:!0})},7566:(t,e,i)=>{var s=i(6840),n=i(9504),r=i(655),a=i(2812),o=URLSearchParams,l=o.prototype,h=n(l.getAll),c=n(l.has),d=new o("a=1");!d.has("a",2)&&d.has("a",void 0)||s(l,"has",(function has(t){var e=arguments.length,i=e<2?void 0:arguments[1];if(e&&void 0===i)return c(this,t);var s=h(this,t);a(e,1);for(var n=r(i),o=0;o<s.length;)if(s[o++]===n)return!0;return!1}),{enumerable:!0,unsafe:!0})},8721:(t,e,i)=>{var s=i(3724),n=i(9504),r=i(2106),a=URLSearchParams.prototype,o=n(a.forEach);s&&!("size"in a)&&r(a,"size",{get:function size(){var t=0;o(this,(function(){t++}));return t},configurable:!0,enumerable:!0})}},e={};function __webpack_require__(i){var s=e[i];if(void 0!==s)return s.exports;var n=e[i]={exports:{}};t[i].call(n.exports,n,n.exports,__webpack_require__);return n.exports}__webpack_require__.d=(t,e)=>{for(var i in e)__webpack_require__.o(e,i)&&!__webpack_require__.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})};__webpack_require__.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var __webpack_exports__ = globalThis.pdfjsLib = {};__webpack_require__.d(__webpack_exports__,{AbortException:()=>AbortException,AnnotationEditorLayer:()=>AnnotationEditorLayer,AnnotationEditorParamsType:()=>m,AnnotationEditorType:()=>f,AnnotationEditorUIManager:()=>AnnotationEditorUIManager,AnnotationLayer:()=>AnnotationLayer,AnnotationMode:()=>g,ColorPicker:()=>ColorPicker,DOMSVGFactory:()=>DOMSVGFactory,DrawLayer:()=>DrawLayer,FeatureTest:()=>util_FeatureTest,GlobalWorkerOptions:()=>GlobalWorkerOptions,ImageKind:()=>E,InvalidPDFException:()=>InvalidPDFException,MissingPDFException:()=>MissingPDFException,OPS:()=>K,OutputScale:()=>OutputScale,PDFDataRangeTransport:()=>PDFDataRangeTransport,PDFDateString:()=>PDFDateString,PDFWorker:()=>PDFWorker,PasswordResponses:()=>Q,PermissionFlag:()=>b,PixelsPerInch:()=>PixelsPerInch,RenderingCancelledException:()=>RenderingCancelledException,TextLayer:()=>TextLayer,TouchManager:()=>TouchManager,UnexpectedResponseException:()=>UnexpectedResponseException,Util:()=>Util,VerbosityLevel:()=>Y,XfaLayer:()=>XfaLayer,build:()=>Ht,createValidAbsoluteUrl:()=>createValidAbsoluteUrl,fetchData:()=>fetchData,getDocument:()=>getDocument,getFilenameFromUrl:()=>getFilenameFromUrl,getPdfFilenameFromUrl:()=>getPdfFilenameFromUrl,getXfaPageViewport:()=>getXfaPageViewport,isDataScheme:()=>isDataScheme,isPdfFile:()=>isPdfFile,noContextMenu:()=>noContextMenu,normalizeUnicode:()=>normalizeUnicode,setLayerDimensions:()=>setLayerDimensions,shadow:()=>shadow,stopEvent:()=>stopEvent,version:()=>Bt});__webpack_require__(4114),__webpack_require__(6573),__webpack_require__(8100),__webpack_require__(7936),__webpack_require__(7467),__webpack_require__(4732),__webpack_require__(9577),__webpack_require__(5247),__webpack_require__(4979),__webpack_require__(4603),__webpack_require__(7566),__webpack_require__(8721);const i=!("object"!=typeof process||process+""!="[object process]"||process.versions.nw||process.versions.electron&&process.type&&"browser"!==process.type),s=[1,0,0,1,0,0],n=[.001,0,0,.001,0,0],r=1.35,a=1,o=2,l=4,h=16,c=32,d=64,u=128,p=256,g={DISABLE:0,ENABLE:1,ENABLE_FORMS:2,ENABLE_STORAGE:3},f={DISABLE:-1,NONE:0,FREETEXT:3,HIGHLIGHT:9,STAMP:13,INK:15},m={RESIZE:1,CREATE:2,FREETEXT_SIZE:11,FREETEXT_COLOR:12,FREETEXT_OPACITY:13,INK_COLOR:21,INK_THICKNESS:22,INK_OPACITY:23,HIGHLIGHT_COLOR:31,HIGHLIGHT_DEFAULT_COLOR:32,HIGHLIGHT_THICKNESS:33,HIGHLIGHT_FREE:34,HIGHLIGHT_SHOW_ALL:35,DRAW_STEP:41},b={PRINT:4,MODIFY_CONTENTS:8,COPY:16,MODIFY_ANNOTATIONS:32,FILL_INTERACTIVE_FORMS:256,COPY_FOR_ACCESSIBILITY:512,ASSEMBLE:1024,PRINT_HIGH_QUALITY:2048},v=0,y=1,w=2,A=3,x=3,_=4,E={GRAYSCALE_1BPP:1,RGB_24BPP:2,RGBA_32BPP:3},S=1,C=2,T=3,M=4,P=5,D=6,k=7,R=8,I=9,L=10,O=11,N=12,B=13,H=14,U=15,z=16,j=17,G=20,V=1,$=2,W=3,q=4,X=5,Y={ERRORS:0,WARNINGS:1,INFOS:5},K={dependency:1,setLineWidth:2,setLineCap:3,setLineJoin:4,setMiterLimit:5,setDash:6,setRenderingIntent:7,setFlatness:8,setGState:9,save:10,restore:11,transform:12,moveTo:13,lineTo:14,curveTo:15,curveTo2:16,curveTo3:17,closePath:18,rectangle:19,stroke:20,closeStroke:21,fill:22,eoFill:23,fillStroke:24,eoFillStroke:25,closeFillStroke:26,closeEOFillStroke:27,endPath:28,clip:29,eoClip:30,beginText:31,endText:32,setCharSpacing:33,setWordSpacing:34,setHScale:35,setLeading:36,setFont:37,setTextRenderingMode:38,setTextRise:39,moveText:40,setLeadingMoveText:41,setTextMatrix:42,nextLine:43,showText:44,showSpacedText:45,nextLineShowText:46,nextLineSetSpacingShowText:47,setCharWidth:48,setCharWidthAndBounds:49,setStrokeColorSpace:50,setFillColorSpace:51,setStrokeColor:52,setStrokeColorN:53,setFillColor:54,setFillColorN:55,setStrokeGray:56,setFillGray:57,setStrokeRGBColor:58,setFillRGBColor:59,setStrokeCMYKColor:60,setFillCMYKColor:61,shadingFill:62,beginInlineImage:63,beginImageData:64,endInlineImage:65,paintXObject:66,markPoint:67,markPointProps:68,beginMarkedContent:69,beginMarkedContentProps:70,endMarkedContent:71,beginCompat:72,endCompat:73,paintFormXObjectBegin:74,paintFormXObjectEnd:75,beginGroup:76,endGroup:77,beginAnnotation:80,endAnnotation:81,paintImageMaskXObject:83,paintImageMaskXObjectGroup:84,paintImageXObject:85,paintInlineImageXObject:86,paintInlineImageXObjectGroup:87,paintImageXObjectRepeat:88,paintImageMaskXObjectRepeat:89,paintSolidColorImageMask:90,constructPath:91,setStrokeTransparent:92,setFillTransparent:93},Q={NEED_PASSWORD:1,INCORRECT_PASSWORD:2};let J=Y.WARNINGS;function setVerbosityLevel(t){Number.isInteger(t)&&(J=t)}function getVerbosityLevel(){return J}function info(t){J>=Y.INFOS&&console.log(`Info: ${t}`)}function warn(t){J>=Y.WARNINGS&&console.log(`Warning: ${t}`)}function unreachable(t){throw new Error(t)}function assert(t,e){t||unreachable(e)}function createValidAbsoluteUrl(t,e=null,i=null){if(!t)return null;try{if(i&&"string"==typeof t){if(i.addDefaultProtocol&&t.startsWith("www.")){const e=t.match(/\./g);e?.length>=2&&(t=`http://${t}`)}if(i.tryConvertEncoding)try{t=function stringToUTF8String(t){return decodeURIComponent(escape(t))}(t)}catch{}}const s=e?new URL(t,e):new URL(t);if(function _isValidProtocol(t){switch(t?.protocol){case"http:":case"https:":case"ftp:":case"mailto:":case"tel:":return!0;default:return!1}}(s))return s}catch{}return null}function shadow(t,e,i,s=!1){Object.defineProperty(t,e,{value:i,enumerable:!s,configurable:!0,writable:!1});return i}const Z=function BaseExceptionClosure(){function BaseException(t,e){this.message=t;this.name=e}BaseException.prototype=new Error;BaseException.constructor=BaseException;return BaseException}();class PasswordException extends Z{constructor(t,e){super(t,"PasswordException");this.code=e}}class UnknownErrorException extends Z{constructor(t,e){super(t,"UnknownErrorException");this.details=e}}class InvalidPDFException extends Z{constructor(t){super(t,"InvalidPDFException")}}class MissingPDFException extends Z{constructor(t){super(t,"MissingPDFException")}}class UnexpectedResponseException extends Z{constructor(t,e){super(t,"UnexpectedResponseException");this.status=e}}class FormatError extends Z{constructor(t){super(t,"FormatError")}}class AbortException extends Z{constructor(t){super(t,"AbortException")}}function bytesToString(t){"object"==typeof t&&void 0!==t?.length||unreachable("Invalid argument for bytesToString");const e=t.length,i=8192;if(e<i)return String.fromCharCode.apply(null,t);const s=[];for(let n=0;n<e;n+=i){const r=Math.min(n+i,e),a=t.subarray(n,r);s.push(String.fromCharCode.apply(null,a))}return s.join("")}function stringToBytes(t){"string"!=typeof t&&unreachable("Invalid argument for stringToBytes");const e=t.length,i=new Uint8Array(e);for(let s=0;s<e;++s)i[s]=255&t.charCodeAt(s);return i}function objectFromMap(t){const e=Object.create(null);for(const[i,s]of t)e[i]=s;return e}class util_FeatureTest{static get isLittleEndian(){return shadow(this,"isLittleEndian",function isLittleEndian(){const t=new Uint8Array(4);t[0]=1;return 1===new Uint32Array(t.buffer,0,1)[0]}())}static get isEvalSupported(){return shadow(this,"isEvalSupported",function isEvalSupported(){try{new Function("");return!0}catch{return!1}}())}static get isOffscreenCanvasSupported(){return shadow(this,"isOffscreenCanvasSupported","undefined"!=typeof OffscreenCanvas)}static get isImageDecoderSupported(){return shadow(this,"isImageDecoderSupported","undefined"!=typeof ImageDecoder)}static get platform(){return"undefined"!=typeof navigator&&"string"==typeof navigator?.platform?shadow(this,"platform",{isMac:navigator.platform.includes("Mac"),isWindows:navigator.platform.includes("Win"),isFirefox:"string"==typeof navigator?.userAgent&&navigator.userAgent.includes("Firefox")}):shadow(this,"platform",{isMac:!1,isWindows:!1,isFirefox:!1})}static get isCSSRoundSupported(){return shadow(this,"isCSSRoundSupported",globalThis.CSS?.supports?.("width: round(1.5px, 1px)"))}}const tt=Array.from(Array(256).keys(),(t=>t.toString(16).padStart(2,"0")));class Util{static makeHexColor(t,e,i){return`#${tt[t]}${tt[e]}${tt[i]}`}static scaleMinMax(t,e){let i;if(t[0]){if(t[0]<0){i=e[0];e[0]=e[2];e[2]=i}e[0]*=t[0];e[2]*=t[0];if(t[3]<0){i=e[1];e[1]=e[3];e[3]=i}e[1]*=t[3];e[3]*=t[3]}else{i=e[0];e[0]=e[1];e[1]=i;i=e[2];e[2]=e[3];e[3]=i;if(t[1]<0){i=e[1];e[1]=e[3];e[3]=i}e[1]*=t[1];e[3]*=t[1];if(t[2]<0){i=e[0];e[0]=e[2];e[2]=i}e[0]*=t[2];e[2]*=t[2]}e[0]+=t[4];e[1]+=t[5];e[2]+=t[4];e[3]+=t[5]}static transform(t,e){return[t[0]*e[0]+t[2]*e[1],t[1]*e[0]+t[3]*e[1],t[0]*e[2]+t[2]*e[3],t[1]*e[2]+t[3]*e[3],t[0]*e[4]+t[2]*e[5]+t[4],t[1]*e[4]+t[3]*e[5]+t[5]]}static applyTransform(t,e){return[t[0]*e[0]+t[1]*e[2]+e[4],t[0]*e[1]+t[1]*e[3]+e[5]]}static applyInverseTransform(t,e){const i=e[0]*e[3]-e[1]*e[2];return[(t[0]*e[3]-t[1]*e[2]+e[2]*e[5]-e[4]*e[3])/i,(-t[0]*e[1]+t[1]*e[0]+e[4]*e[1]-e[5]*e[0])/i]}static getAxialAlignedBoundingBox(t,e){const i=this.applyTransform(t,e),s=this.applyTransform(t.slice(2,4),e),n=this.applyTransform([t[0],t[3]],e),r=this.applyTransform([t[2],t[1]],e);return[Math.min(i[0],s[0],n[0],r[0]),Math.min(i[1],s[1],n[1],r[1]),Math.max(i[0],s[0],n[0],r[0]),Math.max(i[1],s[1],n[1],r[1])]}static inverseTransform(t){const e=t[0]*t[3]-t[1]*t[2];return[t[3]/e,-t[1]/e,-t[2]/e,t[0]/e,(t[2]*t[5]-t[4]*t[3])/e,(t[4]*t[1]-t[5]*t[0])/e]}static singularValueDecompose2dScale(t){const e=[t[0],t[2],t[1],t[3]],i=t[0]*e[0]+t[1]*e[2],s=t[0]*e[1]+t[1]*e[3],n=t[2]*e[0]+t[3]*e[2],r=t[2]*e[1]+t[3]*e[3],a=(i+r)/2,o=Math.sqrt((i+r)**2-4*(i*r-n*s))/2,l=a+o||1,h=a-o||1;return[Math.sqrt(l),Math.sqrt(h)]}static normalizeRect(t){const e=t.slice(0);if(t[0]>t[2]){e[0]=t[2];e[2]=t[0]}if(t[1]>t[3]){e[1]=t[3];e[3]=t[1]}return e}static intersect(t,e){const i=Math.max(Math.min(t[0],t[2]),Math.min(e[0],e[2])),s=Math.min(Math.max(t[0],t[2]),Math.max(e[0],e[2]));if(i>s)return null;const n=Math.max(Math.min(t[1],t[3]),Math.min(e[1],e[3])),r=Math.min(Math.max(t[1],t[3]),Math.max(e[1],e[3]));return n>r?null:[i,n,s,r]}static#t(t,e,i,s,n,r,a,o,l,h){if(l<=0||l>=1)return;const c=1-l,d=l*l,u=d*l,p=c*(c*(c*t+3*l*e)+3*d*i)+u*s,g=c*(c*(c*n+3*l*r)+3*d*a)+u*o;h[0]=Math.min(h[0],p);h[1]=Math.min(h[1],g);h[2]=Math.max(h[2],p);h[3]=Math.max(h[3],g)}static#e(t,e,i,s,n,r,a,o,l,h,c,d){if(Math.abs(l)<1e-12){Math.abs(h)>=1e-12&&this.#t(t,e,i,s,n,r,a,o,-c/h,d);return}const u=h**2-4*c*l;if(u<0)return;const p=Math.sqrt(u),g=2*l;this.#t(t,e,i,s,n,r,a,o,(-h+p)/g,d);this.#t(t,e,i,s,n,r,a,o,(-h-p)/g,d)}static bezierBoundingBox(t,e,i,s,n,r,a,o,l){if(l){l[0]=Math.min(l[0],t,a);l[1]=Math.min(l[1],e,o);l[2]=Math.max(l[2],t,a);l[3]=Math.max(l[3],e,o)}else l=[Math.min(t,a),Math.min(e,o),Math.max(t,a),Math.max(e,o)];this.#e(t,i,n,a,e,s,r,o,3*(3*(i-n)-t+a),6*(t-2*i+n),3*(i-t),l);this.#e(t,i,n,a,e,s,r,o,3*(3*(s-r)-e+o),6*(e-2*s+r),3*(s-e),l);return l}}let et=null,it=null;function normalizeUnicode(t){if(!et){et=/([\u00a0\u00b5\u037e\u0eb3\u2000-\u200a\u202f\u2126\ufb00-\ufb04\ufb06\ufb20-\ufb36\ufb38-\ufb3c\ufb3e\ufb40-\ufb41\ufb43-\ufb44\ufb46-\ufba1\ufba4-\ufba9\ufbae-\ufbb1\ufbd3-\ufbdc\ufbde-\ufbe7\ufbea-\ufbf8\ufbfc-\ufbfd\ufc00-\ufc5d\ufc64-\ufcf1\ufcf5-\ufd3d\ufd88\ufdf4\ufdfa-\ufdfb\ufe71\ufe77\ufe79\ufe7b\ufe7d]+)|(\ufb05+)/gu;it=new Map([["ﬅ","ſt"]])}return t.replaceAll(et,((t,e,i)=>e?e.normalize("NFKC"):it.get(i)))}const st="pdfjs_internal_id_";__webpack_require__(4628),__webpack_require__(7642),__webpack_require__(8004),__webpack_require__(3853),__webpack_require__(5876),__webpack_require__(2475),__webpack_require__(5024),__webpack_require__(1698),__webpack_require__(1454),__webpack_require__(8992),__webpack_require__(4743),__webpack_require__(3215),__webpack_require__(7550),__webpack_require__(8335);const nt="http://www.w3.org/2000/svg";class PixelsPerInch{static CSS=96;static PDF=72;static PDF_TO_CSS_UNITS=this.CSS/this.PDF}async function fetchData(t,e="text"){if(isValidFetchUrl(t,document.baseURI)){const i=await fetch(t);if(!i.ok)throw new Error(i.statusText);switch(e){case"arraybuffer":return i.arrayBuffer();case"blob":return i.blob();case"json":return i.json()}return i.text()}return new Promise(((i,s)=>{const n=new XMLHttpRequest;n.open("GET",t,!0);n.responseType=e;n.onreadystatechange=()=>{if(n.readyState===XMLHttpRequest.DONE)if(200!==n.status&&0!==n.status)s(new Error(n.statusText));else{switch(e){case"arraybuffer":case"blob":case"json":i(n.response);return}i(n.responseText)}};n.send(null)}))}class PageViewport{constructor({viewBox:t,userUnit:e,scale:i,rotation:s,offsetX:n=0,offsetY:r=0,dontFlip:a=!1}){this.viewBox=t;this.userUnit=e;this.scale=i;this.rotation=s;this.offsetX=n;this.offsetY=r;i*=e;const o=(t[2]+t[0])/2,l=(t[3]+t[1])/2;let h,c,d,u,p,g,f,m;(s%=360)<0&&(s+=360);switch(s){case 180:h=-1;c=0;d=0;u=1;break;case 90:h=0;c=1;d=1;u=0;break;case 270:h=0;c=-1;d=-1;u=0;break;case 0:h=1;c=0;d=0;u=-1;break;default:throw new Error("PageViewport: Invalid rotation, must be a multiple of 90 degrees.")}if(a){d=-d;u=-u}if(0===h){p=Math.abs(l-t[1])*i+n;g=Math.abs(o-t[0])*i+r;f=(t[3]-t[1])*i;m=(t[2]-t[0])*i}else{p=Math.abs(o-t[0])*i+n;g=Math.abs(l-t[1])*i+r;f=(t[2]-t[0])*i;m=(t[3]-t[1])*i}this.transform=[h*i,c*i,d*i,u*i,p-h*i*o-d*i*l,g-c*i*o-u*i*l];this.width=f;this.height=m}get rawDims(){const{userUnit:t,viewBox:e}=this,i=e.map((e=>e*t));return shadow(this,"rawDims",{pageWidth:i[2]-i[0],pageHeight:i[3]-i[1],pageX:i[0],pageY:i[1]})}clone({scale:t=this.scale,rotation:e=this.rotation,offsetX:i=this.offsetX,offsetY:s=this.offsetY,dontFlip:n=!1}={}){return new PageViewport({viewBox:this.viewBox.slice(),userUnit:this.userUnit,scale:t,rotation:e,offsetX:i,offsetY:s,dontFlip:n})}convertToViewportPoint(t,e){return Util.applyTransform([t,e],this.transform)}convertToViewportRectangle(t){const e=Util.applyTransform([t[0],t[1]],this.transform),i=Util.applyTransform([t[2],t[3]],this.transform);return[e[0],e[1],i[0],i[1]]}convertToPdfPoint(t,e){return Util.applyInverseTransform([t,e],this.transform)}}class RenderingCancelledException extends Z{constructor(t,e=0){super(t,"RenderingCancelledException");this.extraDelay=e}}function isDataScheme(t){const e=t.length;let i=0;for(;i<e&&""===t[i].trim();)i++;return"data:"===t.substring(i,i+5).toLowerCase()}function isPdfFile(t){return"string"==typeof t&&/\.pdf$/i.test(t)}function getFilenameFromUrl(t){[t]=t.split(/[#?]/,1);return t.substring(t.lastIndexOf("/")+1)}function getPdfFilenameFromUrl(t,e="document.pdf"){if("string"!=typeof t)return e;if(isDataScheme(t)){warn('getPdfFilenameFromUrl: ignore "data:"-URL for performance reasons.');return e}const i=/[^/?#=]+\.pdf\b(?!.*\.pdf\b)/i,s=/^(?:(?:[^:]+:)?\/\/[^/]+)?([^?#]*)(\?[^#]*)?(#.*)?$/.exec(t);let n=i.exec(s[1])||i.exec(s[2])||i.exec(s[3]);if(n){n=n[0];if(n.includes("%"))try{n=i.exec(decodeURIComponent(n))[0]}catch{}}return n||e}class StatTimer{started=Object.create(null);times=[];time(t){t in this.started&&warn(`Timer is already running for ${t}`);this.started[t]=Date.now()}timeEnd(t){t in this.started||warn(`Timer has not been started for ${t}`);this.times.push({name:t,start:this.started[t],end:Date.now()});delete this.started[t]}toString(){const t=[];let e=0;for(const{name:t}of this.times)e=Math.max(t.length,e);for(const{name:i,start:s,end:n}of this.times)t.push(`${i.padEnd(e)} ${n-s}ms\n`);return t.join("")}}function isValidFetchUrl(t,e){try{const{protocol:i}=e?new URL(t,e):new URL(t);return"http:"===i||"https:"===i}catch{return!1}}function noContextMenu(t){t.preventDefault()}function stopEvent(t){t.preventDefault();t.stopPropagation()}class PDFDateString{static#i;static toDateObject(t){if(!t||"string"!=typeof t)return null;this.#i||=new RegExp("^D:(\\d{4})(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?([Z|+|-])?(\\d{2})?'?(\\d{2})?'?");const e=this.#i.exec(t);if(!e)return null;const i=parseInt(e[1],10);let s=parseInt(e[2],10);s=s>=1&&s<=12?s-1:0;let n=parseInt(e[3],10);n=n>=1&&n<=31?n:1;let r=parseInt(e[4],10);r=r>=0&&r<=23?r:0;let a=parseInt(e[5],10);a=a>=0&&a<=59?a:0;let o=parseInt(e[6],10);o=o>=0&&o<=59?o:0;const l=e[7]||"Z";let h=parseInt(e[8],10);h=h>=0&&h<=23?h:0;let c=parseInt(e[9],10)||0;c=c>=0&&c<=59?c:0;if("-"===l){r+=h;a+=c}else if("+"===l){r-=h;a-=c}return new Date(Date.UTC(i,s,n,r,a,o))}}function getXfaPageViewport(t,{scale:e=1,rotation:i=0}){const{width:s,height:n}=t.attributes.style,r=[0,0,parseInt(s),parseInt(n)];return new PageViewport({viewBox:r,userUnit:1,scale:e,rotation:i})}function getRGB(t){if(t.startsWith("#")){const e=parseInt(t.slice(1),16);return[(16711680&e)>>16,(65280&e)>>8,255&e]}if(t.startsWith("rgb("))return t.slice(4,-1).split(",").map((t=>parseInt(t)));if(t.startsWith("rgba("))return t.slice(5,-1).split(",").map((t=>parseInt(t))).slice(0,3);warn(`Not a valid color format: "${t}"`);return[0,0,0]}function getCurrentTransform(t){const{a:e,b:i,c:s,d:n,e:r,f:a}=t.getTransform();return[e,i,s,n,r,a]}function getCurrentTransformInverse(t){const{a:e,b:i,c:s,d:n,e:r,f:a}=t.getTransform().invertSelf();return[e,i,s,n,r,a]}function setLayerDimensions(t,e,i=!1,s=!0){if(e instanceof PageViewport){const{pageWidth:s,pageHeight:n}=e.rawDims,{style:r}=t,a=util_FeatureTest.isCSSRoundSupported,o=`var(--scale-factor) * ${s}px`,l=`var(--scale-factor) * ${n}px`,h=a?`round(down, ${o}, var(--scale-round-x, 1px))`:`calc(${o})`,c=a?`round(down, ${l}, var(--scale-round-y, 1px))`:`calc(${l})`;if(i&&e.rotation%180!=0){r.width=c;r.height=h}else{r.width=h;r.height=c}}s&&t.setAttribute("data-main-rotation",e.rotation)}class OutputScale{constructor(){const t=window.devicePixelRatio||1;this.sx=t;this.sy=t}get scaled(){return 1!==this.sx||1!==this.sy}get symmetric(){return this.sx===this.sy}}class EditorToolbar{#s=null;#n=null;#r;#a=null;#o=null;static#l=null;constructor(t){this.#r=t;EditorToolbar.#l||=Object.freeze({freetext:"pdfjs-editor-remove-freetext-button",highlight:"pdfjs-editor-remove-highlight-button",ink:"pdfjs-editor-remove-ink-button",stamp:"pdfjs-editor-remove-stamp-button"})}render(){const t=this.#s=document.createElement("div");t.classList.add("editToolbar","hidden");t.setAttribute("role","toolbar");const e=this.#r._uiManager._signal;t.addEventListener("contextmenu",noContextMenu,{signal:e});t.addEventListener("pointerdown",EditorToolbar.#h,{signal:e});const i=this.#a=document.createElement("div");i.className="buttons";t.append(i);const s=this.#r.toolbarPosition;if(s){const{style:e}=t,i="ltr"===this.#r._uiManager.direction?1-s[0]:s[0];e.insetInlineEnd=100*i+"%";e.top=`calc(${100*s[1]}% + var(--editor-toolbar-vert-offset))`}this.#c();return t}get div(){return this.#s}static#h(t){t.stopPropagation()}#d(t){this.#r._focusEventsAllowed=!1;stopEvent(t)}#u(t){this.#r._focusEventsAllowed=!0;stopEvent(t)}#p(t){const e=this.#r._uiManager._signal;t.addEventListener("focusin",this.#d.bind(this),{capture:!0,signal:e});t.addEventListener("focusout",this.#u.bind(this),{capture:!0,signal:e});t.addEventListener("contextmenu",noContextMenu,{signal:e})}hide(){this.#s.classList.add("hidden");this.#n?.hideDropdown()}show(){this.#s.classList.remove("hidden");this.#o?.shown()}#c(){const{editorType:t,_uiManager:e}=this.#r,i=document.createElement("button");i.className="delete";i.tabIndex=0;i.setAttribute("data-l10n-id",EditorToolbar.#l[t]);this.#p(i);i.addEventListener("click",(t=>{e.delete()}),{signal:e._signal});this.#a.append(i)}get#g(){const t=document.createElement("div");t.className="divider";return t}async addAltText(t){const e=await t.render();this.#p(e);this.#a.prepend(e,this.#g);this.#o=t}addColorPicker(t){this.#n=t;const e=t.renderButton();this.#p(e);this.#a.prepend(e,this.#g)}remove(){this.#s.remove();this.#n?.destroy();this.#n=null}}class HighlightToolbar{#a=null;#s=null;#f;constructor(t){this.#f=t}#m(){const t=this.#s=document.createElement("div");t.className="editToolbar";t.setAttribute("role","toolbar");t.addEventListener("contextmenu",noContextMenu,{signal:this.#f._signal});const e=this.#a=document.createElement("div");e.className="buttons";t.append(e);this.#b();return t}#v(t,e){let i=0,s=0;for(const n of t){const t=n.y+n.height;if(t<i)continue;const r=n.x+(e?n.width:0);if(t>i){s=r;i=t}else e?r>s&&(s=r):r<s&&(s=r)}return[e?1-s:s,i]}show(t,e,i){const[s,n]=this.#v(e,i),{style:r}=this.#s||=this.#m();t.append(this.#s);r.insetInlineEnd=100*s+"%";r.top=`calc(${100*n}% + var(--editor-toolbar-vert-offset))`}hide(){this.#s.remove()}#b(){const t=document.createElement("button");t.className="highlightButton";t.tabIndex=0;t.setAttribute("data-l10n-id","pdfjs-highlight-floating-button1");const e=document.createElement("span");t.append(e);e.className="visuallyHidden";e.setAttribute("data-l10n-id","pdfjs-highlight-floating-button-label");const i=this.#f._signal;t.addEventListener("contextmenu",noContextMenu,{signal:i});t.addEventListener("click",(()=>{this.#f.highlightSelection("floating_button")}),{signal:i});this.#a.append(t)}}function bindEvents(t,e,i){for(const s of i)e.addEventListener(s,t[s].bind(t))}class IdManager{#y=0;get id(){return"pdfjs_internal_editor_"+this.#y++}}class ImageManager{#w=function getUuid(){if("function"==typeof crypto.randomUUID)return crypto.randomUUID();const t=new Uint8Array(32);crypto.getRandomValues(t);return bytesToString(t)}();#y=0;#A=null;static get _isSVGFittingCanvas(){const t=new OffscreenCanvas(1,3).getContext("2d",{willReadFrequently:!0}),e=new Image;e.src='data:image/svg+xml;charset=UTF-8,<svg viewBox="0 0 1 1" width="1" height="1" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="1" style="fill:red;"/></svg>';return shadow(this,"_isSVGFittingCanvas",e.decode().then((()=>{t.drawImage(e,0,0,1,1,0,0,1,3);return 0===new Uint32Array(t.getImageData(0,0,1,1).data.buffer)[0]})))}async#x(t,e){this.#A||=new Map;let i=this.#A.get(t);if(null===i)return null;if(i?.bitmap){i.refCounter+=1;return i}try{i||={bitmap:null,id:`image_${this.#w}_${this.#y++}`,refCounter:0,isSvg:!1};let t;if("string"==typeof e){i.url=e;t=await fetchData(e,"blob")}else e instanceof File?t=i.file=e:e instanceof Blob&&(t=e);if("image/svg+xml"===t.type){const e=ImageManager._isSVGFittingCanvas,s=new FileReader,n=new Image,r=new Promise(((t,r)=>{n.onload=()=>{i.bitmap=n;i.isSvg=!0;t()};s.onload=async()=>{const t=i.svgUrl=s.result;n.src=await e?`${t}#svgView(preserveAspectRatio(none))`:t};n.onerror=s.onerror=r}));s.readAsDataURL(t);await r}else i.bitmap=await createImageBitmap(t);i.refCounter=1}catch(t){warn(t);i=null}this.#A.set(t,i);i&&this.#A.set(i.id,i);return i}async getFromFile(t){const{lastModified:e,name:i,size:s,type:n}=t;return this.#x(`${e}_${i}_${s}_${n}`,t)}async getFromUrl(t){return this.#x(t,t)}async getFromBlob(t,e){const i=await e;return this.#x(t,i)}async getFromId(t){this.#A||=new Map;const e=this.#A.get(t);if(!e)return null;if(e.bitmap){e.refCounter+=1;return e}if(e.file)return this.getFromFile(e.file);if(e.blobPromise){const{blobPromise:t}=e;delete e.blobPromise;return this.getFromBlob(e.id,t)}return this.getFromUrl(e.url)}getFromCanvas(t,e){this.#A||=new Map;let i=this.#A.get(t);if(i?.bitmap){i.refCounter+=1;return i}const s=new OffscreenCanvas(e.width,e.height);s.getContext("2d").drawImage(e,0,0);i={bitmap:s.transferToImageBitmap(),id:`image_${this.#w}_${this.#y++}`,refCounter:1,isSvg:!1};this.#A.set(t,i);this.#A.set(i.id,i);return i}getSvgUrl(t){const e=this.#A.get(t);return e?.isSvg?e.svgUrl:null}deleteId(t){this.#A||=new Map;const e=this.#A.get(t);if(!e)return;e.refCounter-=1;if(0!==e.refCounter)return;const{bitmap:i}=e;if(!e.url&&!e.file){const t=new OffscreenCanvas(i.width,i.height);t.getContext("bitmaprenderer").transferFromImageBitmap(i);e.blobPromise=t.convertToBlob()}i.close?.();e.bitmap=null}isValidId(t){return t.startsWith(`image_${this.#w}_`)}}class CommandManager{#_=[];#E=!1;#S;#C=-1;constructor(t=128){this.#S=t}add({cmd:t,undo:e,post:i,mustExec:s,type:n=NaN,overwriteIfSameType:r=!1,keepUndo:a=!1}){s&&t();if(this.#E)return;const o={cmd:t,undo:e,post:i,type:n};if(-1===this.#C){this.#_.length>0&&(this.#_.length=0);this.#C=0;this.#_.push(o);return}if(r&&this.#_[this.#C].type===n){a&&(o.undo=this.#_[this.#C].undo);this.#_[this.#C]=o;return}const l=this.#C+1;if(l===this.#S)this.#_.splice(0,1);else{this.#C=l;l<this.#_.length&&this.#_.splice(l)}this.#_.push(o)}undo(){if(-1===this.#C)return;this.#E=!0;const{undo:t,post:e}=this.#_[this.#C];t();e?.();this.#E=!1;this.#C-=1}redo(){if(this.#C<this.#_.length-1){this.#C+=1;this.#E=!0;const{cmd:t,post:e}=this.#_[this.#C];t();e?.();this.#E=!1}}hasSomethingToUndo(){return-1!==this.#C}hasSomethingToRedo(){return this.#C<this.#_.length-1}cleanType(t){if(-1!==this.#C){for(let e=this.#C;e>=0;e--)if(this.#_[e].type!==t){this.#_.splice(e+1,this.#C-e);this.#C=e;return}this.#_.length=0;this.#C=-1}}destroy(){this.#_=null}}class KeyboardManager{constructor(t){this.buffer=[];this.callbacks=new Map;this.allKeys=new Set;const{isMac:e}=util_FeatureTest.platform;for(const[i,s,n={}]of t)for(const t of i){const i=t.startsWith("mac+");if(e&&i){this.callbacks.set(t.slice(4),{callback:s,options:n});this.allKeys.add(t.split("+").at(-1))}else if(!e&&!i){this.callbacks.set(t,{callback:s,options:n});this.allKeys.add(t.split("+").at(-1))}}}#T(t){t.altKey&&this.buffer.push("alt");t.ctrlKey&&this.buffer.push("ctrl");t.metaKey&&this.buffer.push("meta");t.shiftKey&&this.buffer.push("shift");this.buffer.push(t.key);const e=this.buffer.join("+");this.buffer.length=0;return e}exec(t,e){if(!this.allKeys.has(e.key))return;const i=this.callbacks.get(this.#T(e));if(!i)return;const{callback:s,options:{bubbles:n=!1,args:r=[],checker:a=null}}=i;if(!a||a(t,e)){s.bind(t,...r,e)();n||stopEvent(e)}}}class ColorManager{static _colorsMapping=new Map([["CanvasText",[0,0,0]],["Canvas",[255,255,255]]]);get _colors(){const t=new Map([["CanvasText",null],["Canvas",null]]);!function getColorValues(t){const e=document.createElement("span");e.style.visibility="hidden";document.body.append(e);for(const i of t.keys()){e.style.color=i;const s=window.getComputedStyle(e).color;t.set(i,getRGB(s))}e.remove()}(t);return shadow(this,"_colors",t)}convert(t){const e=getRGB(t);if(!window.matchMedia("(forced-colors: active)").matches)return e;for(const[t,i]of this._colors)if(i.every(((t,i)=>t===e[i])))return ColorManager._colorsMapping.get(t);return e}getHexCode(t){const e=this._colors.get(t);return e?Util.makeHexColor(...e):t}}class AnnotationEditorUIManager{#M=new AbortController;#P=null;#D=new Map;#k=new Map;#R=null;#I=null;#F=null;#L=new CommandManager;#O=null;#N=null;#B=0;#H=new Set;#U=null;#z=null;#j=new Set;_editorUndoBar=null;#G=!1;#V=!1;#$=!1;#W=null;#q=null;#X=null;#Y=null;#K=!1;#Q=null;#J=new IdManager;#Z=!1;#tt=!1;#et=null;#it=null;#st=null;#nt=null;#rt=f.NONE;#at=new Set;#ot=null;#lt=null;#ht=null;#ct={isEditing:!1,isEmpty:!0,hasSomethingToUndo:!1,hasSomethingToRedo:!1,hasSelectedEditor:!1,hasSelectedText:!1};#dt=[0,0];#ut=null;#pt=null;#gt=null;#ft=null;static TRANSLATE_SMALL=1;static TRANSLATE_BIG=10;static get _keyboardManager(){const t=AnnotationEditorUIManager.prototype,arrowChecker=t=>t.#pt.contains(document.activeElement)&&"BUTTON"!==document.activeElement.tagName&&t.hasSomethingToControl(),textInputChecker=(t,{target:e})=>{if(e instanceof HTMLInputElement){const{type:t}=e;return"text"!==t&&"number"!==t}return!0},e=this.TRANSLATE_SMALL,i=this.TRANSLATE_BIG;return shadow(this,"_keyboardManager",new KeyboardManager([[["ctrl+a","mac+meta+a"],t.selectAll,{checker:textInputChecker}],[["ctrl+z","mac+meta+z"],t.undo,{checker:textInputChecker}],[["ctrl+y","ctrl+shift+z","mac+meta+shift+z","ctrl+shift+Z","mac+meta+shift+Z"],t.redo,{checker:textInputChecker}],[["Backspace","alt+Backspace","ctrl+Backspace","shift+Backspace","mac+Backspace","mac+alt+Backspace","mac+ctrl+Backspace","Delete","ctrl+Delete","shift+Delete","mac+Delete"],t.delete,{checker:textInputChecker}],[["Enter","mac+Enter"],t.addNewEditorFromKeyboard,{checker:(t,{target:e})=>!(e instanceof HTMLButtonElement)&&t.#pt.contains(e)&&!t.isEnterHandled}],[[" ","mac+ "],t.addNewEditorFromKeyboard,{checker:(t,{target:e})=>!(e instanceof HTMLButtonElement)&&t.#pt.contains(document.activeElement)}],[["Escape","mac+Escape"],t.unselectAll],[["ArrowLeft","mac+ArrowLeft"],t.translateSelectedEditors,{args:[-e,0],checker:arrowChecker}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],t.translateSelectedEditors,{args:[-i,0],checker:arrowChecker}],[["ArrowRight","mac+ArrowRight"],t.translateSelectedEditors,{args:[e,0],checker:arrowChecker}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],t.translateSelectedEditors,{args:[i,0],checker:arrowChecker}],[["ArrowUp","mac+ArrowUp"],t.translateSelectedEditors,{args:[0,-e],checker:arrowChecker}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],t.translateSelectedEditors,{args:[0,-i],checker:arrowChecker}],[["ArrowDown","mac+ArrowDown"],t.translateSelectedEditors,{args:[0,e],checker:arrowChecker}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],t.translateSelectedEditors,{args:[0,i],checker:arrowChecker}]]))}constructor(t,e,i,s,n,r,a,o,l,h,c,d,u){const p=this._signal=this.#M.signal;this.#pt=t;this.#gt=e;this.#R=i;this._eventBus=s;s._on("editingaction",this.onEditingAction.bind(this),{signal:p});s._on("pagechanging",this.onPageChanging.bind(this),{signal:p});s._on("scalechanging",this.onScaleChanging.bind(this),{signal:p});s._on("rotationchanging",this.onRotationChanging.bind(this),{signal:p});s._on("setpreference",this.onSetPreference.bind(this),{signal:p});s._on("switchannotationeditorparams",(t=>this.updateParams(t.type,t.value)),{signal:p});this.#mt();this.#bt();this.#vt();this.#I=n.annotationStorage;this.#W=n.filterFactory;this.#lt=r;this.#Y=a||null;this.#G=o;this.#V=l;this.#$=h;this.#nt=c||null;this.viewParameters={realScale:PixelsPerInch.PDF_TO_CSS_UNITS,rotation:0};this.isShiftKeyDown=!1;this._editorUndoBar=d||null;this._supportsPinchToZoom=!1!==u}destroy(){this.#ft?.resolve();this.#ft=null;this.#M?.abort();this.#M=null;this._signal=null;for(const t of this.#k.values())t.destroy();this.#k.clear();this.#D.clear();this.#j.clear();this.#P=null;this.#at.clear();this.#L.destroy();this.#R?.destroy();this.#Q?.hide();this.#Q=null;if(this.#q){clearTimeout(this.#q);this.#q=null}if(this.#ut){clearTimeout(this.#ut);this.#ut=null}this._editorUndoBar?.destroy()}combinedSignal(t){return AbortSignal.any([this._signal,t.signal])}get mlManager(){return this.#nt}get useNewAltTextFlow(){return this.#V}get useNewAltTextWhenAddingImage(){return this.#$}get hcmFilter(){return shadow(this,"hcmFilter",this.#lt?this.#W.addHCMFilter(this.#lt.foreground,this.#lt.background):"none")}get direction(){return shadow(this,"direction",getComputedStyle(this.#pt).direction)}get highlightColors(){return shadow(this,"highlightColors",this.#Y?new Map(this.#Y.split(",").map((t=>t.split("=").map((t=>t.trim()))))):null)}get highlightColorNames(){return shadow(this,"highlightColorNames",this.highlightColors?new Map(Array.from(this.highlightColors,(t=>t.reverse()))):null)}setCurrentDrawingSession(t){if(t){this.unselectAll();this.disableUserSelect(!0)}else this.disableUserSelect(!1);this.#N=t}setMainHighlightColorPicker(t){this.#st=t}editAltText(t,e=!1){this.#R?.editAltText(this,t,e)}switchToMode(t,e){this._eventBus.on("annotationeditormodechanged",e,{once:!0,signal:this._signal});this._eventBus.dispatch("showannotationeditorui",{source:this,mode:t})}setPreference(t,e){this._eventBus.dispatch("setpreference",{source:this,name:t,value:e})}onSetPreference({name:t,value:e}){if("enableNewAltTextWhenAddingImage"===t)this.#$=e}onPageChanging({pageNumber:t}){this.#B=t-1}focusMainContainer(){this.#pt.focus()}findParent(t,e){for(const i of this.#k.values()){const{x:s,y:n,width:r,height:a}=i.div.getBoundingClientRect();if(t>=s&&t<=s+r&&e>=n&&e<=n+a)return i}return null}disableUserSelect(t=!1){this.#gt.classList.toggle("noUserSelect",t)}addShouldRescale(t){this.#j.add(t)}removeShouldRescale(t){this.#j.delete(t)}onScaleChanging({scale:t}){this.commitOrRemove();this.viewParameters.realScale=t*PixelsPerInch.PDF_TO_CSS_UNITS;for(const t of this.#j)t.onScaleChanging();this.#N?.onScaleChanging()}onRotationChanging({pagesRotation:t}){this.commitOrRemove();this.viewParameters.rotation=t}#yt({anchorNode:t}){return t.nodeType===Node.TEXT_NODE?t.parentElement:t}#wt(t){const{currentLayer:e}=this;if(e.hasTextLayer(t))return e;for(const e of this.#k.values())if(e.hasTextLayer(t))return e;return null}highlightSelection(t=""){const e=document.getSelection();if(!e||e.isCollapsed)return;const{anchorNode:i,anchorOffset:s,focusNode:n,focusOffset:r}=e,a=e.toString(),o=this.#yt(e).closest(".textLayer"),l=this.getSelectionBoxes(o);if(!l)return;e.empty();const h=this.#wt(o),c=this.#rt===f.NONE,callback=()=>{h?.createAndAddNewEditor({x:0,y:0},!1,{methodOfCreation:t,boxes:l,anchorNode:i,anchorOffset:s,focusNode:n,focusOffset:r,text:a});c&&this.showAllEditors("highlight",!0,!0)};c?this.switchToMode(f.HIGHLIGHT,callback):callback()}#At(){const t=document.getSelection();if(!t||t.isCollapsed)return;const e=this.#yt(t).closest(".textLayer"),i=this.getSelectionBoxes(e);if(i){this.#Q||=new HighlightToolbar(this);this.#Q.show(e,i,"ltr"===this.direction)}}addToAnnotationStorage(t){t.isEmpty()||!this.#I||this.#I.has(t.id)||this.#I.setValue(t.id,t)}#xt(){const t=document.getSelection();if(!t||t.isCollapsed){if(this.#ot){this.#Q?.hide();this.#ot=null;this.#_t({hasSelectedText:!1})}return}const{anchorNode:e}=t;if(e===this.#ot)return;const i=this.#yt(t).closest(".textLayer");if(i){this.#Q?.hide();this.#ot=e;this.#_t({hasSelectedText:!0});if(this.#rt===f.HIGHLIGHT||this.#rt===f.NONE){this.#rt===f.HIGHLIGHT&&this.showAllEditors("highlight",!0,!0);this.#K=this.isShiftKeyDown;if(!this.isShiftKeyDown){const t=this.#rt===f.HIGHLIGHT?this.#wt(i):null;t?.toggleDrawing();const e=new AbortController,s=this.combinedSignal(e),pointerup=i=>{if("pointerup"!==i.type||0===i.button){e.abort();t?.toggleDrawing(!0);"pointerup"===i.type&&this.#Et("main_toolbar")}};window.addEventListener("pointerup",pointerup,{signal:s});window.addEventListener("blur",pointerup,{signal:s})}}}else if(this.#ot){this.#Q?.hide();this.#ot=null;this.#_t({hasSelectedText:!1})}}#Et(t=""){this.#rt===f.HIGHLIGHT?this.highlightSelection(t):this.#G&&this.#At()}#mt(){document.addEventListener("selectionchange",this.#xt.bind(this),{signal:this._signal})}#St(){if(this.#X)return;this.#X=new AbortController;const t=this.combinedSignal(this.#X);window.addEventListener("focus",this.focus.bind(this),{signal:t});window.addEventListener("blur",this.blur.bind(this),{signal:t})}#Ct(){this.#X?.abort();this.#X=null}blur(){this.isShiftKeyDown=!1;if(this.#K){this.#K=!1;this.#Et("main_toolbar")}if(!this.hasSelection)return;const{activeElement:t}=document;for(const e of this.#at)if(e.div.contains(t)){this.#it=[e,t];e._focusEventsAllowed=!1;break}}focus(){if(!this.#it)return;const[t,e]=this.#it;this.#it=null;e.addEventListener("focusin",(()=>{t._focusEventsAllowed=!0}),{once:!0,signal:this._signal});e.focus()}#vt(){if(this.#et)return;this.#et=new AbortController;const t=this.combinedSignal(this.#et);window.addEventListener("keydown",this.keydown.bind(this),{signal:t});window.addEventListener("keyup",this.keyup.bind(this),{signal:t})}#Tt(){this.#et?.abort();this.#et=null}#Mt(){if(this.#O)return;this.#O=new AbortController;const t=this.combinedSignal(this.#O);document.addEventListener("copy",this.copy.bind(this),{signal:t});document.addEventListener("cut",this.cut.bind(this),{signal:t});document.addEventListener("paste",this.paste.bind(this),{signal:t})}#Pt(){this.#O?.abort();this.#O=null}#bt(){const t=this._signal;document.addEventListener("dragover",this.dragOver.bind(this),{signal:t});document.addEventListener("drop",this.drop.bind(this),{signal:t})}addEditListeners(){this.#vt();this.#Mt()}removeEditListeners(){this.#Tt();this.#Pt()}dragOver(t){for(const{type:e}of t.dataTransfer.items)for(const i of this.#z)if(i.isHandlingMimeForPasting(e)){t.dataTransfer.dropEffect="copy";t.preventDefault();return}}drop(t){for(const e of t.dataTransfer.items)for(const i of this.#z)if(i.isHandlingMimeForPasting(e.type)){i.paste(e,this.currentLayer);t.preventDefault();return}}copy(t){t.preventDefault();this.#P?.commitOrRemove();if(!this.hasSelection)return;const e=[];for(const t of this.#at){const i=t.serialize(!0);i&&e.push(i)}0!==e.length&&t.clipboardData.setData("application/pdfjs",JSON.stringify(e))}cut(t){this.copy(t);this.delete()}async paste(t){t.preventDefault();const{clipboardData:e}=t;for(const t of e.items)for(const e of this.#z)if(e.isHandlingMimeForPasting(t.type)){e.paste(t,this.currentLayer);return}let i=e.getData("application/pdfjs");if(!i)return;try{i=JSON.parse(i)}catch(t){warn(`paste: "${t.message}".`);return}if(!Array.isArray(i))return;this.unselectAll();const s=this.currentLayer;try{const t=[];for(const e of i){const i=await s.deserialize(e);if(!i)return;t.push(i)}const cmd=()=>{for(const e of t)this.#Dt(e);this.#kt(t)},undo=()=>{for(const e of t)e.remove()};this.addCommands({cmd,undo,mustExec:!0})}catch(t){warn(`paste: "${t.message}".`)}}keydown(t){this.isShiftKeyDown||"Shift"!==t.key||(this.isShiftKeyDown=!0);this.#rt===f.NONE||this.isEditorHandlingKeyboard||AnnotationEditorUIManager._keyboardManager.exec(this,t)}keyup(t){if(this.isShiftKeyDown&&"Shift"===t.key){this.isShiftKeyDown=!1;if(this.#K){this.#K=!1;this.#Et("main_toolbar")}}}onEditingAction({name:t}){switch(t){case"undo":case"redo":case"delete":case"selectAll":this[t]();break;case"highlightSelection":this.highlightSelection("context_menu")}}#_t(t){if(Object.entries(t).some((([t,e])=>this.#ct[t]!==e))){this._eventBus.dispatch("annotationeditorstateschanged",{source:this,details:Object.assign(this.#ct,t)});this.#rt===f.HIGHLIGHT&&!1===t.hasSelectedEditor&&this.#Rt([[m.HIGHLIGHT_FREE,!0]])}}#Rt(t){this._eventBus.dispatch("annotationeditorparamschanged",{source:this,details:t})}setEditingState(t){if(t){this.#St();this.#Mt();this.#_t({isEditing:this.#rt!==f.NONE,isEmpty:this.#It(),hasSomethingToUndo:this.#L.hasSomethingToUndo(),hasSomethingToRedo:this.#L.hasSomethingToRedo(),hasSelectedEditor:!1})}else{this.#Ct();this.#Pt();this.#_t({isEditing:!1});this.disableUserSelect(!1)}}registerEditorTypes(t){if(!this.#z){this.#z=t;for(const t of this.#z)this.#Rt(t.defaultPropertiesToUpdate)}}getId(){return this.#J.id}get currentLayer(){return this.#k.get(this.#B)}getLayer(t){return this.#k.get(t)}get currentPageIndex(){return this.#B}addLayer(t){this.#k.set(t.pageIndex,t);this.#Z?t.enable():t.disable()}removeLayer(t){this.#k.delete(t.pageIndex)}async updateMode(t,e=null,i=!1){if(this.#rt!==t){if(this.#ft){await this.#ft.promise;if(!this.#ft)return}this.#ft=Promise.withResolvers();this.#rt=t;if(t!==f.NONE){this.setEditingState(!0);await this.#Ft();this.unselectAll();for(const e of this.#k.values())e.updateMode(t);if(e){for(const t of this.#D.values())if(t.annotationElementId===e){this.setSelected(t);t.enterInEditMode()}else t.unselect();this.#ft.resolve()}else{i&&this.addNewEditorFromKeyboard();this.#ft.resolve()}}else{this.setEditingState(!1);this.#Lt();this._editorUndoBar?.hide();this.#ft.resolve()}}}addNewEditorFromKeyboard(){this.currentLayer.canCreateNewEmptyEditor()&&this.currentLayer.addNewEditor()}updateToolbar(t){t!==this.#rt&&this._eventBus.dispatch("switchannotationeditormode",{source:this,mode:t})}updateParams(t,e){if(this.#z){switch(t){case m.CREATE:this.currentLayer.addNewEditor();return;case m.HIGHLIGHT_DEFAULT_COLOR:this.#st?.updateColor(e);break;case m.HIGHLIGHT_SHOW_ALL:this._eventBus.dispatch("reporttelemetry",{source:this,details:{type:"editing",data:{type:"highlight",action:"toggle_visibility"}}});(this.#ht||=new Map).set(t,e);this.showAllEditors("highlight",e)}for(const i of this.#at)i.updateParams(t,e);for(const i of this.#z)i.updateDefaultParams(t,e)}}showAllEditors(t,e,i=!1){for(const i of this.#D.values())i.editorType===t&&i.show(e);(this.#ht?.get(m.HIGHLIGHT_SHOW_ALL)??!0)!==e&&this.#Rt([[m.HIGHLIGHT_SHOW_ALL,e]])}enableWaiting(t=!1){if(this.#tt!==t){this.#tt=t;for(const e of this.#k.values()){t?e.disableClick():e.enableClick();e.div.classList.toggle("waiting",t)}}}async#Ft(){if(!this.#Z){this.#Z=!0;const t=[];for(const e of this.#k.values())t.push(e.enable());await Promise.all(t);for(const t of this.#D.values())t.enable()}}#Lt(){this.unselectAll();if(this.#Z){this.#Z=!1;for(const t of this.#k.values())t.disable();for(const t of this.#D.values())t.disable()}}getEditors(t){const e=[];for(const i of this.#D.values())i.pageIndex===t&&e.push(i);return e}getEditor(t){return this.#D.get(t)}addEditor(t){this.#D.set(t.id,t)}removeEditor(t){if(t.div.contains(document.activeElement)){this.#q&&clearTimeout(this.#q);this.#q=setTimeout((()=>{this.focusMainContainer();this.#q=null}),0)}this.#D.delete(t.id);this.unselect(t);t.annotationElementId&&this.#H.has(t.annotationElementId)||this.#I?.remove(t.id)}addDeletedAnnotationElement(t){this.#H.add(t.annotationElementId);this.addChangedExistingAnnotation(t);t.deleted=!0}isDeletedAnnotationElement(t){return this.#H.has(t)}removeDeletedAnnotationElement(t){this.#H.delete(t.annotationElementId);this.removeChangedExistingAnnotation(t);t.deleted=!1}#Dt(t){const e=this.#k.get(t.pageIndex);if(e)e.addOrRebuild(t);else{this.addEditor(t);this.addToAnnotationStorage(t)}}setActiveEditor(t){if(this.#P!==t){this.#P=t;t&&this.#Rt(t.propertiesToUpdate)}}get#Ot(){let t=null;for(t of this.#at);return t}updateUI(t){this.#Ot===t&&this.#Rt(t.propertiesToUpdate)}updateUIForDefaultProperties(t){this.#Rt(t.defaultPropertiesToUpdate)}toggleSelected(t){if(this.#at.has(t)){this.#at.delete(t);t.unselect();this.#_t({hasSelectedEditor:this.hasSelection})}else{this.#at.add(t);t.select();this.#Rt(t.propertiesToUpdate);this.#_t({hasSelectedEditor:!0})}}setSelected(t){this.#N?.commitOrRemove();for(const e of this.#at)e!==t&&e.unselect();this.#at.clear();this.#at.add(t);t.select();this.#Rt(t.propertiesToUpdate);this.#_t({hasSelectedEditor:!0})}isSelected(t){return this.#at.has(t)}get firstSelectedEditor(){return this.#at.values().next().value}unselect(t){t.unselect();this.#at.delete(t);this.#_t({hasSelectedEditor:this.hasSelection})}get hasSelection(){return 0!==this.#at.size}get isEnterHandled(){return 1===this.#at.size&&this.firstSelectedEditor.isEnterHandled}undo(){this.#L.undo();this.#_t({hasSomethingToUndo:this.#L.hasSomethingToUndo(),hasSomethingToRedo:!0,isEmpty:this.#It()});this._editorUndoBar?.hide()}redo(){this.#L.redo();this.#_t({hasSomethingToUndo:!0,hasSomethingToRedo:this.#L.hasSomethingToRedo(),isEmpty:this.#It()})}addCommands(t){this.#L.add(t);this.#_t({hasSomethingToUndo:!0,hasSomethingToRedo:!1,isEmpty:this.#It()})}cleanUndoStack(t){this.#L.cleanType(t)}#It(){if(0===this.#D.size)return!0;if(1===this.#D.size)for(const t of this.#D.values())return t.isEmpty();return!1}delete(){this.commitOrRemove();const t=this.currentLayer?.endDrawingSession(!0);if(!this.hasSelection&&!t)return;const e=t?[t]:[...this.#at],undo=()=>{for(const t of e)this.#Dt(t)};this.addCommands({cmd:()=>{this._editorUndoBar?.show(undo,1===e.length?e[0].editorType:e.length);for(const t of e)t.remove()},undo,mustExec:!0})}commitOrRemove(){this.#P?.commitOrRemove()}hasSomethingToControl(){return this.#P||this.hasSelection}#kt(t){for(const t of this.#at)t.unselect();this.#at.clear();for(const e of t)if(!e.isEmpty()){this.#at.add(e);e.select()}this.#_t({hasSelectedEditor:this.hasSelection})}selectAll(){for(const t of this.#at)t.commit();this.#kt(this.#D.values())}unselectAll(){if(this.#P){this.#P.commitOrRemove();if(this.#rt!==f.NONE)return}if(!this.#N?.commitOrRemove()&&this.hasSelection){for(const t of this.#at)t.unselect();this.#at.clear();this.#_t({hasSelectedEditor:!1})}}translateSelectedEditors(t,e,i=!1){i||this.commitOrRemove();if(!this.hasSelection)return;this.#dt[0]+=t;this.#dt[1]+=e;const[s,n]=this.#dt,r=[...this.#at];this.#ut&&clearTimeout(this.#ut);this.#ut=setTimeout((()=>{this.#ut=null;this.#dt[0]=this.#dt[1]=0;this.addCommands({cmd:()=>{for(const t of r)this.#D.has(t.id)&&t.translateInPage(s,n)},undo:()=>{for(const t of r)this.#D.has(t.id)&&t.translateInPage(-s,-n)},mustExec:!1})}),1e3);for(const i of r)i.translateInPage(t,e)}setUpDragSession(){if(this.hasSelection){this.disableUserSelect(!0);this.#U=new Map;for(const t of this.#at)this.#U.set(t,{savedX:t.x,savedY:t.y,savedPageIndex:t.pageIndex,newX:0,newY:0,newPageIndex:-1})}}endDragSession(){if(!this.#U)return!1;this.disableUserSelect(!1);const t=this.#U;this.#U=null;let e=!1;for(const[{x:i,y:s,pageIndex:n},r]of t){r.newX=i;r.newY=s;r.newPageIndex=n;e||=i!==r.savedX||s!==r.savedY||n!==r.savedPageIndex}if(!e)return!1;const move=(t,e,i,s)=>{if(this.#D.has(t.id)){const n=this.#k.get(s);if(n)t._setParentAndPosition(n,e,i);else{t.pageIndex=s;t.x=e;t.y=i}}};this.addCommands({cmd:()=>{for(const[e,{newX:i,newY:s,newPageIndex:n}]of t)move(e,i,s,n)},undo:()=>{for(const[e,{savedX:i,savedY:s,savedPageIndex:n}]of t)move(e,i,s,n)},mustExec:!0});return!0}dragSelectedEditors(t,e){if(this.#U)for(const i of this.#U.keys())i.drag(t,e)}rebuild(t){if(null===t.parent){const e=this.getLayer(t.pageIndex);if(e){e.changeParent(t);e.addOrRebuild(t)}else{this.addEditor(t);this.addToAnnotationStorage(t);t.rebuild()}}else t.parent.addOrRebuild(t)}get isEditorHandlingKeyboard(){return this.getActive()?.shouldGetKeyboardEvents()||1===this.#at.size&&this.firstSelectedEditor.shouldGetKeyboardEvents()}isActive(t){return this.#P===t}getActive(){return this.#P}getMode(){return this.#rt}get imageManager(){return shadow(this,"imageManager",new ImageManager)}getSelectionBoxes(t){if(!t)return null;const e=document.getSelection();for(let i=0,s=e.rangeCount;i<s;i++)if(!t.contains(e.getRangeAt(i).commonAncestorContainer))return null;const{x:i,y:s,width:n,height:r}=t.getBoundingClientRect();let a;switch(t.getAttribute("data-main-rotation")){case"90":a=(t,e,a,o)=>({x:(e-s)/r,y:1-(t+a-i)/n,width:o/r,height:a/n});break;case"180":a=(t,e,a,o)=>({x:1-(t+a-i)/n,y:1-(e+o-s)/r,width:a/n,height:o/r});break;case"270":a=(t,e,a,o)=>({x:1-(e+o-s)/r,y:(t-i)/n,width:o/r,height:a/n});break;default:a=(t,e,a,o)=>({x:(t-i)/n,y:(e-s)/r,width:a/n,height:o/r})}const o=[];for(let t=0,i=e.rangeCount;t<i;t++){const i=e.getRangeAt(t);if(!i.collapsed)for(const{x:t,y:e,width:s,height:n}of i.getClientRects())0!==s&&0!==n&&o.push(a(t,e,s,n))}return 0===o.length?null:o}addChangedExistingAnnotation({annotationElementId:t,id:e}){(this.#F||=new Map).set(t,e)}removeChangedExistingAnnotation({annotationElementId:t}){this.#F?.delete(t)}renderAnnotationElement(t){const e=this.#F?.get(t.data.id);if(!e)return;const i=this.#I.getRawValue(e);i&&(this.#rt!==f.NONE||i.hasBeenModified)&&i.renderAnnotationElement(t)}}class AltText{#o=null;#Nt=!1;#Bt=null;#Ht=null;#Ut=null;#zt=null;#jt=!1;#Gt=null;#r=null;#Vt=null;#$t=null;#Wt=!1;static#qt=null;static _l10n=null;constructor(t){this.#r=t;this.#Wt=t._uiManager.useNewAltTextFlow;AltText.#qt||=Object.freeze({added:"pdfjs-editor-new-alt-text-added-button","added-label":"pdfjs-editor-new-alt-text-added-button-label",missing:"pdfjs-editor-new-alt-text-missing-button","missing-label":"pdfjs-editor-new-alt-text-missing-button-label",review:"pdfjs-editor-new-alt-text-to-review-button","review-label":"pdfjs-editor-new-alt-text-to-review-button-label"})}static initialize(t){AltText._l10n??=t}async render(){const t=this.#Bt=document.createElement("button");t.className="altText";t.tabIndex="0";const e=this.#Ht=document.createElement("span");t.append(e);if(this.#Wt){t.classList.add("new");t.setAttribute("data-l10n-id",AltText.#qt.missing);e.setAttribute("data-l10n-id",AltText.#qt["missing-label"])}else{t.setAttribute("data-l10n-id","pdfjs-editor-alt-text-button");e.setAttribute("data-l10n-id","pdfjs-editor-alt-text-button-label")}const i=this.#r._uiManager._signal;t.addEventListener("contextmenu",noContextMenu,{signal:i});t.addEventListener("pointerdown",(t=>t.stopPropagation()),{signal:i});const onClick=t=>{t.preventDefault();this.#r._uiManager.editAltText(this.#r);this.#Wt&&this.#r._reportTelemetry({action:"pdfjs.image.alt_text.image_status_label_clicked",data:{label:this.#Xt}})};t.addEventListener("click",onClick,{capture:!0,signal:i});t.addEventListener("keydown",(e=>{if(e.target===t&&"Enter"===e.key){this.#jt=!0;onClick(e)}}),{signal:i});await this.#Yt();return t}get#Xt(){return(this.#o?"added":null===this.#o&&this.guessedText&&"review")||"missing"}finish(){if(this.#Bt){this.#Bt.focus({focusVisible:this.#jt});this.#jt=!1}}isEmpty(){return this.#Wt?null===this.#o:!this.#o&&!this.#Nt}hasData(){return this.#Wt?null!==this.#o||!!this.#Vt:this.isEmpty()}get guessedText(){return this.#Vt}async setGuessedText(t){if(null===this.#o){this.#Vt=t;this.#$t=await AltText._l10n.get("pdfjs-editor-new-alt-text-generated-alt-text-with-disclaimer",{generatedAltText:t});this.#Yt()}}toggleAltTextBadge(t=!1){if(this.#Wt&&!this.#o){if(!this.#Gt){const t=this.#Gt=document.createElement("div");t.className="noAltTextBadge";this.#r.div.append(t)}this.#Gt.classList.toggle("hidden",!t)}else{this.#Gt?.remove();this.#Gt=null}}serialize(t){let e=this.#o;t||this.#Vt!==e||(e=this.#$t);return{altText:e,decorative:this.#Nt,guessedText:this.#Vt,textWithDisclaimer:this.#$t}}get data(){return{altText:this.#o,decorative:this.#Nt}}set data({altText:t,decorative:e,guessedText:i,textWithDisclaimer:s,cancel:n=!1}){if(i){this.#Vt=i;this.#$t=s}if(this.#o!==t||this.#Nt!==e){if(!n){this.#o=t;this.#Nt=e}this.#Yt()}}toggle(t=!1){if(this.#Bt){if(!t&&this.#zt){clearTimeout(this.#zt);this.#zt=null}this.#Bt.disabled=!t}}shown(){this.#r._reportTelemetry({action:"pdfjs.image.alt_text.image_status_label_displayed",data:{label:this.#Xt}})}destroy(){this.#Bt?.remove();this.#Bt=null;this.#Ht=null;this.#Ut=null;this.#Gt?.remove();this.#Gt=null}async#Yt(){const t=this.#Bt;if(!t)return;if(this.#Wt){t.classList.toggle("done",!!this.#o);t.setAttribute("data-l10n-id",AltText.#qt[this.#Xt]);this.#Ht?.setAttribute("data-l10n-id",AltText.#qt[`${this.#Xt}-label`]);if(!this.#o){this.#Ut?.remove();return}}else{if(!this.#o&&!this.#Nt){t.classList.remove("done");this.#Ut?.remove();return}t.classList.add("done");t.setAttribute("data-l10n-id","pdfjs-editor-alt-text-edit-button")}let e=this.#Ut;if(!e){this.#Ut=e=document.createElement("span");e.className="tooltip";e.setAttribute("role","tooltip");e.id=`alt-text-tooltip-${this.#r.id}`;const i=100,s=this.#r._uiManager._signal;s.addEventListener("abort",(()=>{clearTimeout(this.#zt);this.#zt=null}),{once:!0});t.addEventListener("mouseenter",(()=>{this.#zt=setTimeout((()=>{this.#zt=null;this.#Ut.classList.add("show");this.#r._reportTelemetry({action:"alt_text_tooltip"})}),i)}),{signal:s});t.addEventListener("mouseleave",(()=>{if(this.#zt){clearTimeout(this.#zt);this.#zt=null}this.#Ut?.classList.remove("show")}),{signal:s})}if(this.#Nt)e.setAttribute("data-l10n-id","pdfjs-editor-alt-text-decorative-tooltip");else{e.removeAttribute("data-l10n-id");e.textContent=this.#o}e.parentNode||t.append(e);const i=this.#r.getImageForAltText();i?.setAttribute("aria-describedby",e.id)}}class TouchManager{#pt;#Kt=!1;#Qt=null;#Jt;#Zt;#te;#ee;#ie;#se=null;#ne;#re=null;constructor({container:t,isPinchingDisabled:e=null,isPinchingStopped:i=null,onPinchStart:s=null,onPinching:n=null,onPinchEnd:r=null,signal:a}){this.#pt=t;this.#Qt=i;this.#Jt=e;this.#Zt=s;this.#te=n;this.#ee=r;this.#ne=new AbortController;this.#ie=AbortSignal.any([a,this.#ne.signal]);t.addEventListener("touchstart",this.#ae.bind(this),{passive:!1,signal:this.#ie})}get MIN_TOUCH_DISTANCE_TO_PINCH(){return shadow(this,"MIN_TOUCH_DISTANCE_TO_PINCH",35/(window.devicePixelRatio||1))}#ae(t){if(this.#Jt?.()||t.touches.length<2)return;if(!this.#re){this.#re=new AbortController;const t=AbortSignal.any([this.#ie,this.#re.signal]),e=this.#pt,i={signal:t,passive:!1};e.addEventListener("touchmove",this.#oe.bind(this),i);e.addEventListener("touchend",this.#le.bind(this),i);e.addEventListener("touchcancel",this.#le.bind(this),i);this.#Zt?.()}stopEvent(t);if(2!==t.touches.length||this.#Qt?.()){this.#se=null;return}let[e,i]=t.touches;e.identifier>i.identifier&&([e,i]=[i,e]);this.#se={touch0X:e.screenX,touch0Y:e.screenY,touch1X:i.screenX,touch1Y:i.screenY}}#oe(t){if(!this.#se||2!==t.touches.length)return;let[e,i]=t.touches;e.identifier>i.identifier&&([e,i]=[i,e]);const{screenX:s,screenY:n}=e,{screenX:r,screenY:a}=i,o=this.#se,{touch0X:l,touch0Y:h,touch1X:c,touch1Y:d}=o,u=c-l,p=d-h,g=r-s,f=a-n,m=Math.hypot(g,f)||1,b=Math.hypot(u,p)||1;if(!this.#Kt&&Math.abs(b-m)<=TouchManager.MIN_TOUCH_DISTANCE_TO_PINCH)return;o.touch0X=s;o.touch0Y=n;o.touch1X=r;o.touch1Y=a;t.preventDefault();if(!this.#Kt){this.#Kt=!0;return}const v=[(s+r)/2,(n+a)/2];this.#te?.(v,b,m)}#le(t){this.#re.abort();this.#re=null;this.#ee?.();if(this.#se){t.preventDefault();this.#se=null;this.#Kt=!1}}destroy(){this.#ne?.abort();this.#ne=null}}class AnnotationEditor{#he=null;#ce=null;#o=null;#de=!1;#ue=null;#pe="";#ge=!1;#fe=null;#me=null;#be=null;#ve=null;#ye="";#we=!1;#Ae=null;#xe=!1;#_e=!1;#Ee=!1;#Se=null;#Ce=0;#Te=0;#Me=null;#Pe=null;_editToolbar=null;_initialOptions=Object.create(null);_initialData=null;_isVisible=!0;_uiManager=null;_focusEventsAllowed=!0;static _l10n=null;static _l10nResizer=null;#De=!1;#ke=AnnotationEditor._zIndex++;static _borderLineWidth=-1;static _colorManager=new ColorManager;static _zIndex=1;static _telemetryTimeout=1e3;static get _resizerKeyboardManager(){const t=AnnotationEditor.prototype._resizeWithKeyboard,e=AnnotationEditorUIManager.TRANSLATE_SMALL,i=AnnotationEditorUIManager.TRANSLATE_BIG;return shadow(this,"_resizerKeyboardManager",new KeyboardManager([[["ArrowLeft","mac+ArrowLeft"],t,{args:[-e,0]}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],t,{args:[-i,0]}],[["ArrowRight","mac+ArrowRight"],t,{args:[e,0]}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],t,{args:[i,0]}],[["ArrowUp","mac+ArrowUp"],t,{args:[0,-e]}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],t,{args:[0,-i]}],[["ArrowDown","mac+ArrowDown"],t,{args:[0,e]}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],t,{args:[0,i]}],[["Escape","mac+Escape"],AnnotationEditor.prototype._stopResizingWithKeyboard]]))}constructor(t){this.parent=t.parent;this.id=t.id;this.width=this.height=null;this.pageIndex=t.parent.pageIndex;this.name=t.name;this.div=null;this._uiManager=t.uiManager;this.annotationElementId=null;this._willKeepAspectRatio=!1;this._initialOptions.isCentered=t.isCentered;this._structTreeParentId=null;const{rotation:e,rawDims:{pageWidth:i,pageHeight:s,pageX:n,pageY:r}}=this.parent.viewport;this.rotation=e;this.pageRotation=(360+e-this._uiManager.viewParameters.rotation)%360;this.pageDimensions=[i,s];this.pageTranslation=[n,r];const[a,o]=this.parentDimensions;this.x=t.x/a;this.y=t.y/o;this.isAttachedToDOM=!1;this.deleted=!1}get editorType(){return Object.getPrototypeOf(this).constructor._type}static get isDrawer(){return!1}static get _defaultLineColor(){return shadow(this,"_defaultLineColor",this._colorManager.getHexCode("CanvasText"))}static deleteAnnotationElement(t){const e=new FakeEditor({id:t.parent.getNextId(),parent:t.parent,uiManager:t._uiManager});e.annotationElementId=t.annotationElementId;e.deleted=!0;e._uiManager.addToAnnotationStorage(e)}static initialize(t,e){AnnotationEditor._l10n??=t;AnnotationEditor._l10nResizer||=Object.freeze({topLeft:"pdfjs-editor-resizer-top-left",topMiddle:"pdfjs-editor-resizer-top-middle",topRight:"pdfjs-editor-resizer-top-right",middleRight:"pdfjs-editor-resizer-middle-right",bottomRight:"pdfjs-editor-resizer-bottom-right",bottomMiddle:"pdfjs-editor-resizer-bottom-middle",bottomLeft:"pdfjs-editor-resizer-bottom-left",middleLeft:"pdfjs-editor-resizer-middle-left"});if(-1!==AnnotationEditor._borderLineWidth)return;const i=getComputedStyle(document.documentElement);AnnotationEditor._borderLineWidth=parseFloat(i.getPropertyValue("--outline-width"))||0}static updateDefaultParams(t,e){}static get defaultPropertiesToUpdate(){return[]}static isHandlingMimeForPasting(t){return!1}static paste(t,e){unreachable("Not implemented")}get propertiesToUpdate(){return[]}get _isDraggable(){return this.#De}set _isDraggable(t){this.#De=t;this.div?.classList.toggle("draggable",t)}get isEnterHandled(){return!0}center(){const[t,e]=this.pageDimensions;switch(this.parentRotation){case 90:this.x-=this.height*e/(2*t);this.y+=this.width*t/(2*e);break;case 180:this.x+=this.width/2;this.y+=this.height/2;break;case 270:this.x+=this.height*e/(2*t);this.y-=this.width*t/(2*e);break;default:this.x-=this.width/2;this.y-=this.height/2}this.fixAndSetPosition()}addCommands(t){this._uiManager.addCommands(t)}get currentLayer(){return this._uiManager.currentLayer}setInBackground(){this.div.style.zIndex=0}setInForeground(){this.div.style.zIndex=this.#ke}setParent(t){if(null!==t){this.pageIndex=t.pageIndex;this.pageDimensions=t.pageDimensions}else this.#Re();this.parent=t}focusin(t){this._focusEventsAllowed&&(this.#we?this.#we=!1:this.parent.setSelected(this))}focusout(t){if(!this._focusEventsAllowed)return;if(!this.isAttachedToDOM)return;const e=t.relatedTarget;if(!e?.closest(`#${this.id}`)){t.preventDefault();this.parent?.isMultipleSelection||this.commitOrRemove()}}commitOrRemove(){this.isEmpty()?this.remove():this.commit()}commit(){this.addToAnnotationStorage()}addToAnnotationStorage(){this._uiManager.addToAnnotationStorage(this)}setAt(t,e,i,s){const[n,r]=this.parentDimensions;[i,s]=this.screenToPageTranslation(i,s);this.x=(t+i)/n;this.y=(e+s)/r;this.fixAndSetPosition()}#Ie([t,e],i,s){[i,s]=this.screenToPageTranslation(i,s);this.x+=i/t;this.y+=s/e;this._onTranslating(this.x,this.y);this.fixAndSetPosition()}translate(t,e){this.#Ie(this.parentDimensions,t,e)}translateInPage(t,e){this.#Ae||=[this.x,this.y,this.width,this.height];this.#Ie(this.pageDimensions,t,e);this.div.scrollIntoView({block:"nearest"})}drag(t,e){this.#Ae||=[this.x,this.y,this.width,this.height];const{div:i,parentDimensions:[s,n]}=this;this.x+=t/s;this.y+=e/n;if(this.parent&&(this.x<0||this.x>1||this.y<0||this.y>1)){const{x:t,y:e}=this.div.getBoundingClientRect();if(this.parent.findNewParent(this,t,e)){this.x-=Math.floor(this.x);this.y-=Math.floor(this.y)}}let{x:r,y:a}=this;const[o,l]=this.getBaseTranslation();r+=o;a+=l;const{style:h}=i;h.left=`${(100*r).toFixed(2)}%`;h.top=`${(100*a).toFixed(2)}%`;this._onTranslating(r,a);i.scrollIntoView({block:"nearest"})}_onTranslating(t,e){}_onTranslated(t,e){}get _hasBeenMoved(){return!!this.#Ae&&(this.#Ae[0]!==this.x||this.#Ae[1]!==this.y)}get _hasBeenResized(){return!!this.#Ae&&(this.#Ae[2]!==this.width||this.#Ae[3]!==this.height)}getBaseTranslation(){const[t,e]=this.parentDimensions,{_borderLineWidth:i}=AnnotationEditor,s=i/t,n=i/e;switch(this.rotation){case 90:return[-s,n];case 180:return[s,n];case 270:return[s,-n];default:return[-s,-n]}}get _mustFixPosition(){return!0}fixAndSetPosition(t=this.rotation){const{div:{style:e},pageDimensions:[i,s]}=this;let{x:n,y:r,width:a,height:o}=this;a*=i;o*=s;n*=i;r*=s;if(this._mustFixPosition)switch(t){case 0:n=Math.max(0,Math.min(i-a,n));r=Math.max(0,Math.min(s-o,r));break;case 90:n=Math.max(0,Math.min(i-o,n));r=Math.min(s,Math.max(a,r));break;case 180:n=Math.min(i,Math.max(a,n));r=Math.min(s,Math.max(o,r));break;case 270:n=Math.min(i,Math.max(o,n));r=Math.max(0,Math.min(s-a,r))}this.x=n/=i;this.y=r/=s;const[l,h]=this.getBaseTranslation();n+=l;r+=h;e.left=`${(100*n).toFixed(2)}%`;e.top=`${(100*r).toFixed(2)}%`;this.moveInDOM()}static#Fe(t,e,i){switch(i){case 90:return[e,-t];case 180:return[-t,-e];case 270:return[-e,t];default:return[t,e]}}screenToPageTranslation(t,e){return AnnotationEditor.#Fe(t,e,this.parentRotation)}pageTranslationToScreen(t,e){return AnnotationEditor.#Fe(t,e,360-this.parentRotation)}#Le(t){switch(t){case 90:{const[t,e]=this.pageDimensions;return[0,-t/e,e/t,0]}case 180:return[-1,0,0,-1];case 270:{const[t,e]=this.pageDimensions;return[0,t/e,-e/t,0]}default:return[1,0,0,1]}}get parentScale(){return this._uiManager.viewParameters.realScale}get parentRotation(){return(this._uiManager.viewParameters.rotation+this.pageRotation)%360}get parentDimensions(){const{parentScale:t,pageDimensions:[e,i]}=this;return[e*t,i*t]}setDims(t,e){const[i,s]=this.parentDimensions,{style:n}=this.div;n.width=`${(100*t/i).toFixed(2)}%`;this.#ge||(n.height=`${(100*e/s).toFixed(2)}%`)}fixDims(){const{style:t}=this.div,{height:e,width:i}=t,s=i.endsWith("%"),n=!this.#ge&&e.endsWith("%");if(s&&n)return;const[r,a]=this.parentDimensions;s||(t.width=`${(100*parseFloat(i)/r).toFixed(2)}%`);this.#ge||n||(t.height=`${(100*parseFloat(e)/a).toFixed(2)}%`)}getInitialTranslation(){return[0,0]}#Oe(){if(this.#fe)return;this.#fe=document.createElement("div");this.#fe.classList.add("resizers");const t=this._willKeepAspectRatio?["topLeft","topRight","bottomRight","bottomLeft"]:["topLeft","topMiddle","topRight","middleRight","bottomRight","bottomMiddle","bottomLeft","middleLeft"],e=this._uiManager._signal;for(const i of t){const t=document.createElement("div");this.#fe.append(t);t.classList.add("resizer",i);t.setAttribute("data-resizer-name",i);t.addEventListener("pointerdown",this.#Ne.bind(this,i),{signal:e});t.addEventListener("contextmenu",noContextMenu,{signal:e});t.tabIndex=-1}this.div.prepend(this.#fe)}#Ne(t,e){e.preventDefault();const{isMac:i}=util_FeatureTest.platform;if(0!==e.button||e.ctrlKey&&i)return;this.#o?.toggle(!1);const s=this._isDraggable;this._isDraggable=!1;this.#me=[e.screenX,e.screenY];const n=new AbortController,r=this._uiManager.combinedSignal(n);this.parent.togglePointerEvents(!1);window.addEventListener("pointermove",this.#Be.bind(this,t),{passive:!0,capture:!0,signal:r});window.addEventListener("touchmove",stopEvent,{passive:!1,signal:r});window.addEventListener("contextmenu",noContextMenu,{signal:r});this.#be={savedX:this.x,savedY:this.y,savedWidth:this.width,savedHeight:this.height};const a=this.parent.div.style.cursor,o=this.div.style.cursor;this.div.style.cursor=this.parent.div.style.cursor=window.getComputedStyle(e.target).cursor;const pointerUpCallback=()=>{n.abort();this.parent.togglePointerEvents(!0);this.#o?.toggle(!0);this._isDraggable=s;this.parent.div.style.cursor=a;this.div.style.cursor=o;this.#He()};window.addEventListener("pointerup",pointerUpCallback,{signal:r});window.addEventListener("blur",pointerUpCallback,{signal:r})}#Ue(t,e,i,s){this.width=i;this.height=s;this.x=t;this.y=e;const[n,r]=this.parentDimensions;this.setDims(n*i,r*s);this.fixAndSetPosition();this._onResized()}_onResized(){}#He(){if(!this.#be)return;const{savedX:t,savedY:e,savedWidth:i,savedHeight:s}=this.#be;this.#be=null;const n=this.x,r=this.y,a=this.width,o=this.height;n===t&&r===e&&a===i&&o===s||this.addCommands({cmd:this.#Ue.bind(this,n,r,a,o),undo:this.#Ue.bind(this,t,e,i,s),mustExec:!0})}static _round(t){return Math.round(1e4*t)/1e4}#Be(t,e){const[i,s]=this.parentDimensions,n=this.x,r=this.y,a=this.width,o=this.height,l=AnnotationEditor.MIN_SIZE/i,h=AnnotationEditor.MIN_SIZE/s,c=this.#Le(this.rotation),transf=(t,e)=>[c[0]*t+c[2]*e,c[1]*t+c[3]*e],d=this.#Le(360-this.rotation);let u,p,g=!1,f=!1;switch(t){case"topLeft":g=!0;u=(t,e)=>[0,0];p=(t,e)=>[t,e];break;case"topMiddle":u=(t,e)=>[t/2,0];p=(t,e)=>[t/2,e];break;case"topRight":g=!0;u=(t,e)=>[t,0];p=(t,e)=>[0,e];break;case"middleRight":f=!0;u=(t,e)=>[t,e/2];p=(t,e)=>[0,e/2];break;case"bottomRight":g=!0;u=(t,e)=>[t,e];p=(t,e)=>[0,0];break;case"bottomMiddle":u=(t,e)=>[t/2,e];p=(t,e)=>[t/2,0];break;case"bottomLeft":g=!0;u=(t,e)=>[0,e];p=(t,e)=>[t,0];break;case"middleLeft":f=!0;u=(t,e)=>[0,e/2];p=(t,e)=>[t,e/2]}const m=u(a,o),b=p(a,o);let v=transf(...b);const y=AnnotationEditor._round(n+v[0]),w=AnnotationEditor._round(r+v[1]);let A,x,_=1,E=1;if(e.fromKeyboard)({deltaX:A,deltaY:x}=e);else{const{screenX:t,screenY:i}=e,[s,n]=this.#me;[A,x]=this.screenToPageTranslation(t-s,i-n);this.#me[0]=t;this.#me[1]=i}[A,x]=(S=A/i,C=x/s,[d[0]*S+d[2]*C,d[1]*S+d[3]*C]);var S,C;if(g){const t=Math.hypot(a,o);_=E=Math.max(Math.min(Math.hypot(b[0]-m[0]-A,b[1]-m[1]-x)/t,1/a,1/o),l/a,h/o)}else f?_=Math.max(l,Math.min(1,Math.abs(b[0]-m[0]-A)))/a:E=Math.max(h,Math.min(1,Math.abs(b[1]-m[1]-x)))/o;const T=AnnotationEditor._round(a*_),M=AnnotationEditor._round(o*E);v=transf(...p(T,M));const P=y-v[0],D=w-v[1];this.#Ae||=[this.x,this.y,this.width,this.height];this.width=T;this.height=M;this.x=P;this.y=D;this.setDims(i*T,s*M);this.fixAndSetPosition();this._onResizing()}_onResizing(){}altTextFinish(){this.#o?.finish()}async addEditToolbar(){if(this._editToolbar||this.#_e)return this._editToolbar;this._editToolbar=new EditorToolbar(this);this.div.append(this._editToolbar.render());this.#o&&await this._editToolbar.addAltText(this.#o);return this._editToolbar}removeEditToolbar(){if(this._editToolbar){this._editToolbar.remove();this._editToolbar=null;this.#o?.destroy()}}addContainer(t){const e=this._editToolbar?.div;e?e.before(t):this.div.append(t)}getClientDimensions(){return this.div.getBoundingClientRect()}async addAltTextButton(){if(!this.#o){AltText.initialize(AnnotationEditor._l10n);this.#o=new AltText(this);if(this.#he){this.#o.data=this.#he;this.#he=null}await this.addEditToolbar()}}get altTextData(){return this.#o?.data}set altTextData(t){this.#o&&(this.#o.data=t)}get guessedAltText(){return this.#o?.guessedText}async setGuessedAltText(t){await(this.#o?.setGuessedText(t))}serializeAltText(t){return this.#o?.serialize(t)}hasAltText(){return!!this.#o&&!this.#o.isEmpty()}hasAltTextData(){return this.#o?.hasData()??!1}render(){this.div=document.createElement("div");this.div.setAttribute("data-editor-rotation",(360-this.rotation)%360);this.div.className=this.name;this.div.setAttribute("id",this.id);this.div.tabIndex=this.#de?-1:0;this._isVisible||this.div.classList.add("hidden");this.setInForeground();this.#ze();const[t,e]=this.parentDimensions;if(this.parentRotation%180!=0){this.div.style.maxWidth=`${(100*e/t).toFixed(2)}%`;this.div.style.maxHeight=`${(100*t/e).toFixed(2)}%`}const[i,s]=this.getInitialTranslation();this.translate(i,s);bindEvents(this,this.div,["pointerdown"]);this.isResizable&&this._uiManager._supportsPinchToZoom&&(this.#Pe||=new TouchManager({container:this.div,isPinchingDisabled:()=>!this.isSelected,onPinchStart:this.#je.bind(this),onPinching:this.#Ge.bind(this),onPinchEnd:this.#Ve.bind(this),signal:this._uiManager._signal}));this._uiManager._editorUndoBar?.hide();return this.div}#je(){this.#be={savedX:this.x,savedY:this.y,savedWidth:this.width,savedHeight:this.height};this.#o?.toggle(!1);this.parent.togglePointerEvents(!1)}#Ge(t,e,i){let s=i/e*.7+1-.7;if(1===s)return;const n=this.#Le(this.rotation),transf=(t,e)=>[n[0]*t+n[2]*e,n[1]*t+n[3]*e],[r,a]=this.parentDimensions,o=this.x,l=this.y,h=this.width,c=this.height,d=AnnotationEditor.MIN_SIZE/r,u=AnnotationEditor.MIN_SIZE/a;s=Math.max(Math.min(s,1/h,1/c),d/h,u/c);const p=AnnotationEditor._round(h*s),g=AnnotationEditor._round(c*s);if(p===h&&g===c)return;this.#Ae||=[o,l,h,c];const f=transf(h/2,c/2),m=AnnotationEditor._round(o+f[0]),b=AnnotationEditor._round(l+f[1]),v=transf(p/2,g/2);this.x=m-v[0];this.y=b-v[1];this.width=p;this.height=g;this.setDims(r*p,a*g);this.fixAndSetPosition();this._onResizing()}#Ve(){this.#o?.toggle(!0);this.parent.togglePointerEvents(!0);this.#He()}pointerdown(t){const{isMac:e}=util_FeatureTest.platform;if(0!==t.button||t.ctrlKey&&e)t.preventDefault();else{this.#we=!0;this._isDraggable?this.#$e(t):this.#We(t)}}get isSelected(){return this._uiManager.isSelected(this)}#We(t){const{isMac:e}=util_FeatureTest.platform;t.ctrlKey&&!e||t.shiftKey||t.metaKey&&e?this.parent.toggleSelected(this):this.parent.setSelected(this)}#$e(t){const{isSelected:e}=this;this._uiManager.setUpDragSession();let i=!1;const s=new AbortController,n=this._uiManager.combinedSignal(s),r={capture:!0,passive:!1,signal:n},cancelDrag=t=>{s.abort();this.#ue=null;this.#we=!1;this._uiManager.endDragSession()||this.#We(t);i&&this._onStopDragging()};if(e){this.#Ce=t.clientX;this.#Te=t.clientY;this.#ue=t.pointerId;this.#pe=t.pointerType;window.addEventListener("pointermove",(t=>{if(!i){i=!0;this._onStartDragging()}const{clientX:e,clientY:s,pointerId:n}=t;if(n!==this.#ue){stopEvent(t);return}const[r,a]=this.screenToPageTranslation(e-this.#Ce,s-this.#Te);this.#Ce=e;this.#Te=s;this._uiManager.dragSelectedEditors(r,a)}),r);window.addEventListener("touchmove",stopEvent,r);window.addEventListener("pointerdown",(t=>{t.pointerType===this.#pe&&(this.#Pe||t.isPrimary)&&cancelDrag(t);stopEvent(t)}),r)}const pointerUpCallback=t=>{this.#ue&&this.#ue!==t.pointerId?stopEvent(t):cancelDrag(t)};window.addEventListener("pointerup",pointerUpCallback,{signal:n});window.addEventListener("blur",pointerUpCallback,{signal:n})}_onStartDragging(){}_onStopDragging(){}moveInDOM(){this.#Se&&clearTimeout(this.#Se);this.#Se=setTimeout((()=>{this.#Se=null;this.parent?.moveEditorInDOM(this)}),0)}_setParentAndPosition(t,e,i){t.changeParent(this);this.x=e;this.y=i;this.fixAndSetPosition();this._onTranslated()}getRect(t,e,i=this.rotation){const s=this.parentScale,[n,r]=this.pageDimensions,[a,o]=this.pageTranslation,l=t/s,h=e/s,c=this.x*n,d=this.y*r,u=this.width*n,p=this.height*r;switch(i){case 0:return[c+l+a,r-d-h-p+o,c+l+u+a,r-d-h+o];case 90:return[c+h+a,r-d+l+o,c+h+p+a,r-d+l+u+o];case 180:return[c-l-u+a,r-d+h+o,c-l+a,r-d+h+p+o];case 270:return[c-h-p+a,r-d-l-u+o,c-h+a,r-d-l+o];default:throw new Error("Invalid rotation")}}getRectInCurrentCoords(t,e){const[i,s,n,r]=t,a=n-i,o=r-s;switch(this.rotation){case 0:return[i,e-r,a,o];case 90:return[i,e-s,o,a];case 180:return[n,e-s,a,o];case 270:return[n,e-r,o,a];default:throw new Error("Invalid rotation")}}onceAdded(t){}isEmpty(){return!1}enableEditMode(){this.#_e=!0}disableEditMode(){this.#_e=!1}isInEditMode(){return this.#_e}shouldGetKeyboardEvents(){return this.#Ee}needsToBeRebuilt(){return this.div&&!this.isAttachedToDOM}get isOnScreen(){const{top:t,left:e,bottom:i,right:s}=this.getClientDimensions(),{innerHeight:n,innerWidth:r}=window;return e<r&&s>0&&t<n&&i>0}#ze(){if(this.#ve||!this.div)return;this.#ve=new AbortController;const t=this._uiManager.combinedSignal(this.#ve);this.div.addEventListener("focusin",this.focusin.bind(this),{signal:t});this.div.addEventListener("focusout",this.focusout.bind(this),{signal:t})}rebuild(){this.#ze()}rotate(t){}resize(){}serializeDeleted(){return{id:this.annotationElementId,deleted:!0,pageIndex:this.pageIndex,popupRef:this._initialData?.popupRef||""}}serialize(t=!1,e=null){unreachable("An editor must be serializable")}static async deserialize(t,e,i){const s=new this.prototype.constructor({parent:e,id:e.getNextId(),uiManager:i});s.rotation=t.rotation;s.#he=t.accessibilityData;const[n,r]=s.pageDimensions,[a,o,l,h]=s.getRectInCurrentCoords(t.rect,r);s.x=a/n;s.y=o/r;s.width=l/n;s.height=h/r;return s}get hasBeenModified(){return!!this.annotationElementId&&(this.deleted||null!==this.serialize())}remove(){this.#ve?.abort();this.#ve=null;this.isEmpty()||this.commit();this.parent?this.parent.remove(this):this._uiManager.removeEditor(this);if(this.#Se){clearTimeout(this.#Se);this.#Se=null}this.#Re();this.removeEditToolbar();if(this.#Me){for(const t of this.#Me.values())clearTimeout(t);this.#Me=null}this.parent=null;this.#Pe?.destroy();this.#Pe=null}get isResizable(){return!1}makeResizable(){if(this.isResizable){this.#Oe();this.#fe.classList.remove("hidden");bindEvents(this,this.div,["keydown"])}}get toolbarPosition(){return null}keydown(t){if(!this.isResizable||t.target!==this.div||"Enter"!==t.key)return;this._uiManager.setSelected(this);this.#be={savedX:this.x,savedY:this.y,savedWidth:this.width,savedHeight:this.height};const e=this.#fe.children;if(!this.#ce){this.#ce=Array.from(e);const t=this.#qe.bind(this),i=this.#Xe.bind(this),s=this._uiManager._signal;for(const e of this.#ce){const n=e.getAttribute("data-resizer-name");e.setAttribute("role","spinbutton");e.addEventListener("keydown",t,{signal:s});e.addEventListener("blur",i,{signal:s});e.addEventListener("focus",this.#Ye.bind(this,n),{signal:s});e.setAttribute("data-l10n-id",AnnotationEditor._l10nResizer[n])}}const i=this.#ce[0];let s=0;for(const t of e){if(t===i)break;s++}const n=(360-this.rotation+this.parentRotation)%360/90*(this.#ce.length/4);if(n!==s){if(n<s)for(let t=0;t<s-n;t++)this.#fe.append(this.#fe.firstChild);else if(n>s)for(let t=0;t<n-s;t++)this.#fe.firstChild.before(this.#fe.lastChild);let t=0;for(const i of e){const e=this.#ce[t++].getAttribute("data-resizer-name");i.setAttribute("data-l10n-id",AnnotationEditor._l10nResizer[e])}}this.#Ke(0);this.#Ee=!0;this.#fe.firstChild.focus({focusVisible:!0});t.preventDefault();t.stopImmediatePropagation()}#qe(t){AnnotationEditor._resizerKeyboardManager.exec(this,t)}#Xe(t){this.#Ee&&t.relatedTarget?.parentNode!==this.#fe&&this.#Re()}#Ye(t){this.#ye=this.#Ee?t:""}#Ke(t){if(this.#ce)for(const e of this.#ce)e.tabIndex=t}_resizeWithKeyboard(t,e){this.#Ee&&this.#Be(this.#ye,{deltaX:t,deltaY:e,fromKeyboard:!0})}#Re(){this.#Ee=!1;this.#Ke(-1);this.#He()}_stopResizingWithKeyboard(){this.#Re();this.div.focus()}select(){this.makeResizable();this.div?.classList.add("selectedEditor");if(this._editToolbar){this._editToolbar?.show();this.#o?.toggleAltTextBadge(!1)}else this.addEditToolbar().then((()=>{this.div?.classList.contains("selectedEditor")&&this._editToolbar?.show()}))}unselect(){this.#fe?.classList.add("hidden");this.div?.classList.remove("selectedEditor");this.div?.contains(document.activeElement)&&this._uiManager.currentLayer.div.focus({preventScroll:!0});this._editToolbar?.hide();this.#o?.toggleAltTextBadge(!0)}updateParams(t,e){}disableEditing(){}enableEditing(){}enterInEditMode(){}getImageForAltText(){return null}get contentDiv(){return this.div}get isEditing(){return this.#xe}set isEditing(t){this.#xe=t;if(this.parent)if(t){this.parent.setSelected(this);this.parent.setActiveEditor(this)}else this.parent.setActiveEditor(null)}setAspectRatio(t,e){this.#ge=!0;const i=t/e,{style:s}=this.div;s.aspectRatio=i;s.height="auto"}static get MIN_SIZE(){return 16}static canCreateNewEmptyEditor(){return!0}get telemetryInitialData(){return{action:"added"}}get telemetryFinalData(){return null}_reportTelemetry(t,e=!1){if(e){this.#Me||=new Map;const{action:e}=t;let i=this.#Me.get(e);i&&clearTimeout(i);i=setTimeout((()=>{this._reportTelemetry(t);this.#Me.delete(e);0===this.#Me.size&&(this.#Me=null)}),AnnotationEditor._telemetryTimeout);this.#Me.set(e,i)}else{t.type||=this.editorType;this._uiManager._eventBus.dispatch("reporttelemetry",{source:this,details:{type:"editing",data:t}})}}show(t=this._isVisible){this.div.classList.toggle("hidden",!t);this._isVisible=t}enable(){this.div&&(this.div.tabIndex=0);this.#de=!1}disable(){this.div&&(this.div.tabIndex=-1);this.#de=!0}renderAnnotationElement(t){let e=t.container.querySelector(".annotationContent");if(e){if("CANVAS"===e.nodeName){const t=e;e=document.createElement("div");e.classList.add("annotationContent",this.editorType);t.before(e)}}else{e=document.createElement("div");e.classList.add("annotationContent",this.editorType);t.container.prepend(e)}return e}resetAnnotationElement(t){const{firstChild:e}=t.container;"DIV"===e?.nodeName&&e.classList.contains("annotationContent")&&e.remove()}}class FakeEditor extends AnnotationEditor{constructor(t){super(t);this.annotationElementId=t.annotationElementId;this.deleted=!0}serialize(){return this.serializeDeleted()}}const rt=3285377520,at=4294901760,ot=65535;class MurmurHash3_64{constructor(t){this.h1=t?4294967295&t:rt;this.h2=t?4294967295&t:rt}update(t){let e,i;if("string"==typeof t){e=new Uint8Array(2*t.length);i=0;for(let s=0,n=t.length;s<n;s++){const n=t.charCodeAt(s);if(n<=255)e[i++]=n;else{e[i++]=n>>>8;e[i++]=255&n}}}else{if(!ArrayBuffer.isView(t))throw new Error("Invalid data format, must be a string or TypedArray.");e=t.slice();i=e.byteLength}const s=i>>2,n=i-4*s,r=new Uint32Array(e.buffer,0,s);let a=0,o=0,l=this.h1,h=this.h2;const c=3432918353,d=461845907,u=11601,p=13715;for(let t=0;t<s;t++)if(1&t){a=r[t];a=a*c&at|a*u&ot;a=a<<15|a>>>17;a=a*d&at|a*p&ot;l^=a;l=l<<13|l>>>19;l=5*l+3864292196}else{o=r[t];o=o*c&at|o*u&ot;o=o<<15|o>>>17;o=o*d&at|o*p&ot;h^=o;h=h<<13|h>>>19;h=5*h+3864292196}a=0;switch(n){case 3:a^=e[4*s+2]<<16;case 2:a^=e[4*s+1]<<8;case 1:a^=e[4*s];a=a*c&at|a*u&ot;a=a<<15|a>>>17;a=a*d&at|a*p&ot;1&s?l^=a:h^=a}this.h1=l;this.h2=h}hexdigest(){let t=this.h1,e=this.h2;t^=e>>>1;t=3981806797*t&at|36045*t&ot;e=4283543511*e&at|(2950163797*(e<<16|t>>>16)&at)>>>16;t^=e>>>1;t=444984403*t&at|60499*t&ot;e=3301882366*e&at|(3120437893*(e<<16|t>>>16)&at)>>>16;t^=e>>>1;return(t>>>0).toString(16).padStart(8,"0")+(e>>>0).toString(16).padStart(8,"0")}}const lt=Object.freeze({map:null,hash:"",transfer:void 0});class AnnotationStorage{#Qe=!1;#Je=null;#Ze=new Map;constructor(){this.onSetModified=null;this.onResetModified=null;this.onAnnotationEditor=null}getValue(t,e){const i=this.#Ze.get(t);return void 0===i?e:Object.assign(e,i)}getRawValue(t){return this.#Ze.get(t)}remove(t){this.#Ze.delete(t);0===this.#Ze.size&&this.resetModified();if("function"==typeof this.onAnnotationEditor){for(const t of this.#Ze.values())if(t instanceof AnnotationEditor)return;this.onAnnotationEditor(null)}}setValue(t,e){const i=this.#Ze.get(t);let s=!1;if(void 0!==i){for(const[t,n]of Object.entries(e))if(i[t]!==n){s=!0;i[t]=n}}else{s=!0;this.#Ze.set(t,e)}s&&this.#ti();e instanceof AnnotationEditor&&"function"==typeof this.onAnnotationEditor&&this.onAnnotationEditor(e.constructor._type)}has(t){return this.#Ze.has(t)}getAll(){return this.#Ze.size>0?objectFromMap(this.#Ze):null}setAll(t){for(const[e,i]of Object.entries(t))this.setValue(e,i)}get size(){return this.#Ze.size}#ti(){if(!this.#Qe){this.#Qe=!0;"function"==typeof this.onSetModified&&this.onSetModified()}}resetModified(){if(this.#Qe){this.#Qe=!1;"function"==typeof this.onResetModified&&this.onResetModified()}}get print(){return new PrintAnnotationStorage(this)}get serializable(){if(0===this.#Ze.size)return lt;const t=new Map,e=new MurmurHash3_64,i=[],s=Object.create(null);let n=!1;for(const[i,r]of this.#Ze){const a=r instanceof AnnotationEditor?r.serialize(!1,s):r;if(a){t.set(i,a);e.update(`${i}:${JSON.stringify(a)}`);n||=!!a.bitmap}}if(n)for(const e of t.values())e.bitmap&&i.push(e.bitmap);return t.size>0?{map:t,hash:e.hexdigest(),transfer:i}:lt}get editorStats(){let t=null;const e=new Map;for(const i of this.#Ze.values()){if(!(i instanceof AnnotationEditor))continue;const s=i.telemetryFinalData;if(!s)continue;const{type:n}=s;e.has(n)||e.set(n,Object.getPrototypeOf(i).constructor);t||=Object.create(null);const r=t[n]||=new Map;for(const[t,e]of Object.entries(s)){if("type"===t)continue;let i=r.get(t);if(!i){i=new Map;r.set(t,i)}const s=i.get(e)??0;i.set(e,s+1)}}for(const[i,s]of e)t[i]=s.computeTelemetryFinalData(t[i]);return t}resetModifiedIds(){this.#Je=null}get modifiedIds(){if(this.#Je)return this.#Je;const t=[];for(const e of this.#Ze.values())e instanceof AnnotationEditor&&e.annotationElementId&&e.serialize()&&t.push(e.annotationElementId);return this.#Je={ids:new Set(t),hash:t.join(",")}}}class PrintAnnotationStorage extends AnnotationStorage{#ei;constructor(t){super();const{map:e,hash:i,transfer:s}=t.serializable,n=structuredClone(e,s?{transfer:s}:null);this.#ei={map:n,hash:i,transfer:s}}get print(){unreachable("Should not call PrintAnnotationStorage.print")}get serializable(){return this.#ei}get modifiedIds(){return shadow(this,"modifiedIds",{ids:new Set,hash:""})}}class FontLoader{#ii=new Set;constructor({ownerDocument:t=globalThis.document,styleElement:e=null}){this._document=t;this.nativeFontFaces=new Set;this.styleElement=null;this.loadingRequests=[];this.loadTestFontId=0}addNativeFontFace(t){this.nativeFontFaces.add(t);this._document.fonts.add(t)}removeNativeFontFace(t){this.nativeFontFaces.delete(t);this._document.fonts.delete(t)}insertRule(t){if(!this.styleElement){this.styleElement=this._document.createElement("style");this._document.documentElement.getElementsByTagName("head")[0].append(this.styleElement)}const e=this.styleElement.sheet;e.insertRule(t,e.cssRules.length)}clear(){for(const t of this.nativeFontFaces)this._document.fonts.delete(t);this.nativeFontFaces.clear();this.#ii.clear();if(this.styleElement){this.styleElement.remove();this.styleElement=null}}async loadSystemFont({systemFontInfo:t,_inspectFont:e}){if(t&&!this.#ii.has(t.loadedName)){assert(!this.disableFontFace,"loadSystemFont shouldn't be called when `disableFontFace` is set.");if(this.isFontLoadingAPISupported){const{loadedName:i,src:s,style:n}=t,r=new FontFace(i,s,n);this.addNativeFontFace(r);try{await r.load();this.#ii.add(i);e?.(t)}catch{warn(`Cannot load system font: ${t.baseFontName}, installing it could help to improve PDF rendering.`);this.removeNativeFontFace(r)}}else unreachable("Not implemented: loadSystemFont without the Font Loading API.")}}async bind(t){if(t.attached||t.missingFile&&!t.systemFontInfo)return;t.attached=!0;if(t.systemFontInfo){await this.loadSystemFont(t);return}if(this.isFontLoadingAPISupported){const e=t.createNativeFontFace();if(e){this.addNativeFontFace(e);try{await e.loaded}catch(i){warn(`Failed to load font '${e.family}': '${i}'.`);t.disableFontFace=!0;throw i}}return}const e=t.createFontFaceRule();if(e){this.insertRule(e);if(this.isSyncFontLoadingSupported)return;await new Promise((e=>{const i=this._queueLoadingCallback(e);this._prepareFontLoadEvent(t,i)}))}}get isFontLoadingAPISupported(){return shadow(this,"isFontLoadingAPISupported",!!this._document?.fonts)}get isSyncFontLoadingSupported(){let t=!1;(i||"undefined"!=typeof navigator&&"string"==typeof navigator?.userAgent&&/Mozilla\/5.0.*?rv:\d+.*? Gecko/.test(navigator.userAgent))&&(t=!0);return shadow(this,"isSyncFontLoadingSupported",t)}_queueLoadingCallback(t){const{loadingRequests:e}=this,i={done:!1,complete:function completeRequest(){assert(!i.done,"completeRequest() cannot be called twice.");i.done=!0;for(;e.length>0&&e[0].done;){const t=e.shift();setTimeout(t.callback,0)}},callback:t};e.push(i);return i}get _loadTestFont(){return shadow(this,"_loadTestFont",atob("T1RUTwALAIAAAwAwQ0ZGIDHtZg4AAAOYAAAAgUZGVE1lkzZwAAAEHAAAABxHREVGABQAFQAABDgAAAAeT1MvMlYNYwkAAAEgAAAAYGNtYXABDQLUAAACNAAAAUJoZWFk/xVFDQAAALwAAAA2aGhlYQdkA+oAAAD0AAAAJGhtdHgD6AAAAAAEWAAAAAZtYXhwAAJQAAAAARgAAAAGbmFtZVjmdH4AAAGAAAAAsXBvc3T/hgAzAAADeAAAACAAAQAAAAEAALZRFsRfDzz1AAsD6AAAAADOBOTLAAAAAM4KHDwAAAAAA+gDIQAAAAgAAgAAAAAAAAABAAADIQAAAFoD6AAAAAAD6AABAAAAAAAAAAAAAAAAAAAAAQAAUAAAAgAAAAQD6AH0AAUAAAKKArwAAACMAooCvAAAAeAAMQECAAACAAYJAAAAAAAAAAAAAQAAAAAAAAAAAAAAAFBmRWQAwAAuAC4DIP84AFoDIQAAAAAAAQAAAAAAAAAAACAAIAABAAAADgCuAAEAAAAAAAAAAQAAAAEAAAAAAAEAAQAAAAEAAAAAAAIAAQAAAAEAAAAAAAMAAQAAAAEAAAAAAAQAAQAAAAEAAAAAAAUAAQAAAAEAAAAAAAYAAQAAAAMAAQQJAAAAAgABAAMAAQQJAAEAAgABAAMAAQQJAAIAAgABAAMAAQQJAAMAAgABAAMAAQQJAAQAAgABAAMAAQQJAAUAAgABAAMAAQQJAAYAAgABWABYAAAAAAAAAwAAAAMAAAAcAAEAAAAAADwAAwABAAAAHAAEACAAAAAEAAQAAQAAAC7//wAAAC7////TAAEAAAAAAAABBgAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMAAAAAAAD/gwAyAAAAAQAAAAAAAAAAAAAAAAAAAAABAAQEAAEBAQJYAAEBASH4DwD4GwHEAvgcA/gXBIwMAYuL+nz5tQXkD5j3CBLnEQACAQEBIVhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYAAABAQAADwACAQEEE/t3Dov6fAH6fAT+fPp8+nwHDosMCvm1Cvm1DAz6fBQAAAAAAAABAAAAAMmJbzEAAAAAzgTjFQAAAADOBOQpAAEAAAAAAAAADAAUAAQAAAABAAAAAgABAAAAAAAAAAAD6AAAAAAAAA=="))}_prepareFontLoadEvent(t,e){function int32(t,e){return t.charCodeAt(e)<<24|t.charCodeAt(e+1)<<16|t.charCodeAt(e+2)<<8|255&t.charCodeAt(e+3)}function spliceString(t,e,i,s){return t.substring(0,e)+s+t.substring(e+i)}let i,s;const n=this._document.createElement("canvas");n.width=1;n.height=1;const r=n.getContext("2d");let a=0;const o=`lt${Date.now()}${this.loadTestFontId++}`;let l=this._loadTestFont;l=spliceString(l,976,o.length,o);const h=1482184792;let c=int32(l,16);for(i=0,s=o.length-3;i<s;i+=4)c=c-h+int32(o,i)|0;i<o.length&&(c=c-h+int32(o+"XXX",i)|0);l=spliceString(l,16,4,function string32(t){return String.fromCharCode(t>>24&255,t>>16&255,t>>8&255,255&t)}(c));const d=`@font-face {font-family:"${o}";src:${`url(data:font/opentype;base64,${btoa(l)});`}}`;this.insertRule(d);const u=this._document.createElement("div");u.style.visibility="hidden";u.style.width=u.style.height="10px";u.style.position="absolute";u.style.top=u.style.left="0px";for(const e of[t.loadedName,o]){const t=this._document.createElement("span");t.textContent="Hi";t.style.fontFamily=e;u.append(t)}this._document.body.append(u);!function isFontReady(t,e){if(++a>30){warn("Load test font never loaded.");e();return}r.font="30px "+t;r.fillText(".",0,20);r.getImageData(0,0,1,1).data[3]>0?e():setTimeout(isFontReady.bind(null,t,e))}(o,(()=>{u.remove();e.complete()}))}}class FontFaceObject{constructor(t,{disableFontFace:e=!1,fontExtraProperties:i=!1,inspectFont:s=null}){this.compiledGlyphs=Object.create(null);for(const e in t)this[e]=t[e];this.disableFontFace=!0===e;this.fontExtraProperties=!0===i;this._inspectFont=s}createNativeFontFace(){if(!this.data||this.disableFontFace)return null;let t;if(this.cssFontInfo){const e={weight:this.cssFontInfo.fontWeight};this.cssFontInfo.italicAngle&&(e.style=`oblique ${this.cssFontInfo.italicAngle}deg`);t=new FontFace(this.cssFontInfo.fontFamily,this.data,e)}else t=new FontFace(this.loadedName,this.data,{});this._inspectFont?.(this);return t}createFontFaceRule(){if(!this.data||this.disableFontFace)return null;const t=`url(data:${this.mimetype};base64,${function toBase64Util(t){return Uint8Array.prototype.toBase64?t.toBase64():btoa(bytesToString(t))}(this.data)});`;let e;if(this.cssFontInfo){let i=`font-weight: ${this.cssFontInfo.fontWeight};`;this.cssFontInfo.italicAngle&&(i+=`font-style: oblique ${this.cssFontInfo.italicAngle}deg;`);e=`@font-face {font-family:"${this.cssFontInfo.fontFamily}";${i}src:${t}}`}else e=`@font-face {font-family:"${this.loadedName}";src:${t}}`;this._inspectFont?.(this,t);return e}getPathGenerator(t,e){if(void 0!==this.compiledGlyphs[e])return this.compiledGlyphs[e];const i=this.loadedName+"_path_"+e;let s;try{s=t.get(i)}catch(t){warn(`getPathGenerator - ignoring character: "${t}".`)}const n=new Path2D(s||"");this.fontExtraProperties||t.delete(i);return this.compiledGlyphs[e]=n}}const ht=1,ct=2,dt=1,ut=2,pt=3,gt=4,ft=5,mt=6,bt=7,vt=8;function onFn(){}function wrapReason(t){if(t instanceof AbortException||t instanceof InvalidPDFException||t instanceof MissingPDFException||t instanceof PasswordException||t instanceof UnexpectedResponseException||t instanceof UnknownErrorException)return t;t instanceof Error||"object"==typeof t&&null!==t||unreachable('wrapReason: Expected "reason" to be a (possibly cloned) Error.');switch(t.name){case"AbortException":return new AbortException(t.message);case"InvalidPDFException":return new InvalidPDFException(t.message);case"MissingPDFException":return new MissingPDFException(t.message);case"PasswordException":return new PasswordException(t.message,t.code);case"UnexpectedResponseException":return new UnexpectedResponseException(t.message,t.status);case"UnknownErrorException":return new UnknownErrorException(t.message,t.details)}return new UnknownErrorException(t.message,t.toString())}class MessageHandler{#si=new AbortController;constructor(t,e,i){this.sourceName=t;this.targetName=e;this.comObj=i;this.callbackId=1;this.streamId=1;this.streamSinks=Object.create(null);this.streamControllers=Object.create(null);this.callbackCapabilities=Object.create(null);this.actionHandler=Object.create(null);i.addEventListener("message",this.#ni.bind(this),{signal:this.#si.signal})}#ni({data:t}){if(t.targetName!==this.sourceName)return;if(t.stream){this.#ri(t);return}if(t.callback){const e=t.callbackId,i=this.callbackCapabilities[e];if(!i)throw new Error(`Cannot resolve callback ${e}`);delete this.callbackCapabilities[e];if(t.callback===ht)i.resolve(t.data);else{if(t.callback!==ct)throw new Error("Unexpected callback case");i.reject(wrapReason(t.reason))}return}const e=this.actionHandler[t.action];if(!e)throw new Error(`Unknown action from worker: ${t.action}`);if(t.callbackId){const i=this.sourceName,s=t.sourceName,n=this.comObj;Promise.try(e,t.data).then((function(e){n.postMessage({sourceName:i,targetName:s,callback:ht,callbackId:t.callbackId,data:e})}),(function(e){n.postMessage({sourceName:i,targetName:s,callback:ct,callbackId:t.callbackId,reason:wrapReason(e)})}))}else t.streamId?this.#ai(t):e(t.data)}on(t,e){const i=this.actionHandler;if(i[t])throw new Error(`There is already an actionName called "${t}"`);i[t]=e}send(t,e,i){this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:t,data:e},i)}sendWithPromise(t,e,i){const s=this.callbackId++,n=Promise.withResolvers();this.callbackCapabilities[s]=n;try{this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:t,callbackId:s,data:e},i)}catch(t){n.reject(t)}return n.promise}sendWithStream(t,e,i,s){const n=this.streamId++,r=this.sourceName,a=this.targetName,o=this.comObj;return new ReadableStream({start:i=>{const l=Promise.withResolvers();this.streamControllers[n]={controller:i,startCall:l,pullCall:null,cancelCall:null,isClosed:!1};o.postMessage({sourceName:r,targetName:a,action:t,streamId:n,data:e,desiredSize:i.desiredSize},s);return l.promise},pull:t=>{const e=Promise.withResolvers();this.streamControllers[n].pullCall=e;o.postMessage({sourceName:r,targetName:a,stream:mt,streamId:n,desiredSize:t.desiredSize});return e.promise},cancel:t=>{assert(t instanceof Error,"cancel must have a valid reason");const e=Promise.withResolvers();this.streamControllers[n].cancelCall=e;this.streamControllers[n].isClosed=!0;o.postMessage({sourceName:r,targetName:a,stream:dt,streamId:n,reason:wrapReason(t)});return e.promise}},i)}#ai(t){const e=t.streamId,i=this.sourceName,s=t.sourceName,n=this.comObj,r=this,a=this.actionHandler[t.action],o={enqueue(t,r=1,a){if(this.isCancelled)return;const o=this.desiredSize;this.desiredSize-=r;if(o>0&&this.desiredSize<=0){this.sinkCapability=Promise.withResolvers();this.ready=this.sinkCapability.promise}n.postMessage({sourceName:i,targetName:s,stream:gt,streamId:e,chunk:t},a)},close(){if(!this.isCancelled){this.isCancelled=!0;n.postMessage({sourceName:i,targetName:s,stream:pt,streamId:e});delete r.streamSinks[e]}},error(t){assert(t instanceof Error,"error must have a valid reason");if(!this.isCancelled){this.isCancelled=!0;n.postMessage({sourceName:i,targetName:s,stream:ft,streamId:e,reason:wrapReason(t)})}},sinkCapability:Promise.withResolvers(),onPull:null,onCancel:null,isCancelled:!1,desiredSize:t.desiredSize,ready:null};o.sinkCapability.resolve();o.ready=o.sinkCapability.promise;this.streamSinks[e]=o;Promise.try(a,t.data,o).then((function(){n.postMessage({sourceName:i,targetName:s,stream:vt,streamId:e,success:!0})}),(function(t){n.postMessage({sourceName:i,targetName:s,stream:vt,streamId:e,reason:wrapReason(t)})}))}#ri(t){const e=t.streamId,i=this.sourceName,s=t.sourceName,n=this.comObj,r=this.streamControllers[e],a=this.streamSinks[e];switch(t.stream){case vt:t.success?r.startCall.resolve():r.startCall.reject(wrapReason(t.reason));break;case bt:t.success?r.pullCall.resolve():r.pullCall.reject(wrapReason(t.reason));break;case mt:if(!a){n.postMessage({sourceName:i,targetName:s,stream:bt,streamId:e,success:!0});break}a.desiredSize<=0&&t.desiredSize>0&&a.sinkCapability.resolve();a.desiredSize=t.desiredSize;Promise.try(a.onPull||onFn).then((function(){n.postMessage({sourceName:i,targetName:s,stream:bt,streamId:e,success:!0})}),(function(t){n.postMessage({sourceName:i,targetName:s,stream:bt,streamId:e,reason:wrapReason(t)})}));break;case gt:assert(r,"enqueue should have stream controller");if(r.isClosed)break;r.controller.enqueue(t.chunk);break;case pt:assert(r,"close should have stream controller");if(r.isClosed)break;r.isClosed=!0;r.controller.close();this.#oi(r,e);break;case ft:assert(r,"error should have stream controller");r.controller.error(wrapReason(t.reason));this.#oi(r,e);break;case ut:t.success?r.cancelCall.resolve():r.cancelCall.reject(wrapReason(t.reason));this.#oi(r,e);break;case dt:if(!a)break;const o=wrapReason(t.reason);Promise.try(a.onCancel||onFn,o).then((function(){n.postMessage({sourceName:i,targetName:s,stream:ut,streamId:e,success:!0})}),(function(t){n.postMessage({sourceName:i,targetName:s,stream:ut,streamId:e,reason:wrapReason(t)})}));a.sinkCapability.reject(o);a.isCancelled=!0;delete this.streamSinks[e];break;default:throw new Error("Unexpected stream case")}}async#oi(t,e){await Promise.allSettled([t.startCall?.promise,t.pullCall?.promise,t.cancelCall?.promise]);delete this.streamControllers[e]}destroy(){this.#si?.abort();this.#si=null}}class BaseCanvasFactory{#li=!1;constructor({enableHWA:t=!1}){this.#li=t}create(t,e){if(t<=0||e<=0)throw new Error("Invalid canvas size");const i=this._createCanvas(t,e);return{canvas:i,context:i.getContext("2d",{willReadFrequently:!this.#li})}}reset(t,e,i){if(!t.canvas)throw new Error("Canvas is not specified");if(e<=0||i<=0)throw new Error("Invalid canvas size");t.canvas.width=e;t.canvas.height=i}destroy(t){if(!t.canvas)throw new Error("Canvas is not specified");t.canvas.width=0;t.canvas.height=0;t.canvas=null;t.context=null}_createCanvas(t,e){unreachable("Abstract method `_createCanvas` called.")}}class BaseCMapReaderFactory{constructor({baseUrl:t=null,isCompressed:e=!0}){this.baseUrl=t;this.isCompressed=e}async fetch({name:t}){if(!this.baseUrl)throw new Error("Ensure that the `cMapUrl` and `cMapPacked` API parameters are provided.");if(!t)throw new Error("CMap name must be specified.");const e=this.baseUrl+t+(this.isCompressed?".bcmap":"");return this._fetch(e).then((t=>({cMapData:t,isCompressed:this.isCompressed}))).catch((t=>{throw new Error(`Unable to load ${this.isCompressed?"binary ":""}CMap at: ${e}`)}))}async _fetch(t){unreachable("Abstract method `_fetch` called.")}}class DOMCMapReaderFactory extends BaseCMapReaderFactory{async _fetch(t){const e=await fetchData(t,this.isCompressed?"arraybuffer":"text");return e instanceof ArrayBuffer?new Uint8Array(e):stringToBytes(e)}}__webpack_require__(4520);class BaseFilterFactory{addFilter(t){return"none"}addHCMFilter(t,e){return"none"}addAlphaFilter(t){return"none"}addLuminosityFilter(t){return"none"}addHighlightHCMFilter(t,e,i,s,n){return"none"}destroy(t=!1){}}class BaseStandardFontDataFactory{constructor({baseUrl:t=null}){this.baseUrl=t}async fetch({filename:t}){if(!this.baseUrl)throw new Error("Ensure that the `standardFontDataUrl` API parameter is provided.");if(!t)throw new Error("Font filename must be specified.");const e=`${this.baseUrl}${t}`;return this._fetch(e).catch((t=>{throw new Error(`Unable to load font data at: ${e}`)}))}async _fetch(t){unreachable("Abstract method `_fetch` called.")}}class DOMStandardFontDataFactory extends BaseStandardFontDataFactory{async _fetch(t){const e=await fetchData(t,"arraybuffer");return new Uint8Array(e)}}if(i){let t;try{const e=process.getBuiltinModule("module").createRequire(import.meta.url);try{t=e("@napi-rs/canvas")}catch(t){warn(`Cannot load "@napi-rs/canvas" package: "${t}".`)}}catch(t){warn(`Cannot access the \`require\` function: "${t}".`)}globalThis.DOMMatrix||(t?.DOMMatrix?globalThis.DOMMatrix=t.DOMMatrix:warn("Cannot polyfill `DOMMatrix`, rendering may be broken."));globalThis.ImageData||(t?.ImageData?globalThis.ImageData=t.ImageData:warn("Cannot polyfill `ImageData`, rendering may be broken."));globalThis.Path2D||(t?.Path2D?globalThis.Path2D=t.Path2D:warn("Cannot polyfill `Path2D`, rendering may be broken."))}async function node_utils_fetchData(t){const e=process.getBuiltinModule("fs"),i=await e.promises.readFile(t);return new Uint8Array(i)}const yt="Fill",wt="Stroke",At="Shading";function applyBoundingBox(t,e){if(!e)return;const i=e[2]-e[0],s=e[3]-e[1],n=new Path2D;n.rect(e[0],e[1],i,s);t.clip(n)}class BaseShadingPattern{getPattern(){unreachable("Abstract method `getPattern` called.")}}class RadialAxialShadingPattern extends BaseShadingPattern{constructor(t){super();this._type=t[1];this._bbox=t[2];this._colorStops=t[3];this._p0=t[4];this._p1=t[5];this._r0=t[6];this._r1=t[7];this.matrix=null}_createGradient(t){let e;"axial"===this._type?e=t.createLinearGradient(this._p0[0],this._p0[1],this._p1[0],this._p1[1]):"radial"===this._type&&(e=t.createRadialGradient(this._p0[0],this._p0[1],this._r0,this._p1[0],this._p1[1],this._r1));for(const t of this._colorStops)e.addColorStop(t[0],t[1]);return e}getPattern(t,e,i,s){let n;if(s===wt||s===yt){const r=e.current.getClippedPathBoundingBox(s,getCurrentTransform(t))||[0,0,0,0],a=Math.ceil(r[2]-r[0])||1,o=Math.ceil(r[3]-r[1])||1,l=e.cachedCanvases.getCanvas("pattern",a,o),h=l.context;h.clearRect(0,0,h.canvas.width,h.canvas.height);h.beginPath();h.rect(0,0,h.canvas.width,h.canvas.height);h.translate(-r[0],-r[1]);i=Util.transform(i,[1,0,0,1,r[0],r[1]]);h.transform(...e.baseTransform);this.matrix&&h.transform(...this.matrix);applyBoundingBox(h,this._bbox);h.fillStyle=this._createGradient(h);h.fill();n=t.createPattern(l.canvas,"no-repeat");const c=new DOMMatrix(i);n.setTransform(c)}else{applyBoundingBox(t,this._bbox);n=this._createGradient(t)}return n}}function drawTriangle(t,e,i,s,n,r,a,o){const l=e.coords,h=e.colors,c=t.data,d=4*t.width;let u;if(l[i+1]>l[s+1]){u=i;i=s;s=u;u=r;r=a;a=u}if(l[s+1]>l[n+1]){u=s;s=n;n=u;u=a;a=o;o=u}if(l[i+1]>l[s+1]){u=i;i=s;s=u;u=r;r=a;a=u}const p=(l[i]+e.offsetX)*e.scaleX,g=(l[i+1]+e.offsetY)*e.scaleY,f=(l[s]+e.offsetX)*e.scaleX,m=(l[s+1]+e.offsetY)*e.scaleY,b=(l[n]+e.offsetX)*e.scaleX,v=(l[n+1]+e.offsetY)*e.scaleY;if(g>=v)return;const y=h[r],w=h[r+1],A=h[r+2],x=h[a],_=h[a+1],E=h[a+2],S=h[o],C=h[o+1],T=h[o+2],M=Math.round(g),P=Math.round(v);let D,k,R,I,L,O,N,B;for(let t=M;t<=P;t++){if(t<m){const e=t<g?0:(g-t)/(g-m);D=p-(p-f)*e;k=y-(y-x)*e;R=w-(w-_)*e;I=A-(A-E)*e}else{let e;e=t>v?1:m===v?0:(m-t)/(m-v);D=f-(f-b)*e;k=x-(x-S)*e;R=_-(_-C)*e;I=E-(E-T)*e}let e;e=t<g?0:t>v?1:(g-t)/(g-v);L=p-(p-b)*e;O=y-(y-S)*e;N=w-(w-C)*e;B=A-(A-T)*e;const i=Math.round(Math.min(D,L)),s=Math.round(Math.max(D,L));let n=d*t+4*i;for(let t=i;t<=s;t++){e=(D-t)/(D-L);e<0?e=0:e>1&&(e=1);c[n++]=k-(k-O)*e|0;c[n++]=R-(R-N)*e|0;c[n++]=I-(I-B)*e|0;c[n++]=255}}}function drawFigure(t,e,i){const s=e.coords,n=e.colors;let r,a;switch(e.type){case"lattice":const o=e.verticesPerRow,l=Math.floor(s.length/o)-1,h=o-1;for(r=0;r<l;r++){let e=r*o;for(let r=0;r<h;r++,e++){drawTriangle(t,i,s[e],s[e+1],s[e+o],n[e],n[e+1],n[e+o]);drawTriangle(t,i,s[e+o+1],s[e+1],s[e+o],n[e+o+1],n[e+1],n[e+o])}}break;case"triangles":for(r=0,a=s.length;r<a;r+=3)drawTriangle(t,i,s[r],s[r+1],s[r+2],n[r],n[r+1],n[r+2]);break;default:throw new Error("illegal figure")}}class MeshShadingPattern extends BaseShadingPattern{constructor(t){super();this._coords=t[2];this._colors=t[3];this._figures=t[4];this._bounds=t[5];this._bbox=t[7];this._background=t[8];this.matrix=null}_createMeshCanvas(t,e,i){const s=Math.floor(this._bounds[0]),n=Math.floor(this._bounds[1]),r=Math.ceil(this._bounds[2])-s,a=Math.ceil(this._bounds[3])-n,o=Math.min(Math.ceil(Math.abs(r*t[0]*1.1)),3e3),l=Math.min(Math.ceil(Math.abs(a*t[1]*1.1)),3e3),h=r/o,c=a/l,d={coords:this._coords,colors:this._colors,offsetX:-s,offsetY:-n,scaleX:1/h,scaleY:1/c},u=o+4,p=l+4,g=i.getCanvas("mesh",u,p),f=g.context,m=f.createImageData(o,l);if(e){const t=m.data;for(let i=0,s=t.length;i<s;i+=4){t[i]=e[0];t[i+1]=e[1];t[i+2]=e[2];t[i+3]=255}}for(const t of this._figures)drawFigure(m,t,d);f.putImageData(m,2,2);return{canvas:g.canvas,offsetX:s-2*h,offsetY:n-2*c,scaleX:h,scaleY:c}}getPattern(t,e,i,s){applyBoundingBox(t,this._bbox);let n;if(s===At)n=Util.singularValueDecompose2dScale(getCurrentTransform(t));else{n=Util.singularValueDecompose2dScale(e.baseTransform);if(this.matrix){const t=Util.singularValueDecompose2dScale(this.matrix);n=[n[0]*t[0],n[1]*t[1]]}}const r=this._createMeshCanvas(n,s===At?null:this._background,e.cachedCanvases);if(s!==At){t.setTransform(...e.baseTransform);this.matrix&&t.transform(...this.matrix)}t.translate(r.offsetX,r.offsetY);t.scale(r.scaleX,r.scaleY);return t.createPattern(r.canvas,"no-repeat")}}class DummyShadingPattern extends BaseShadingPattern{getPattern(){return"hotpink"}}const xt=1,_t=2;class TilingPattern{static MAX_PATTERN_SIZE=3e3;constructor(t,e,i,s,n){this.operatorList=t[2];this.matrix=t[3];this.bbox=t[4];this.xstep=t[5];this.ystep=t[6];this.paintType=t[7];this.tilingType=t[8];this.color=e;this.ctx=i;this.canvasGraphicsFactory=s;this.baseTransform=n}createPatternCanvas(t){const{bbox:e,operatorList:i,paintType:s,tilingType:n,color:r,canvasGraphicsFactory:a}=this;let{xstep:o,ystep:l}=this;o=Math.abs(o);l=Math.abs(l);info("TilingType: "+n);const h=e[0],c=e[1],d=e[2],u=e[3],p=d-h,g=u-c,f=Util.singularValueDecompose2dScale(this.matrix),m=Util.singularValueDecompose2dScale(this.baseTransform),b=f[0]*m[0],v=f[1]*m[1];let y=p,w=g,A=!1,x=!1;const _=Math.ceil(o*b),E=Math.ceil(l*v);_>=Math.ceil(p*b)?y=o:A=!0;E>=Math.ceil(g*v)?w=l:x=!0;const S=this.getSizeAndScale(y,this.ctx.canvas.width,b),C=this.getSizeAndScale(w,this.ctx.canvas.height,v),T=t.cachedCanvases.getCanvas("pattern",S.size,C.size),M=T.context,P=a.createCanvasGraphics(M);P.groupLevel=t.groupLevel;this.setFillAndStrokeStyleToContext(P,s,r);M.translate(-S.scale*h,-C.scale*c);P.transform(S.scale,0,0,C.scale,0,0);M.save();this.clipBbox(P,h,c,d,u);P.baseTransform=getCurrentTransform(P.ctx);P.executeOperatorList(i);P.endDrawing();M.restore();if(A||x){const e=T.canvas;A&&(y=o);x&&(w=l);const i=this.getSizeAndScale(y,this.ctx.canvas.width,b),s=this.getSizeAndScale(w,this.ctx.canvas.height,v),n=i.size,r=s.size,a=t.cachedCanvases.getCanvas("pattern-workaround",n,r),d=a.context,u=A?Math.floor(p/o):0,f=x?Math.floor(g/l):0;for(let t=0;t<=u;t++)for(let i=0;i<=f;i++)d.drawImage(e,n*t,r*i,n,r,0,0,n,r);return{canvas:a.canvas,scaleX:i.scale,scaleY:s.scale,offsetX:h,offsetY:c}}return{canvas:T.canvas,scaleX:S.scale,scaleY:C.scale,offsetX:h,offsetY:c}}getSizeAndScale(t,e,i){const s=Math.max(TilingPattern.MAX_PATTERN_SIZE,e);let n=Math.ceil(t*i);n>=s?n=s:i=n/t;return{scale:i,size:n}}clipBbox(t,e,i,s,n){const r=s-e,a=n-i;t.ctx.rect(e,i,r,a);t.current.updateRectMinMax(getCurrentTransform(t.ctx),[e,i,s,n]);t.clip();t.endPath()}setFillAndStrokeStyleToContext(t,e,i){const s=t.ctx,n=t.current;switch(e){case xt:const t=this.ctx;s.fillStyle=t.fillStyle;s.strokeStyle=t.strokeStyle;n.fillColor=t.fillStyle;n.strokeColor=t.strokeStyle;break;case _t:const r=Util.makeHexColor(i[0],i[1],i[2]);s.fillStyle=r;s.strokeStyle=r;n.fillColor=r;n.strokeColor=r;break;default:throw new FormatError(`Unsupported paint type: ${e}`)}}getPattern(t,e,i,s){let n=i;if(s!==At){n=Util.transform(n,e.baseTransform);this.matrix&&(n=Util.transform(n,this.matrix))}const r=this.createPatternCanvas(e);let a=new DOMMatrix(n);a=a.translate(r.offsetX,r.offsetY);a=a.scale(1/r.scaleX,1/r.scaleY);const o=t.createPattern(r.canvas,"repeat");o.setTransform(a);return o}}function convertBlackAndWhiteToRGBA({src:t,srcPos:e=0,dest:i,width:s,height:n,nonBlackColor:r=4294967295,inverseDecode:a=!1}){const o=util_FeatureTest.isLittleEndian?4278190080:255,[l,h]=a?[r,o]:[o,r],c=s>>3,d=7&s,u=t.length;i=new Uint32Array(i.buffer);let p=0;for(let s=0;s<n;s++){for(const s=e+c;e<s;e++){const s=e<u?t[e]:255;i[p++]=128&s?h:l;i[p++]=64&s?h:l;i[p++]=32&s?h:l;i[p++]=16&s?h:l;i[p++]=8&s?h:l;i[p++]=4&s?h:l;i[p++]=2&s?h:l;i[p++]=1&s?h:l}if(0===d)continue;const s=e<u?t[e++]:255;for(let t=0;t<d;t++)i[p++]=s&1<<7-t?h:l}return{srcPos:e,destPos:p}}const Et=16;class CachedCanvases{constructor(t){this.canvasFactory=t;this.cache=Object.create(null)}getCanvas(t,e,i){let s;if(void 0!==this.cache[t]){s=this.cache[t];this.canvasFactory.reset(s,e,i)}else{s=this.canvasFactory.create(e,i);this.cache[t]=s}return s}delete(t){delete this.cache[t]}clear(){for(const t in this.cache){const e=this.cache[t];this.canvasFactory.destroy(e);delete this.cache[t]}}}function drawImageAtIntegerCoords(t,e,i,s,n,r,a,o,l,h){const[c,d,u,p,g,f]=getCurrentTransform(t);if(0===d&&0===u){const m=a*c+g,b=Math.round(m),v=o*p+f,y=Math.round(v),w=(a+l)*c+g,A=Math.abs(Math.round(w)-b)||1,x=(o+h)*p+f,_=Math.abs(Math.round(x)-y)||1;t.setTransform(Math.sign(c),0,0,Math.sign(p),b,y);t.drawImage(e,i,s,n,r,0,0,A,_);t.setTransform(c,d,u,p,g,f);return[A,_]}if(0===c&&0===p){const m=o*u+g,b=Math.round(m),v=a*d+f,y=Math.round(v),w=(o+h)*u+g,A=Math.abs(Math.round(w)-b)||1,x=(a+l)*d+f,_=Math.abs(Math.round(x)-y)||1;t.setTransform(0,Math.sign(d),Math.sign(u),0,b,y);t.drawImage(e,i,s,n,r,0,0,_,A);t.setTransform(c,d,u,p,g,f);return[_,A]}t.drawImage(e,i,s,n,r,a,o,l,h);return[Math.hypot(c,d)*l,Math.hypot(u,p)*h]}class CanvasExtraState{constructor(t,e){this.alphaIsShape=!1;this.fontSize=0;this.fontSizeScale=1;this.textMatrix=s;this.textMatrixScale=1;this.fontMatrix=n;this.leading=0;this.x=0;this.y=0;this.lineX=0;this.lineY=0;this.charSpacing=0;this.wordSpacing=0;this.textHScale=1;this.textRenderingMode=v;this.textRise=0;this.fillColor="#000000";this.strokeColor="#000000";this.patternFill=!1;this.patternStroke=!1;this.fillAlpha=1;this.strokeAlpha=1;this.lineWidth=1;this.activeSMask=null;this.transferMaps="none";this.startNewPathAndClipBox([0,0,t,e])}clone(){const t=Object.create(this);t.clipBox=this.clipBox.slice();return t}setCurrentPoint(t,e){this.x=t;this.y=e}updatePathMinMax(t,e,i){[e,i]=Util.applyTransform([e,i],t);this.minX=Math.min(this.minX,e);this.minY=Math.min(this.minY,i);this.maxX=Math.max(this.maxX,e);this.maxY=Math.max(this.maxY,i)}updateRectMinMax(t,e){const i=Util.applyTransform(e,t),s=Util.applyTransform(e.slice(2),t),n=Util.applyTransform([e[0],e[3]],t),r=Util.applyTransform([e[2],e[1]],t);this.minX=Math.min(this.minX,i[0],s[0],n[0],r[0]);this.minY=Math.min(this.minY,i[1],s[1],n[1],r[1]);this.maxX=Math.max(this.maxX,i[0],s[0],n[0],r[0]);this.maxY=Math.max(this.maxY,i[1],s[1],n[1],r[1])}updateScalingPathMinMax(t,e){Util.scaleMinMax(t,e);this.minX=Math.min(this.minX,e[0]);this.minY=Math.min(this.minY,e[1]);this.maxX=Math.max(this.maxX,e[2]);this.maxY=Math.max(this.maxY,e[3])}updateCurvePathMinMax(t,e,i,s,n,r,a,o,l,h){const c=Util.bezierBoundingBox(e,i,s,n,r,a,o,l,h);h||this.updateRectMinMax(t,c)}getPathBoundingBox(t=yt,e=null){const i=[this.minX,this.minY,this.maxX,this.maxY];if(t===wt){e||unreachable("Stroke bounding box must include transform.");const t=Util.singularValueDecompose2dScale(e),s=t[0]*this.lineWidth/2,n=t[1]*this.lineWidth/2;i[0]-=s;i[1]-=n;i[2]+=s;i[3]+=n}return i}updateClipFromPath(){const t=Util.intersect(this.clipBox,this.getPathBoundingBox());this.startNewPathAndClipBox(t||[0,0,0,0])}isEmptyClip(){return this.minX===1/0}startNewPathAndClipBox(t){this.clipBox=t;this.minX=1/0;this.minY=1/0;this.maxX=0;this.maxY=0}getClippedPathBoundingBox(t=yt,e=null){return Util.intersect(this.clipBox,this.getPathBoundingBox(t,e))}}function putBinaryImageData(t,e){if(e instanceof ImageData){t.putImageData(e,0,0);return}const i=e.height,s=e.width,n=i%Et,r=(i-n)/Et,a=0===n?r:r+1,o=t.createImageData(s,Et);let l,h=0;const c=e.data,d=o.data;let u,p,g,f;if(e.kind===E.GRAYSCALE_1BPP){const e=c.byteLength,i=new Uint32Array(d.buffer,0,d.byteLength>>2),f=i.length,m=s+7>>3,b=4294967295,v=util_FeatureTest.isLittleEndian?4278190080:255;for(u=0;u<a;u++){g=u<r?Et:n;l=0;for(p=0;p<g;p++){const t=e-h;let n=0;const r=t>m?s:8*t-7,a=-8&r;let o=0,d=0;for(;n<a;n+=8){d=c[h++];i[l++]=128&d?b:v;i[l++]=64&d?b:v;i[l++]=32&d?b:v;i[l++]=16&d?b:v;i[l++]=8&d?b:v;i[l++]=4&d?b:v;i[l++]=2&d?b:v;i[l++]=1&d?b:v}for(;n<r;n++){if(0===o){d=c[h++];o=128}i[l++]=d&o?b:v;o>>=1}}for(;l<f;)i[l++]=0;t.putImageData(o,0,u*Et)}}else if(e.kind===E.RGBA_32BPP){p=0;f=s*Et*4;for(u=0;u<r;u++){d.set(c.subarray(h,h+f));h+=f;t.putImageData(o,0,p);p+=Et}if(u<a){f=s*n*4;d.set(c.subarray(h,h+f));t.putImageData(o,0,p)}}else{if(e.kind!==E.RGB_24BPP)throw new Error(`bad image kind: ${e.kind}`);g=Et;f=s*g;for(u=0;u<a;u++){if(u>=r){g=n;f=s*g}l=0;for(p=f;p--;){d[l++]=c[h++];d[l++]=c[h++];d[l++]=c[h++];d[l++]=255}t.putImageData(o,0,u*Et)}}}function putBinaryImageMask(t,e){if(e.bitmap){t.drawImage(e.bitmap,0,0);return}const i=e.height,s=e.width,n=i%Et,r=(i-n)/Et,a=0===n?r:r+1,o=t.createImageData(s,Et);let l=0;const h=e.data,c=o.data;for(let e=0;e<a;e++){const i=e<r?Et:n;({srcPos:l}=convertBlackAndWhiteToRGBA({src:h,srcPos:l,dest:c,width:s,height:i,nonBlackColor:0}));t.putImageData(o,0,e*Et)}}function copyCtxState(t,e){const i=["strokeStyle","fillStyle","fillRule","globalAlpha","lineWidth","lineCap","lineJoin","miterLimit","globalCompositeOperation","font","filter"];for(const s of i)void 0!==t[s]&&(e[s]=t[s]);if(void 0!==t.setLineDash){e.setLineDash(t.getLineDash());e.lineDashOffset=t.lineDashOffset}}function resetCtxToDefault(t){t.strokeStyle=t.fillStyle="#000000";t.fillRule="nonzero";t.globalAlpha=1;t.lineWidth=1;t.lineCap="butt";t.lineJoin="miter";t.miterLimit=10;t.globalCompositeOperation="source-over";t.font="10px sans-serif";if(void 0!==t.setLineDash){t.setLineDash([]);t.lineDashOffset=0}if(!i){const{filter:e}=t;"none"!==e&&""!==e&&(t.filter="none")}}function getImageSmoothingEnabled(t,e){if(e)return!0;const i=Util.singularValueDecompose2dScale(t);i[0]=Math.fround(i[0]);i[1]=Math.fround(i[1]);const s=Math.fround((globalThis.devicePixelRatio||1)*PixelsPerInch.PDF_TO_CSS_UNITS);return i[0]<=s&&i[1]<=s}const St=["butt","round","square"],Ct=["miter","round","bevel"],Tt={},Mt={};class CanvasGraphics{constructor(t,e,i,s,n,{optionalContentConfig:r,markedContentStack:a=null},o,l){this.ctx=t;this.current=new CanvasExtraState(this.ctx.canvas.width,this.ctx.canvas.height);this.stateStack=[];this.pendingClip=null;this.pendingEOFill=!1;this.res=null;this.xobjs=null;this.commonObjs=e;this.objs=i;this.canvasFactory=s;this.filterFactory=n;this.groupStack=[];this.processingType3=null;this.baseTransform=null;this.baseTransformStack=[];this.groupLevel=0;this.smaskStack=[];this.smaskCounter=0;this.tempSMask=null;this.suspendedCtx=null;this.contentVisible=!0;this.markedContentStack=a||[];this.optionalContentConfig=r;this.cachedCanvases=new CachedCanvases(this.canvasFactory);this.cachedPatterns=new Map;this.annotationCanvasMap=o;this.viewportScale=1;this.outputScaleX=1;this.outputScaleY=1;this.pageColors=l;this._cachedScaleForStroking=[-1,0];this._cachedGetSinglePixelWidth=null;this._cachedBitmapsMap=new Map}getObject(t,e=null){return"string"==typeof t?t.startsWith("g_")?this.commonObjs.get(t):this.objs.get(t):e}beginDrawing({transform:t,viewport:e,transparency:i=!1,background:s=null}){const n=this.ctx.canvas.width,r=this.ctx.canvas.height,a=this.ctx.fillStyle;this.ctx.fillStyle=s||"#ffffff";this.ctx.fillRect(0,0,n,r);this.ctx.fillStyle=a;if(i){const t=this.cachedCanvases.getCanvas("transparent",n,r);this.compositeCtx=this.ctx;this.transparentCanvas=t.canvas;this.ctx=t.context;this.ctx.save();this.ctx.transform(...getCurrentTransform(this.compositeCtx))}this.ctx.save();resetCtxToDefault(this.ctx);if(t){this.ctx.transform(...t);this.outputScaleX=t[0];this.outputScaleY=t[0]}this.ctx.transform(...e.transform);this.viewportScale=e.scale;this.baseTransform=getCurrentTransform(this.ctx)}executeOperatorList(t,e,i,s){const n=t.argsArray,r=t.fnArray;let a=e||0;const o=n.length;if(o===a)return a;const l=o-a>10&&"function"==typeof i,h=l?Date.now()+15:0;let c=0;const d=this.commonObjs,u=this.objs;let p;for(;;){if(void 0!==s&&a===s.nextBreakPoint){s.breakIt(a,i);return a}p=r[a];if(p!==K.dependency)this[p].apply(this,n[a]);else for(const t of n[a]){const e=t.startsWith("g_")?d:u;if(!e.has(t)){e.get(t,i);return a}}a++;if(a===o)return a;if(l&&++c>10){if(Date.now()>h){i();return a}c=0}}}#hi(){for(;this.stateStack.length||this.inSMaskMode;)this.restore();this.current.activeSMask=null;this.ctx.restore();if(this.transparentCanvas){this.ctx=this.compositeCtx;this.ctx.save();this.ctx.setTransform(1,0,0,1,0,0);this.ctx.drawImage(this.transparentCanvas,0,0);this.ctx.restore();this.transparentCanvas=null}}endDrawing(){this.#hi();this.cachedCanvases.clear();this.cachedPatterns.clear();for(const t of this._cachedBitmapsMap.values()){for(const e of t.values())"undefined"!=typeof HTMLCanvasElement&&e instanceof HTMLCanvasElement&&(e.width=e.height=0);t.clear()}this._cachedBitmapsMap.clear();this.#ci()}#ci(){if(this.pageColors){const t=this.filterFactory.addHCMFilter(this.pageColors.foreground,this.pageColors.background);if("none"!==t){const e=this.ctx.filter;this.ctx.filter=t;this.ctx.drawImage(this.ctx.canvas,0,0);this.ctx.filter=e}}}_scaleImage(t,e){const i=t.width??t.displayWidth,s=t.height??t.displayHeight;let n,r,a=Math.max(Math.hypot(e[0],e[1]),1),o=Math.max(Math.hypot(e[2],e[3]),1),l=i,h=s,c="prescale1";for(;a>2&&l>1||o>2&&h>1;){let e=l,i=h;if(a>2&&l>1){e=l>=16384?Math.floor(l/2)-1||1:Math.ceil(l/2);a/=l/e}if(o>2&&h>1){i=h>=16384?Math.floor(h/2)-1||1:Math.ceil(h)/2;o/=h/i}n=this.cachedCanvases.getCanvas(c,e,i);r=n.context;r.clearRect(0,0,e,i);r.drawImage(t,0,0,l,h,0,0,e,i);t=n.canvas;l=e;h=i;c="prescale1"===c?"prescale2":"prescale1"}return{img:t,paintWidth:l,paintHeight:h}}_createMaskCanvas(t){const e=this.ctx,{width:i,height:s}=t,n=this.current.fillColor,r=this.current.patternFill,a=getCurrentTransform(e);let o,l,h,c;if((t.bitmap||t.data)&&t.count>1){const e=t.bitmap||t.data.buffer;l=JSON.stringify(r?a:[a.slice(0,4),n]);o=this._cachedBitmapsMap.get(e);if(!o){o=new Map;this._cachedBitmapsMap.set(e,o)}const i=o.get(l);if(i&&!r){return{canvas:i,offsetX:Math.round(Math.min(a[0],a[2])+a[4]),offsetY:Math.round(Math.min(a[1],a[3])+a[5])}}h=i}if(!h){c=this.cachedCanvases.getCanvas("maskCanvas",i,s);putBinaryImageMask(c.context,t)}let d=Util.transform(a,[1/i,0,0,-1/s,0,0]);d=Util.transform(d,[1,0,0,1,0,-s]);const[u,p,g,f]=Util.getAxialAlignedBoundingBox([0,0,i,s],d),m=Math.round(g-u)||1,b=Math.round(f-p)||1,v=this.cachedCanvases.getCanvas("fillCanvas",m,b),y=v.context,w=u,A=p;y.translate(-w,-A);y.transform(...d);if(!h){h=this._scaleImage(c.canvas,getCurrentTransformInverse(y));h=h.img;o&&r&&o.set(l,h)}y.imageSmoothingEnabled=getImageSmoothingEnabled(getCurrentTransform(y),t.interpolate);drawImageAtIntegerCoords(y,h,0,0,h.width,h.height,0,0,i,s);y.globalCompositeOperation="source-in";const x=Util.transform(getCurrentTransformInverse(y),[1,0,0,1,-w,-A]);y.fillStyle=r?n.getPattern(e,this,x,yt):n;y.fillRect(0,0,i,s);if(o&&!r){this.cachedCanvases.delete("fillCanvas");o.set(l,v.canvas)}return{canvas:v.canvas,offsetX:Math.round(w),offsetY:Math.round(A)}}setLineWidth(t){t!==this.current.lineWidth&&(this._cachedScaleForStroking[0]=-1);this.current.lineWidth=t;this.ctx.lineWidth=t}setLineCap(t){this.ctx.lineCap=St[t]}setLineJoin(t){this.ctx.lineJoin=Ct[t]}setMiterLimit(t){this.ctx.miterLimit=t}setDash(t,e){const i=this.ctx;if(void 0!==i.setLineDash){i.setLineDash(t);i.lineDashOffset=e}}setRenderingIntent(t){}setFlatness(t){}setGState(t){for(const[e,i]of t)switch(e){case"LW":this.setLineWidth(i);break;case"LC":this.setLineCap(i);break;case"LJ":this.setLineJoin(i);break;case"ML":this.setMiterLimit(i);break;case"D":this.setDash(i[0],i[1]);break;case"RI":this.setRenderingIntent(i);break;case"FL":this.setFlatness(i);break;case"Font":this.setFont(i[0],i[1]);break;case"CA":this.current.strokeAlpha=i;break;case"ca":this.current.fillAlpha=i;this.ctx.globalAlpha=i;break;case"BM":this.ctx.globalCompositeOperation=i;break;case"SMask":this.current.activeSMask=i?this.tempSMask:null;this.tempSMask=null;this.checkSMaskState();break;case"TR":this.ctx.filter=this.current.transferMaps=this.filterFactory.addFilter(i)}}get inSMaskMode(){return!!this.suspendedCtx}checkSMaskState(){const t=this.inSMaskMode;this.current.activeSMask&&!t?this.beginSMaskMode():!this.current.activeSMask&&t&&this.endSMaskMode()}beginSMaskMode(){if(this.inSMaskMode)throw new Error("beginSMaskMode called while already in smask mode");const t=this.ctx.canvas.width,e=this.ctx.canvas.height,i="smaskGroupAt"+this.groupLevel,s=this.cachedCanvases.getCanvas(i,t,e);this.suspendedCtx=this.ctx;this.ctx=s.context;const n=this.ctx;n.setTransform(...getCurrentTransform(this.suspendedCtx));copyCtxState(this.suspendedCtx,n);!function mirrorContextOperations(t,e){if(t._removeMirroring)throw new Error("Context is already forwarding operations.");t.__originalSave=t.save;t.__originalRestore=t.restore;t.__originalRotate=t.rotate;t.__originalScale=t.scale;t.__originalTranslate=t.translate;t.__originalTransform=t.transform;t.__originalSetTransform=t.setTransform;t.__originalResetTransform=t.resetTransform;t.__originalClip=t.clip;t.__originalMoveTo=t.moveTo;t.__originalLineTo=t.lineTo;t.__originalBezierCurveTo=t.bezierCurveTo;t.__originalRect=t.rect;t.__originalClosePath=t.closePath;t.__originalBeginPath=t.beginPath;t._removeMirroring=()=>{t.save=t.__originalSave;t.restore=t.__originalRestore;t.rotate=t.__originalRotate;t.scale=t.__originalScale;t.translate=t.__originalTranslate;t.transform=t.__originalTransform;t.setTransform=t.__originalSetTransform;t.resetTransform=t.__originalResetTransform;t.clip=t.__originalClip;t.moveTo=t.__originalMoveTo;t.lineTo=t.__originalLineTo;t.bezierCurveTo=t.__originalBezierCurveTo;t.rect=t.__originalRect;t.closePath=t.__originalClosePath;t.beginPath=t.__originalBeginPath;delete t._removeMirroring};t.save=function ctxSave(){e.save();this.__originalSave()};t.restore=function ctxRestore(){e.restore();this.__originalRestore()};t.translate=function ctxTranslate(t,i){e.translate(t,i);this.__originalTranslate(t,i)};t.scale=function ctxScale(t,i){e.scale(t,i);this.__originalScale(t,i)};t.transform=function ctxTransform(t,i,s,n,r,a){e.transform(t,i,s,n,r,a);this.__originalTransform(t,i,s,n,r,a)};t.setTransform=function ctxSetTransform(t,i,s,n,r,a){e.setTransform(t,i,s,n,r,a);this.__originalSetTransform(t,i,s,n,r,a)};t.resetTransform=function ctxResetTransform(){e.resetTransform();this.__originalResetTransform()};t.rotate=function ctxRotate(t){e.rotate(t);this.__originalRotate(t)};t.clip=function ctxRotate(t){e.clip(t);this.__originalClip(t)};t.moveTo=function(t,i){e.moveTo(t,i);this.__originalMoveTo(t,i)};t.lineTo=function(t,i){e.lineTo(t,i);this.__originalLineTo(t,i)};t.bezierCurveTo=function(t,i,s,n,r,a){e.bezierCurveTo(t,i,s,n,r,a);this.__originalBezierCurveTo(t,i,s,n,r,a)};t.rect=function(t,i,s,n){e.rect(t,i,s,n);this.__originalRect(t,i,s,n)};t.closePath=function(){e.closePath();this.__originalClosePath()};t.beginPath=function(){e.beginPath();this.__originalBeginPath()}}(n,this.suspendedCtx);this.setGState([["BM","source-over"],["ca",1],["CA",1]])}endSMaskMode(){if(!this.inSMaskMode)throw new Error("endSMaskMode called while not in smask mode");this.ctx._removeMirroring();copyCtxState(this.ctx,this.suspendedCtx);this.ctx=this.suspendedCtx;this.suspendedCtx=null}compose(t){if(!this.current.activeSMask)return;if(t){t[0]=Math.floor(t[0]);t[1]=Math.floor(t[1]);t[2]=Math.ceil(t[2]);t[3]=Math.ceil(t[3])}else t=[0,0,this.ctx.canvas.width,this.ctx.canvas.height];const e=this.current.activeSMask,i=this.suspendedCtx;this.composeSMask(i,e,this.ctx,t);this.ctx.save();this.ctx.setTransform(1,0,0,1,0,0);this.ctx.clearRect(0,0,this.ctx.canvas.width,this.ctx.canvas.height);this.ctx.restore()}composeSMask(t,e,i,s){const n=s[0],r=s[1],a=s[2]-n,o=s[3]-r;if(0!==a&&0!==o){this.genericComposeSMask(e.context,i,a,o,e.subtype,e.backdrop,e.transferMap,n,r,e.offsetX,e.offsetY);t.save();t.globalAlpha=1;t.globalCompositeOperation="source-over";t.setTransform(1,0,0,1,0,0);t.drawImage(i.canvas,0,0);t.restore()}}genericComposeSMask(t,e,i,s,n,r,a,o,l,h,c){let d=t.canvas,u=o-h,p=l-c;if(r){const e=Util.makeHexColor(...r);if(u<0||p<0||u+i>d.width||p+s>d.height){const t=this.cachedCanvases.getCanvas("maskExtension",i,s),n=t.context;n.drawImage(d,-u,-p);n.globalCompositeOperation="destination-atop";n.fillStyle=e;n.fillRect(0,0,i,s);n.globalCompositeOperation="source-over";d=t.canvas;u=p=0}else{t.save();t.globalAlpha=1;t.setTransform(1,0,0,1,0,0);const n=new Path2D;n.rect(u,p,i,s);t.clip(n);t.globalCompositeOperation="destination-atop";t.fillStyle=e;t.fillRect(u,p,i,s);t.restore()}}e.save();e.globalAlpha=1;e.setTransform(1,0,0,1,0,0);"Alpha"===n&&a?e.filter=this.filterFactory.addAlphaFilter(a):"Luminosity"===n&&(e.filter=this.filterFactory.addLuminosityFilter(a));const g=new Path2D;g.rect(o,l,i,s);e.clip(g);e.globalCompositeOperation="destination-in";e.drawImage(d,u,p,i,s,o,l,i,s);e.restore()}save(){if(this.inSMaskMode){copyCtxState(this.ctx,this.suspendedCtx);this.suspendedCtx.save()}else this.ctx.save();const t=this.current;this.stateStack.push(t);this.current=t.clone()}restore(){0===this.stateStack.length&&this.inSMaskMode&&this.endSMaskMode();if(0!==this.stateStack.length){this.current=this.stateStack.pop();if(this.inSMaskMode){this.suspendedCtx.restore();copyCtxState(this.suspendedCtx,this.ctx)}else this.ctx.restore();this.checkSMaskState();this.pendingClip=null;this._cachedScaleForStroking[0]=-1;this._cachedGetSinglePixelWidth=null}}transform(t,e,i,s,n,r){this.ctx.transform(t,e,i,s,n,r);this._cachedScaleForStroking[0]=-1;this._cachedGetSinglePixelWidth=null}constructPath(t,e,i){const s=this.ctx,n=this.current;let r,a,o=n.x,l=n.y;const h=getCurrentTransform(s),c=0===h[0]&&0===h[3]||0===h[1]&&0===h[2],d=c?i.slice(0):null;for(let i=0,u=0,p=t.length;i<p;i++)switch(0|t[i]){case K.rectangle:o=e[u++];l=e[u++];const t=e[u++],i=e[u++],p=o+t,g=l+i;s.moveTo(o,l);if(0===t||0===i)s.lineTo(p,g);else{s.lineTo(p,l);s.lineTo(p,g);s.lineTo(o,g)}c||n.updateRectMinMax(h,[o,l,p,g]);s.closePath();break;case K.moveTo:o=e[u++];l=e[u++];s.moveTo(o,l);c||n.updatePathMinMax(h,o,l);break;case K.lineTo:o=e[u++];l=e[u++];s.lineTo(o,l);c||n.updatePathMinMax(h,o,l);break;case K.curveTo:r=o;a=l;o=e[u+4];l=e[u+5];s.bezierCurveTo(e[u],e[u+1],e[u+2],e[u+3],o,l);n.updateCurvePathMinMax(h,r,a,e[u],e[u+1],e[u+2],e[u+3],o,l,d);u+=6;break;case K.curveTo2:r=o;a=l;s.bezierCurveTo(o,l,e[u],e[u+1],e[u+2],e[u+3]);n.updateCurvePathMinMax(h,r,a,o,l,e[u],e[u+1],e[u+2],e[u+3],d);o=e[u+2];l=e[u+3];u+=4;break;case K.curveTo3:r=o;a=l;o=e[u+2];l=e[u+3];s.bezierCurveTo(e[u],e[u+1],o,l,o,l);n.updateCurvePathMinMax(h,r,a,e[u],e[u+1],o,l,o,l,d);u+=4;break;case K.closePath:s.closePath()}c&&n.updateScalingPathMinMax(h,d);n.setCurrentPoint(o,l)}closePath(){this.ctx.closePath()}stroke(t=!0){const e=this.ctx,i=this.current.strokeColor;e.globalAlpha=this.current.strokeAlpha;if(this.contentVisible)if("object"==typeof i&&i?.getPattern){e.save();e.strokeStyle=i.getPattern(e,this,getCurrentTransformInverse(e),wt);this.rescaleAndStroke(!1);e.restore()}else this.rescaleAndStroke(!0);t&&this.consumePath(this.current.getClippedPathBoundingBox());e.globalAlpha=this.current.fillAlpha}closeStroke(){this.closePath();this.stroke()}fill(t=!0){const e=this.ctx,i=this.current.fillColor;let s=!1;if(this.current.patternFill){e.save();e.fillStyle=i.getPattern(e,this,getCurrentTransformInverse(e),yt);s=!0}const n=this.current.getClippedPathBoundingBox();if(this.contentVisible&&null!==n)if(this.pendingEOFill){e.fill("evenodd");this.pendingEOFill=!1}else e.fill();s&&e.restore();t&&this.consumePath(n)}eoFill(){this.pendingEOFill=!0;this.fill()}fillStroke(){this.fill(!1);this.stroke(!1);this.consumePath()}eoFillStroke(){this.pendingEOFill=!0;this.fillStroke()}closeFillStroke(){this.closePath();this.fillStroke()}closeEOFillStroke(){this.pendingEOFill=!0;this.closePath();this.fillStroke()}endPath(){this.consumePath()}clip(){this.pendingClip=Tt}eoClip(){this.pendingClip=Mt}beginText(){this.current.textMatrix=s;this.current.textMatrixScale=1;this.current.x=this.current.lineX=0;this.current.y=this.current.lineY=0}endText(){const t=this.pendingTextPaths,e=this.ctx;if(void 0===t){e.beginPath();return}const i=new Path2D,s=e.getTransform().invertSelf();for(const{transform:e,x:n,y:r,fontSize:a,path:o}of t)i.addPath(o,new DOMMatrix(e).preMultiplySelf(s).translate(n,r).scale(a,-a));e.clip(i);e.beginPath();delete this.pendingTextPaths}setCharSpacing(t){this.current.charSpacing=t}setWordSpacing(t){this.current.wordSpacing=t}setHScale(t){this.current.textHScale=t/100}setLeading(t){this.current.leading=-t}setFont(t,e){const i=this.commonObjs.get(t),s=this.current;if(!i)throw new Error(`Can't find font for ${t}`);s.fontMatrix=i.fontMatrix||n;0!==s.fontMatrix[0]&&0!==s.fontMatrix[3]||warn("Invalid font matrix for font "+t);if(e<0){e=-e;s.fontDirection=-1}else s.fontDirection=1;this.current.font=i;this.current.fontSize=e;if(i.isType3Font)return;const r=i.loadedName||"sans-serif",a=i.systemFontInfo?.css||`"${r}", ${i.fallbackName}`;let o="normal";i.black?o="900":i.bold&&(o="bold");const l=i.italic?"italic":"normal";let h=e;e<16?h=16:e>100&&(h=100);this.current.fontSizeScale=e/h;this.ctx.font=`${l} ${o} ${h}px ${a}`}setTextRenderingMode(t){this.current.textRenderingMode=t}setTextRise(t){this.current.textRise=t}moveText(t,e){this.current.x=this.current.lineX+=t;this.current.y=this.current.lineY+=e}setLeadingMoveText(t,e){this.setLeading(-e);this.moveText(t,e)}setTextMatrix(t,e,i,s,n,r){this.current.textMatrix=[t,e,i,s,n,r];this.current.textMatrixScale=Math.hypot(t,e);this.current.x=this.current.lineX=0;this.current.y=this.current.lineY=0}nextLine(){this.moveText(0,this.current.leading)}#di(t,e,i){const s=new Path2D;s.addPath(t,new DOMMatrix(i).invertSelf().multiplySelf(e));return s}paintChar(t,e,i,s,n){const r=this.ctx,a=this.current,o=a.font,l=a.textRenderingMode,h=a.fontSize/a.fontSizeScale,c=l&x,d=!!(l&_),u=a.patternFill&&!o.missingFile,p=a.patternStroke&&!o.missingFile;let g;(o.disableFontFace||d||u||p)&&(g=o.getPathGenerator(this.commonObjs,t));if(o.disableFontFace||u||p){r.save();r.translate(e,i);r.scale(h,-h);if(c===v||c===w)if(s){const t=r.getTransform();r.setTransform(...s);r.fill(this.#di(g,t,s))}else r.fill(g);if(c===y||c===w)if(n){const t=r.getTransform();r.setTransform(...n);r.stroke(this.#di(g,t,n))}else{r.lineWidth/=h;r.stroke(g)}r.restore()}else{c!==v&&c!==w||r.fillText(t,e,i);c!==y&&c!==w||r.strokeText(t,e,i)}if(d){(this.pendingTextPaths||=[]).push({transform:getCurrentTransform(r),x:e,y:i,fontSize:h,path:g})}}get isFontSubpixelAAEnabled(){const{context:t}=this.cachedCanvases.getCanvas("isFontSubpixelAAEnabled",10,10);t.scale(1.5,1);t.fillText("I",0,10);const e=t.getImageData(0,0,10,10).data;let i=!1;for(let t=3;t<e.length;t+=4)if(e[t]>0&&e[t]<255){i=!0;break}return shadow(this,"isFontSubpixelAAEnabled",i)}showText(t){const e=this.current,i=e.font;if(i.isType3Font)return this.showType3Text(t);const s=e.fontSize;if(0===s)return;const n=this.ctx,r=e.fontSizeScale,a=e.charSpacing,o=e.wordSpacing,l=e.fontDirection,h=e.textHScale*l,c=t.length,d=i.vertical,u=d?1:-1,p=i.defaultVMetrics,g=s*e.fontMatrix[0],f=e.textRenderingMode===v&&!i.disableFontFace&&!e.patternFill;n.save();n.transform(...e.textMatrix);n.translate(e.x,e.y+e.textRise);l>0?n.scale(h,-1):n.scale(h,1);let m,b;if(e.patternFill){n.save();const t=e.fillColor.getPattern(n,this,getCurrentTransformInverse(n),yt);m=getCurrentTransform(n);n.restore();n.fillStyle=t}if(e.patternStroke){n.save();const t=e.strokeColor.getPattern(n,this,getCurrentTransformInverse(n),wt);b=getCurrentTransform(n);n.restore();n.strokeStyle=t}let A=e.lineWidth;const _=e.textMatrixScale;if(0===_||0===A){const t=e.textRenderingMode&x;t!==y&&t!==w||(A=this.getSinglePixelWidth())}else A/=_;if(1!==r){n.scale(r,r);A/=r}n.lineWidth=A;if(i.isInvalidPDFjsFont){const i=[];let s=0;for(const e of t){i.push(e.unicode);s+=e.width}n.fillText(i.join(""),0,0);e.x+=s*g*h;n.restore();this.compose();return}let E,S=0;for(E=0;E<c;++E){const e=t[E];if("number"==typeof e){S+=u*e*s/1e3;continue}let h=!1;const c=(e.isSpace?o:0)+a,v=e.fontChar,y=e.accent;let w,A,x=e.width;if(d){const t=e.vmetric||p,i=-(e.vmetric?t[1]:.5*x)*g,s=t[2]*g;x=t?-t[0]:x;w=i/r;A=(S+s)/r}else{w=S/r;A=0}if(i.remeasure&&x>0){const t=1e3*n.measureText(v).width/s*r;if(x<t&&this.isFontSubpixelAAEnabled){const e=x/t;h=!0;n.save();n.scale(e,1);w/=e}else x!==t&&(w+=(x-t)/2e3*s/r)}if(this.contentVisible&&(e.isInFont||i.missingFile))if(f&&!y)n.fillText(v,w,A);else{this.paintChar(v,w,A,m,b);if(y){const t=w+s*y.offset.x/r,e=A-s*y.offset.y/r;this.paintChar(y.fontChar,t,e,m,b)}}S+=d?x*g-c*l:x*g+c*l;h&&n.restore()}d?e.y-=S:e.x+=S*h;n.restore();this.compose()}showType3Text(t){const e=this.ctx,i=this.current,s=i.font,r=i.fontSize,a=i.fontDirection,o=s.vertical?1:-1,l=i.charSpacing,h=i.wordSpacing,c=i.textHScale*a,d=i.fontMatrix||n,u=t.length;let p,g,f,m;if(!(i.textRenderingMode===A)&&0!==r){this._cachedScaleForStroking[0]=-1;this._cachedGetSinglePixelWidth=null;e.save();e.transform(...i.textMatrix);e.translate(i.x,i.y);e.scale(c,a);for(p=0;p<u;++p){g=t[p];if("number"==typeof g){m=o*g*r/1e3;this.ctx.translate(m,0);i.x+=m*c;continue}const n=(g.isSpace?h:0)+l,a=s.charProcOperatorList[g.operatorListId];if(!a){warn(`Type3 character "${g.operatorListId}" is not available.`);continue}if(this.contentVisible){this.processingType3=g;this.save();e.scale(r,r);e.transform(...d);this.executeOperatorList(a);this.restore()}f=Util.applyTransform([g.width,0],d)[0]*r+n;e.translate(f,0);i.x+=f*c}e.restore();this.processingType3=null}}setCharWidth(t,e){}setCharWidthAndBounds(t,e,i,s,n,r){this.ctx.rect(i,s,n-i,r-s);this.ctx.clip();this.endPath()}getColorN_Pattern(t){let e;if("TilingPattern"===t[0]){const i=t[1],s=this.baseTransform||getCurrentTransform(this.ctx),n={createCanvasGraphics:t=>new CanvasGraphics(t,this.commonObjs,this.objs,this.canvasFactory,this.filterFactory,{optionalContentConfig:this.optionalContentConfig,markedContentStack:this.markedContentStack})};e=new TilingPattern(t,i,this.ctx,n,s)}else e=this._getPattern(t[1],t[2]);return e}setStrokeColorN(){this.current.strokeColor=this.getColorN_Pattern(arguments);this.current.patternStroke=!0}setFillColorN(){this.current.fillColor=this.getColorN_Pattern(arguments);this.current.patternFill=!0}setStrokeRGBColor(t,e,i){this.ctx.strokeStyle=this.current.strokeColor=Util.makeHexColor(t,e,i);this.current.patternStroke=!1}setStrokeTransparent(){this.ctx.strokeStyle=this.current.strokeColor="transparent";this.current.patternStroke=!1}setFillRGBColor(t,e,i){this.ctx.fillStyle=this.current.fillColor=Util.makeHexColor(t,e,i);this.current.patternFill=!1}setFillTransparent(){this.ctx.fillStyle=this.current.fillColor="transparent";this.current.patternFill=!1}_getPattern(t,e=null){let i;if(this.cachedPatterns.has(t))i=this.cachedPatterns.get(t);else{i=function getShadingPattern(t){switch(t[0]){case"RadialAxial":return new RadialAxialShadingPattern(t);case"Mesh":return new MeshShadingPattern(t);case"Dummy":return new DummyShadingPattern}throw new Error(`Unknown IR type: ${t[0]}`)}(this.getObject(t));this.cachedPatterns.set(t,i)}e&&(i.matrix=e);return i}shadingFill(t){if(!this.contentVisible)return;const e=this.ctx;this.save();const i=this._getPattern(t);e.fillStyle=i.getPattern(e,this,getCurrentTransformInverse(e),At);const s=getCurrentTransformInverse(e);if(s){const{width:t,height:i}=e.canvas,[n,r,a,o]=Util.getAxialAlignedBoundingBox([0,0,t,i],s);this.ctx.fillRect(n,r,a-n,o-r)}else this.ctx.fillRect(-1e10,-1e10,2e10,2e10);this.compose(this.current.getClippedPathBoundingBox());this.restore()}beginInlineImage(){unreachable("Should not call beginInlineImage")}beginImageData(){unreachable("Should not call beginImageData")}paintFormXObjectBegin(t,e){if(this.contentVisible){this.save();this.baseTransformStack.push(this.baseTransform);t&&this.transform(...t);this.baseTransform=getCurrentTransform(this.ctx);if(e){const t=e[2]-e[0],i=e[3]-e[1];this.ctx.rect(e[0],e[1],t,i);this.current.updateRectMinMax(getCurrentTransform(this.ctx),e);this.clip();this.endPath()}}}paintFormXObjectEnd(){if(this.contentVisible){this.restore();this.baseTransform=this.baseTransformStack.pop()}}beginGroup(t){if(!this.contentVisible)return;this.save();if(this.inSMaskMode){this.endSMaskMode();this.current.activeSMask=null}const e=this.ctx;t.isolated||info("TODO: Support non-isolated groups.");t.knockout&&warn("Knockout groups not supported.");const i=getCurrentTransform(e);t.matrix&&e.transform(...t.matrix);if(!t.bbox)throw new Error("Bounding box is required.");let s=Util.getAxialAlignedBoundingBox(t.bbox,getCurrentTransform(e));const n=[0,0,e.canvas.width,e.canvas.height];s=Util.intersect(s,n)||[0,0,0,0];const r=Math.floor(s[0]),a=Math.floor(s[1]),o=Math.max(Math.ceil(s[2])-r,1),l=Math.max(Math.ceil(s[3])-a,1);this.current.startNewPathAndClipBox([0,0,o,l]);let h="groupAt"+this.groupLevel;t.smask&&(h+="_smask_"+this.smaskCounter++%2);const c=this.cachedCanvases.getCanvas(h,o,l),d=c.context;d.translate(-r,-a);d.transform(...i);if(t.smask)this.smaskStack.push({canvas:c.canvas,context:d,offsetX:r,offsetY:a,subtype:t.smask.subtype,backdrop:t.smask.backdrop,transferMap:t.smask.transferMap||null,startTransformInverse:null});else{e.setTransform(1,0,0,1,0,0);e.translate(r,a);e.save()}copyCtxState(e,d);this.ctx=d;this.setGState([["BM","source-over"],["ca",1],["CA",1]]);this.groupStack.push(e);this.groupLevel++}endGroup(t){if(!this.contentVisible)return;this.groupLevel--;const e=this.ctx,i=this.groupStack.pop();this.ctx=i;this.ctx.imageSmoothingEnabled=!1;if(t.smask){this.tempSMask=this.smaskStack.pop();this.restore()}else{this.ctx.restore();const t=getCurrentTransform(this.ctx);this.restore();this.ctx.save();this.ctx.setTransform(...t);const i=Util.getAxialAlignedBoundingBox([0,0,e.canvas.width,e.canvas.height],t);this.ctx.drawImage(e.canvas,0,0);this.ctx.restore();this.compose(i)}}beginAnnotation(t,e,i,s,n){this.#hi();resetCtxToDefault(this.ctx);this.ctx.save();this.save();this.baseTransform&&this.ctx.setTransform(...this.baseTransform);if(e){const s=e[2]-e[0],r=e[3]-e[1];if(n&&this.annotationCanvasMap){(i=i.slice())[4]-=e[0];i[5]-=e[1];(e=e.slice())[0]=e[1]=0;e[2]=s;e[3]=r;const[n,a]=Util.singularValueDecompose2dScale(getCurrentTransform(this.ctx)),{viewportScale:o}=this,l=Math.ceil(s*this.outputScaleX*o),h=Math.ceil(r*this.outputScaleY*o);this.annotationCanvas=this.canvasFactory.create(l,h);const{canvas:c,context:d}=this.annotationCanvas;this.annotationCanvasMap.set(t,c);this.annotationCanvas.savedCtx=this.ctx;this.ctx=d;this.ctx.save();this.ctx.setTransform(n,0,0,-a,0,r*a);resetCtxToDefault(this.ctx)}else{resetCtxToDefault(this.ctx);this.endPath();this.ctx.rect(e[0],e[1],s,r);this.ctx.clip();this.ctx.beginPath()}}this.current=new CanvasExtraState(this.ctx.canvas.width,this.ctx.canvas.height);this.transform(...i);this.transform(...s)}endAnnotation(){if(this.annotationCanvas){this.ctx.restore();this.#ci();this.ctx=this.annotationCanvas.savedCtx;delete this.annotationCanvas.savedCtx;delete this.annotationCanvas}}paintImageMaskXObject(t){if(!this.contentVisible)return;const e=t.count;(t=this.getObject(t.data,t)).count=e;const i=this.ctx,s=this.processingType3;if(s){void 0===s.compiled&&(s.compiled=function compileType3Glyph(t){const{width:e,height:i}=t;if(e>1e3||i>1e3)return null;const s=new Uint8Array([0,2,4,0,1,0,5,4,8,10,0,8,0,2,1,0]),n=e+1;let r,a,o,l=new Uint8Array(n*(i+1));const h=e+7&-8;let c=new Uint8Array(h*i),d=0;for(const e of t.data){let t=128;for(;t>0;){c[d++]=e&t?0:255;t>>=1}}let u=0;d=0;if(0!==c[d]){l[0]=1;++u}for(a=1;a<e;a++){if(c[d]!==c[d+1]){l[a]=c[d]?2:1;++u}d++}if(0!==c[d]){l[a]=2;++u}for(r=1;r<i;r++){d=r*h;o=r*n;if(c[d-h]!==c[d]){l[o]=c[d]?1:8;++u}let t=(c[d]?4:0)+(c[d-h]?8:0);for(a=1;a<e;a++){t=(t>>2)+(c[d+1]?4:0)+(c[d-h+1]?8:0);if(s[t]){l[o+a]=s[t];++u}d++}if(c[d-h]!==c[d]){l[o+a]=c[d]?2:4;++u}if(u>1e3)return null}d=h*(i-1);o=r*n;if(0!==c[d]){l[o]=8;++u}for(a=1;a<e;a++){if(c[d]!==c[d+1]){l[o+a]=c[d]?4:8;++u}d++}if(0!==c[d]){l[o+a]=4;++u}if(u>1e3)return null;const p=new Int32Array([0,n,-1,0,-n,0,0,0,1]),g=new Path2D;for(r=0;u&&r<=i;r++){let t=r*n;const i=t+e;for(;t<i&&!l[t];)t++;if(t===i)continue;g.moveTo(t%n,r);const s=t;let a=l[t];do{const e=p[a];do{t+=e}while(!l[t]);const i=l[t];if(5!==i&&10!==i){a=i;l[t]=0}else{a=i&51*a>>4;l[t]&=a>>2|a<<2}g.lineTo(t%n,t/n|0);l[t]||--u}while(s!==t);--r}c=null;l=null;return function(t){t.save();t.scale(1/e,-1/i);t.translate(0,-i);t.fill(g);t.beginPath();t.restore()}}(t));if(s.compiled){s.compiled(i);return}}const n=this._createMaskCanvas(t),r=n.canvas;i.save();i.setTransform(1,0,0,1,0,0);i.drawImage(r,n.offsetX,n.offsetY);i.restore();this.compose()}paintImageMaskXObjectRepeat(t,e,i=0,s=0,n,r){if(!this.contentVisible)return;t=this.getObject(t.data,t);const a=this.ctx;a.save();const o=getCurrentTransform(a);a.transform(e,i,s,n,0,0);const l=this._createMaskCanvas(t);a.setTransform(1,0,0,1,l.offsetX-o[4],l.offsetY-o[5]);for(let t=0,h=r.length;t<h;t+=2){const h=Util.transform(o,[e,i,s,n,r[t],r[t+1]]),[c,d]=Util.applyTransform([0,0],h);a.drawImage(l.canvas,c,d)}a.restore();this.compose()}paintImageMaskXObjectGroup(t){if(!this.contentVisible)return;const e=this.ctx,i=this.current.fillColor,s=this.current.patternFill;for(const n of t){const{data:t,width:r,height:a,transform:o}=n,l=this.cachedCanvases.getCanvas("maskCanvas",r,a),h=l.context;h.save();putBinaryImageMask(h,this.getObject(t,n));h.globalCompositeOperation="source-in";h.fillStyle=s?i.getPattern(h,this,getCurrentTransformInverse(e),yt):i;h.fillRect(0,0,r,a);h.restore();e.save();e.transform(...o);e.scale(1,-1);drawImageAtIntegerCoords(e,l.canvas,0,0,r,a,0,-1,1,1);e.restore()}this.compose()}paintImageXObject(t){if(!this.contentVisible)return;const e=this.getObject(t);e?this.paintInlineImageXObject(e):warn("Dependent image isn't ready yet")}paintImageXObjectRepeat(t,e,i,s){if(!this.contentVisible)return;const n=this.getObject(t);if(!n){warn("Dependent image isn't ready yet");return}const r=n.width,a=n.height,o=[];for(let t=0,n=s.length;t<n;t+=2)o.push({transform:[e,0,0,i,s[t],s[t+1]],x:0,y:0,w:r,h:a});this.paintInlineImageXObjectGroup(n,o)}applyTransferMapsToCanvas(t){if("none"!==this.current.transferMaps){t.filter=this.current.transferMaps;t.drawImage(t.canvas,0,0);t.filter="none"}return t.canvas}applyTransferMapsToBitmap(t){if("none"===this.current.transferMaps)return t.bitmap;const{bitmap:e,width:i,height:s}=t,n=this.cachedCanvases.getCanvas("inlineImage",i,s),r=n.context;r.filter=this.current.transferMaps;r.drawImage(e,0,0);r.filter="none";return n.canvas}paintInlineImageXObject(t){if(!this.contentVisible)return;const e=t.width,s=t.height,n=this.ctx;this.save();if(!i){const{filter:t}=n;"none"!==t&&""!==t&&(n.filter="none")}n.scale(1/e,-1/s);let r;if(t.bitmap)r=this.applyTransferMapsToBitmap(t);else if("function"==typeof HTMLElement&&t instanceof HTMLElement||!t.data)r=t;else{const i=this.cachedCanvases.getCanvas("inlineImage",e,s).context;putBinaryImageData(i,t);r=this.applyTransferMapsToCanvas(i)}const a=this._scaleImage(r,getCurrentTransformInverse(n));n.imageSmoothingEnabled=getImageSmoothingEnabled(getCurrentTransform(n),t.interpolate);drawImageAtIntegerCoords(n,a.img,0,0,a.paintWidth,a.paintHeight,0,-s,e,s);this.compose();this.restore()}paintInlineImageXObjectGroup(t,e){if(!this.contentVisible)return;const i=this.ctx;let s;if(t.bitmap)s=t.bitmap;else{const e=t.width,i=t.height,n=this.cachedCanvases.getCanvas("inlineImage",e,i).context;putBinaryImageData(n,t);s=this.applyTransferMapsToCanvas(n)}for(const t of e){i.save();i.transform(...t.transform);i.scale(1,-1);drawImageAtIntegerCoords(i,s,t.x,t.y,t.w,t.h,0,-1,1,1);i.restore()}this.compose()}paintSolidColorImageMask(){if(this.contentVisible){this.ctx.fillRect(0,0,1,1);this.compose()}}markPoint(t){}markPointProps(t,e){}beginMarkedContent(t){this.markedContentStack.push({visible:!0})}beginMarkedContentProps(t,e){"OC"===t?this.markedContentStack.push({visible:this.optionalContentConfig.isVisible(e)}):this.markedContentStack.push({visible:!0});this.contentVisible=this.isContentVisible()}endMarkedContent(){this.markedContentStack.pop();this.contentVisible=this.isContentVisible()}beginCompat(){}endCompat(){}consumePath(t){const e=this.current.isEmptyClip();this.pendingClip&&this.current.updateClipFromPath();this.pendingClip||this.compose(t);const i=this.ctx;if(this.pendingClip){e||(this.pendingClip===Mt?i.clip("evenodd"):i.clip());this.pendingClip=null}this.current.startNewPathAndClipBox(this.current.clipBox);i.beginPath()}getSinglePixelWidth(){if(!this._cachedGetSinglePixelWidth){const t=getCurrentTransform(this.ctx);if(0===t[1]&&0===t[2])this._cachedGetSinglePixelWidth=1/Math.min(Math.abs(t[0]),Math.abs(t[3]));else{const e=Math.abs(t[0]*t[3]-t[2]*t[1]),i=Math.hypot(t[0],t[2]),s=Math.hypot(t[1],t[3]);this._cachedGetSinglePixelWidth=Math.max(i,s)/e}}return this._cachedGetSinglePixelWidth}getScaleForStroking(){if(-1===this._cachedScaleForStroking[0]){const{lineWidth:t}=this.current,{a:e,b:i,c:s,d:n}=this.ctx.getTransform();let r,a;if(0===i&&0===s){const i=Math.abs(e),s=Math.abs(n);if(i===s)if(0===t)r=a=1/i;else{const e=i*t;r=a=e<1?1/e:1}else if(0===t){r=1/i;a=1/s}else{const e=i*t,n=s*t;r=e<1?1/e:1;a=n<1?1/n:1}}else{const o=Math.abs(e*n-i*s),l=Math.hypot(e,i),h=Math.hypot(s,n);if(0===t){r=h/o;a=l/o}else{const e=t*o;r=h>e?h/e:1;a=l>e?l/e:1}}this._cachedScaleForStroking[0]=r;this._cachedScaleForStroking[1]=a}return this._cachedScaleForStroking}rescaleAndStroke(t){const{ctx:e}=this,{lineWidth:i}=this.current,[s,n]=this.getScaleForStroking();e.lineWidth=i||1;if(1===s&&1===n){e.stroke();return}const r=e.getLineDash();t&&e.save();e.scale(s,n);if(r.length>0){const t=Math.max(s,n);e.setLineDash(r.map((e=>e/t)));e.lineDashOffset/=t}e.stroke();t&&e.restore()}isContentVisible(){for(let t=this.markedContentStack.length-1;t>=0;t--)if(!this.markedContentStack[t].visible)return!1;return!0}}for(const t in K)void 0!==CanvasGraphics.prototype[t]&&(CanvasGraphics.prototype[K[t]]=CanvasGraphics.prototype[t]);class GlobalWorkerOptions{static#ui=null;static#pi="";static get workerPort(){return this.#ui}static set workerPort(t){if(!("undefined"!=typeof Worker&&t instanceof Worker)&&null!==t)throw new Error("Invalid `workerPort` type.");this.#ui=t}static get workerSrc(){return this.#pi}static set workerSrc(t){if("string"!=typeof t)throw new Error("Invalid `workerSrc` type.");this.#pi=t}}class Metadata{#gi;#fi;constructor({parsedData:t,rawData:e}){this.#gi=t;this.#fi=e}getRaw(){return this.#fi}get(t){return this.#gi.get(t)??null}getAll(){return objectFromMap(this.#gi)}has(t){return this.#gi.has(t)}}const Pt=Symbol("INTERNAL");class OptionalContentGroup{#mi=!1;#bi=!1;#vi=!1;#yi=!0;constructor(t,{name:e,intent:i,usage:s,rbGroups:n}){this.#mi=!!(t&o);this.#bi=!!(t&l);this.name=e;this.intent=i;this.usage=s;this.rbGroups=n}get visible(){if(this.#vi)return this.#yi;if(!this.#yi)return!1;const{print:t,view:e}=this.usage;return this.#mi?"OFF"!==e?.viewState:!this.#bi||"OFF"!==t?.printState}_setVisible(t,e,i=!1){t!==Pt&&unreachable("Internal method `_setVisible` called.");this.#vi=i;this.#yi=e}}class OptionalContentConfig{#wi=null;#Ai=new Map;#xi=null;#_i=null;constructor(t,e=o){this.renderingIntent=e;this.name=null;this.creator=null;if(null!==t){this.name=t.name;this.creator=t.creator;this.#_i=t.order;for(const i of t.groups)this.#Ai.set(i.id,new OptionalContentGroup(e,i));if("OFF"===t.baseState)for(const t of this.#Ai.values())t._setVisible(Pt,!1);for(const e of t.on)this.#Ai.get(e)._setVisible(Pt,!0);for(const e of t.off)this.#Ai.get(e)._setVisible(Pt,!1);this.#xi=this.getHash()}}#Ei(t){const e=t.length;if(e<2)return!0;const i=t[0];for(let s=1;s<e;s++){const e=t[s];let n;if(Array.isArray(e))n=this.#Ei(e);else{if(!this.#Ai.has(e)){warn(`Optional content group not found: ${e}`);return!0}n=this.#Ai.get(e).visible}switch(i){case"And":if(!n)return!1;break;case"Or":if(n)return!0;break;case"Not":return!n;default:return!0}}return"And"===i}isVisible(t){if(0===this.#Ai.size)return!0;if(!t){info("Optional content group not defined.");return!0}if("OCG"===t.type){if(!this.#Ai.has(t.id)){warn(`Optional content group not found: ${t.id}`);return!0}return this.#Ai.get(t.id).visible}if("OCMD"===t.type){if(t.expression)return this.#Ei(t.expression);if(!t.policy||"AnyOn"===t.policy){for(const e of t.ids){if(!this.#Ai.has(e)){warn(`Optional content group not found: ${e}`);return!0}if(this.#Ai.get(e).visible)return!0}return!1}if("AllOn"===t.policy){for(const e of t.ids){if(!this.#Ai.has(e)){warn(`Optional content group not found: ${e}`);return!0}if(!this.#Ai.get(e).visible)return!1}return!0}if("AnyOff"===t.policy){for(const e of t.ids){if(!this.#Ai.has(e)){warn(`Optional content group not found: ${e}`);return!0}if(!this.#Ai.get(e).visible)return!0}return!1}if("AllOff"===t.policy){for(const e of t.ids){if(!this.#Ai.has(e)){warn(`Optional content group not found: ${e}`);return!0}if(this.#Ai.get(e).visible)return!1}return!0}warn(`Unknown optional content policy ${t.policy}.`);return!0}warn(`Unknown group type ${t.type}.`);return!0}setVisibility(t,e=!0,i=!0){const s=this.#Ai.get(t);if(s){if(i&&e&&s.rbGroups.length)for(const e of s.rbGroups)for(const i of e)i!==t&&this.#Ai.get(i)?._setVisible(Pt,!1,!0);s._setVisible(Pt,!!e,!0);this.#wi=null}else warn(`Optional content group not found: ${t}`)}setOCGState({state:t,preserveRB:e}){let i;for(const s of t){switch(s){case"ON":case"OFF":case"Toggle":i=s;continue}const t=this.#Ai.get(s);if(t)switch(i){case"ON":this.setVisibility(s,!0,e);break;case"OFF":this.setVisibility(s,!1,e);break;case"Toggle":this.setVisibility(s,!t.visible,e)}}this.#wi=null}get hasInitialVisibility(){return null===this.#xi||this.getHash()===this.#xi}getOrder(){return this.#Ai.size?this.#_i?this.#_i.slice():[...this.#Ai.keys()]:null}getGroups(){return this.#Ai.size>0?objectFromMap(this.#Ai):null}getGroup(t){return this.#Ai.get(t)||null}getHash(){if(null!==this.#wi)return this.#wi;const t=new MurmurHash3_64;for(const[e,i]of this.#Ai)t.update(`${e}:${i.visible}`);return this.#wi=t.hexdigest()}}class PDFDataTransportStream{constructor(t,{disableRange:e=!1,disableStream:i=!1}){assert(t,'PDFDataTransportStream - missing required "pdfDataRangeTransport" argument.');const{length:s,initialData:n,progressiveDone:r,contentDispositionFilename:a}=t;this._queuedChunks=[];this._progressiveDone=r;this._contentDispositionFilename=a;if(n?.length>0){const t=n instanceof Uint8Array&&n.byteLength===n.buffer.byteLength?n.buffer:new Uint8Array(n).buffer;this._queuedChunks.push(t)}this._pdfDataRangeTransport=t;this._isStreamingSupported=!i;this._isRangeSupported=!e;this._contentLength=s;this._fullRequestReader=null;this._rangeReaders=[];t.addRangeListener(((t,e)=>{this._onReceiveData({begin:t,chunk:e})}));t.addProgressListener(((t,e)=>{this._onProgress({loaded:t,total:e})}));t.addProgressiveReadListener((t=>{this._onReceiveData({chunk:t})}));t.addProgressiveDoneListener((()=>{this._onProgressiveDone()}));t.transportReady()}_onReceiveData({begin:t,chunk:e}){const i=e instanceof Uint8Array&&e.byteLength===e.buffer.byteLength?e.buffer:new Uint8Array(e).buffer;if(void 0===t)this._fullRequestReader?this._fullRequestReader._enqueue(i):this._queuedChunks.push(i);else{assert(this._rangeReaders.some((function(e){if(e._begin!==t)return!1;e._enqueue(i);return!0})),"_onReceiveData - no `PDFDataTransportStreamRangeReader` instance found.")}}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}_onProgress(t){void 0===t.total?this._rangeReaders[0]?.onProgress?.({loaded:t.loaded}):this._fullRequestReader?.onProgress?.({loaded:t.loaded,total:t.total})}_onProgressiveDone(){this._fullRequestReader?.progressiveDone();this._progressiveDone=!0}_removeRangeReader(t){const e=this._rangeReaders.indexOf(t);e>=0&&this._rangeReaders.splice(e,1)}getFullReader(){assert(!this._fullRequestReader,"PDFDataTransportStream.getFullReader can only be called once.");const t=this._queuedChunks;this._queuedChunks=null;return new PDFDataTransportStreamReader(this,t,this._progressiveDone,this._contentDispositionFilename)}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const i=new PDFDataTransportStreamRangeReader(this,t,e);this._pdfDataRangeTransport.requestDataRange(t,e);this._rangeReaders.push(i);return i}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeReaders.slice(0))e.cancel(t);this._pdfDataRangeTransport.abort()}}class PDFDataTransportStreamReader{constructor(t,e,i=!1,s=null){this._stream=t;this._done=i||!1;this._filename=isPdfFile(s)?s:null;this._queuedChunks=e||[];this._loaded=0;for(const t of this._queuedChunks)this._loaded+=t.byteLength;this._requests=[];this._headersReady=Promise.resolve();t._fullRequestReader=this;this.onProgress=null}_enqueue(t){if(!this._done){if(this._requests.length>0){this._requests.shift().resolve({value:t,done:!1})}else this._queuedChunks.push(t);this._loaded+=t.byteLength}}get headersReady(){return this._headersReady}get filename(){return this._filename}get isRangeSupported(){return this._stream._isRangeSupported}get isStreamingSupported(){return this._stream._isStreamingSupported}get contentLength(){return this._stream._contentLength}async read(){if(this._queuedChunks.length>0){return{value:this._queuedChunks.shift(),done:!1}}if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();this._requests.push(t);return t.promise}cancel(t){this._done=!0;for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0}progressiveDone(){this._done||(this._done=!0)}}class PDFDataTransportStreamRangeReader{constructor(t,e,i){this._stream=t;this._begin=e;this._end=i;this._queuedChunk=null;this._requests=[];this._done=!1;this.onProgress=null}_enqueue(t){if(!this._done){if(0===this._requests.length)this._queuedChunk=t;else{this._requests.shift().resolve({value:t,done:!1});for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0}this._done=!0;this._stream._removeRangeReader(this)}}get isStreamingSupported(){return!1}async read(){if(this._queuedChunk){const t=this._queuedChunk;this._queuedChunk=null;return{value:t,done:!1}}if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();this._requests.push(t);return t.promise}cancel(t){this._done=!0;for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0;this._stream._removeRangeReader(this)}}function createHeaders(t,e){const i=new Headers;if(!t||!e||"object"!=typeof e)return i;for(const t in e){const s=e[t];void 0!==s&&i.append(t,s)}return i}function getResponseOrigin(t){try{return new URL(t).origin}catch{}return null}function validateRangeRequestCapabilities({responseHeaders:t,isHttp:e,rangeChunkSize:i,disableRange:s}){const n={allowRangeRequests:!1,suggestedLength:void 0},r=parseInt(t.get("Content-Length"),10);if(!Number.isInteger(r))return n;n.suggestedLength=r;if(r<=2*i)return n;if(s||!e)return n;if("bytes"!==t.get("Accept-Ranges"))return n;if("identity"!==(t.get("Content-Encoding")||"identity"))return n;n.allowRangeRequests=!0;return n}function extractFilenameFromHeader(t){const e=t.get("Content-Disposition");if(e){let t=function getFilenameFromContentDispositionHeader(t){let e=!0,i=toParamRegExp("filename\\*","i").exec(t);if(i){i=i[1];let t=rfc2616unquote(i);t=unescape(t);t=rfc5987decode(t);t=rfc2047decode(t);return fixupEncoding(t)}i=function rfc2231getparam(t){const e=[];let i;const s=toParamRegExp("filename\\*((?!0\\d)\\d+)(\\*?)","ig");for(;null!==(i=s.exec(t));){let[,t,s,n]=i;t=parseInt(t,10);if(t in e){if(0===t)break}else e[t]=[s,n]}const n=[];for(let t=0;t<e.length&&t in e;++t){let[i,s]=e[t];s=rfc2616unquote(s);if(i){s=unescape(s);0===t&&(s=rfc5987decode(s))}n.push(s)}return n.join("")}(t);if(i)return fixupEncoding(rfc2047decode(i));i=toParamRegExp("filename","i").exec(t);if(i){i=i[1];let t=rfc2616unquote(i);t=rfc2047decode(t);return fixupEncoding(t)}function toParamRegExp(t,e){return new RegExp("(?:^|;)\\s*"+t+'\\s*=\\s*([^";\\s][^;\\s]*|"(?:[^"\\\\]|\\\\"?)+"?)',e)}function textdecode(t,i){if(t){if(!/^[\x00-\xFF]+$/.test(i))return i;try{const s=new TextDecoder(t,{fatal:!0}),n=stringToBytes(i);i=s.decode(n);e=!1}catch{}}return i}function fixupEncoding(t){if(e&&/[\x80-\xff]/.test(t)){t=textdecode("utf-8",t);e&&(t=textdecode("iso-8859-1",t))}return t}function rfc2616unquote(t){if(t.startsWith('"')){const e=t.slice(1).split('\\"');for(let t=0;t<e.length;++t){const i=e[t].indexOf('"');if(-1!==i){e[t]=e[t].slice(0,i);e.length=t+1}e[t]=e[t].replaceAll(/\\(.)/g,"$1")}t=e.join('"')}return t}function rfc5987decode(t){const e=t.indexOf("'");return-1===e?t:textdecode(t.slice(0,e),t.slice(e+1).replace(/^[^']*'/,""))}function rfc2047decode(t){return!t.startsWith("=?")||/[\x00-\x19\x80-\xff]/.test(t)?t:t.replaceAll(/=\?([\w-]*)\?([QqBb])\?((?:[^?]|\?(?!=))*)\?=/g,(function(t,e,i,s){if("q"===i||"Q"===i)return textdecode(e,s=(s=s.replaceAll("_"," ")).replaceAll(/=([0-9a-fA-F]{2})/g,(function(t,e){return String.fromCharCode(parseInt(e,16))})));try{s=atob(s)}catch{}return textdecode(e,s)}))}return""}(e);if(t.includes("%"))try{t=decodeURIComponent(t)}catch{}if(isPdfFile(t))return t}return null}function createResponseStatusError(t,e){return 404===t||0===t&&e.startsWith("file:")?new MissingPDFException('Missing PDF "'+e+'".'):new UnexpectedResponseException(`Unexpected server response (${t}) while retrieving PDF "${e}".`,t)}function validateResponseStatus(t){return 200===t||206===t}function createFetchOptions(t,e,i){return{method:"GET",headers:t,signal:i.signal,mode:"cors",credentials:e?"include":"same-origin",redirect:"follow"}}function getArrayBuffer(t){if(t instanceof Uint8Array)return t.buffer;if(t instanceof ArrayBuffer)return t;warn(`getArrayBuffer - unexpected data format: ${t}`);return new Uint8Array(t).buffer}class PDFFetchStream{_responseOrigin=null;constructor(t){this.source=t;this.isHttp=/^https?:/i.test(t.url);this.headers=createHeaders(this.isHttp,t.httpHeaders);this._fullRequestReader=null;this._rangeRequestReaders=[]}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}getFullReader(){assert(!this._fullRequestReader,"PDFFetchStream.getFullReader can only be called once.");this._fullRequestReader=new PDFFetchStreamReader(this);return this._fullRequestReader}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const i=new PDFFetchStreamRangeReader(this,t,e);this._rangeRequestReaders.push(i);return i}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeRequestReaders.slice(0))e.cancel(t)}}class PDFFetchStreamReader{constructor(t){this._stream=t;this._reader=null;this._loaded=0;this._filename=null;const e=t.source;this._withCredentials=e.withCredentials||!1;this._contentLength=e.length;this._headersCapability=Promise.withResolvers();this._disableRange=e.disableRange||!1;this._rangeChunkSize=e.rangeChunkSize;this._rangeChunkSize||this._disableRange||(this._disableRange=!0);this._abortController=new AbortController;this._isStreamingSupported=!e.disableStream;this._isRangeSupported=!e.disableRange;const i=new Headers(t.headers),s=e.url;fetch(s,createFetchOptions(i,this._withCredentials,this._abortController)).then((e=>{t._responseOrigin=getResponseOrigin(e.url);if(!validateResponseStatus(e.status))throw createResponseStatusError(e.status,s);this._reader=e.body.getReader();this._headersCapability.resolve();const i=e.headers,{allowRangeRequests:n,suggestedLength:r}=validateRangeRequestCapabilities({responseHeaders:i,isHttp:t.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});this._isRangeSupported=n;this._contentLength=r||this._contentLength;this._filename=extractFilenameFromHeader(i);!this._isStreamingSupported&&this._isRangeSupported&&this.cancel(new AbortException("Streaming is disabled."))})).catch(this._headersCapability.reject);this.onProgress=null}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._headersCapability.promise;const{value:t,done:e}=await this._reader.read();if(e)return{value:t,done:e};this._loaded+=t.byteLength;this.onProgress?.({loaded:this._loaded,total:this._contentLength});return{value:getArrayBuffer(t),done:!1}}cancel(t){this._reader?.cancel(t);this._abortController.abort()}}class PDFFetchStreamRangeReader{constructor(t,e,i){this._stream=t;this._reader=null;this._loaded=0;const s=t.source;this._withCredentials=s.withCredentials||!1;this._readCapability=Promise.withResolvers();this._isStreamingSupported=!s.disableStream;this._abortController=new AbortController;const n=new Headers(t.headers);n.append("Range",`bytes=${e}-${i-1}`);const r=s.url;fetch(r,createFetchOptions(n,this._withCredentials,this._abortController)).then((e=>{const i=getResponseOrigin(e.url);if(i!==t._responseOrigin)throw new Error(`Expected range response-origin "${i}" to match "${t._responseOrigin}".`);if(!validateResponseStatus(e.status))throw createResponseStatusError(e.status,r);this._readCapability.resolve();this._reader=e.body.getReader()})).catch(this._readCapability.reject);this.onProgress=null}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._readCapability.promise;const{value:t,done:e}=await this._reader.read();if(e)return{value:t,done:e};this._loaded+=t.byteLength;this.onProgress?.({loaded:this._loaded});return{value:getArrayBuffer(t),done:!1}}cancel(t){this._reader?.cancel(t);this._abortController.abort()}}class NetworkManager{_responseOrigin=null;constructor({url:t,httpHeaders:e,withCredentials:i}){this.url=t;this.isHttp=/^https?:/i.test(t);this.headers=createHeaders(this.isHttp,e);this.withCredentials=i||!1;this.currXhrId=0;this.pendingRequests=Object.create(null)}request(t){const e=new XMLHttpRequest,i=this.currXhrId++,s=this.pendingRequests[i]={xhr:e};e.open("GET",this.url);e.withCredentials=this.withCredentials;for(const[t,i]of this.headers)e.setRequestHeader(t,i);if(this.isHttp&&"begin"in t&&"end"in t){e.setRequestHeader("Range",`bytes=${t.begin}-${t.end-1}`);s.expectedStatus=206}else s.expectedStatus=200;e.responseType="arraybuffer";assert(t.onError,"Expected `onError` callback to be provided.");e.onerror=()=>{t.onError(e.status)};e.onreadystatechange=this.onStateChange.bind(this,i);e.onprogress=this.onProgress.bind(this,i);s.onHeadersReceived=t.onHeadersReceived;s.onDone=t.onDone;s.onError=t.onError;s.onProgress=t.onProgress;e.send(null);return i}onProgress(t,e){const i=this.pendingRequests[t];i&&i.onProgress?.(e)}onStateChange(t,e){const i=this.pendingRequests[t];if(!i)return;const s=i.xhr;if(s.readyState>=2&&i.onHeadersReceived){i.onHeadersReceived();delete i.onHeadersReceived}if(4!==s.readyState)return;if(!(t in this.pendingRequests))return;delete this.pendingRequests[t];if(0===s.status&&this.isHttp){i.onError(s.status);return}const n=s.status||200;if(!(200===n&&206===i.expectedStatus)&&n!==i.expectedStatus){i.onError(s.status);return}const r=function network_getArrayBuffer(t){const e=t.response;return"string"!=typeof e?e:stringToBytes(e).buffer}(s);if(206===n){const t=s.getResponseHeader("Content-Range"),e=/bytes (\d+)-(\d+)\/(\d+)/.exec(t);if(e)i.onDone({begin:parseInt(e[1],10),chunk:r});else{warn('Missing or invalid "Content-Range" header.');i.onError(0)}}else r?i.onDone({begin:0,chunk:r}):i.onError(s.status)}getRequestXhr(t){return this.pendingRequests[t].xhr}isPendingRequest(t){return t in this.pendingRequests}abortRequest(t){const e=this.pendingRequests[t].xhr;delete this.pendingRequests[t];e.abort()}}class PDFNetworkStream{constructor(t){this._source=t;this._manager=new NetworkManager(t);this._rangeChunkSize=t.rangeChunkSize;this._fullRequestReader=null;this._rangeRequestReaders=[]}_onRangeRequestReaderClosed(t){const e=this._rangeRequestReaders.indexOf(t);e>=0&&this._rangeRequestReaders.splice(e,1)}getFullReader(){assert(!this._fullRequestReader,"PDFNetworkStream.getFullReader can only be called once.");this._fullRequestReader=new PDFNetworkStreamFullRequestReader(this._manager,this._source);return this._fullRequestReader}getRangeReader(t,e){const i=new PDFNetworkStreamRangeRequestReader(this._manager,t,e);i.onClosed=this._onRangeRequestReaderClosed.bind(this);this._rangeRequestReaders.push(i);return i}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeRequestReaders.slice(0))e.cancel(t)}}class PDFNetworkStreamFullRequestReader{constructor(t,e){this._manager=t;this._url=e.url;this._fullRequestId=t.request({onHeadersReceived:this._onHeadersReceived.bind(this),onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)});this._headersCapability=Promise.withResolvers();this._disableRange=e.disableRange||!1;this._contentLength=e.length;this._rangeChunkSize=e.rangeChunkSize;this._rangeChunkSize||this._disableRange||(this._disableRange=!0);this._isStreamingSupported=!1;this._isRangeSupported=!1;this._cachedChunks=[];this._requests=[];this._done=!1;this._storedError=void 0;this._filename=null;this.onProgress=null}_onHeadersReceived(){const t=this._fullRequestId,e=this._manager.getRequestXhr(t);this._manager._responseOrigin=getResponseOrigin(e.responseURL);const i=e.getAllResponseHeaders(),s=new Headers(i?i.trimStart().replace(/[^\S ]+$/,"").split(/[\r\n]+/).map((t=>{const[e,...i]=t.split(": ");return[e,i.join(": ")]})):[]),{allowRangeRequests:n,suggestedLength:r}=validateRangeRequestCapabilities({responseHeaders:s,isHttp:this._manager.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});n&&(this._isRangeSupported=!0);this._contentLength=r||this._contentLength;this._filename=extractFilenameFromHeader(s);this._isRangeSupported&&this._manager.abortRequest(t);this._headersCapability.resolve()}_onDone(t){if(t)if(this._requests.length>0){this._requests.shift().resolve({value:t.chunk,done:!1})}else this._cachedChunks.push(t.chunk);this._done=!0;if(!(this._cachedChunks.length>0)){for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0}}_onError(t){this._storedError=createResponseStatusError(t,this._url);this._headersCapability.reject(this._storedError);for(const t of this._requests)t.reject(this._storedError);this._requests.length=0;this._cachedChunks.length=0}_onProgress(t){this.onProgress?.({loaded:t.loaded,total:t.lengthComputable?t.total:this._contentLength})}get filename(){return this._filename}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}get contentLength(){return this._contentLength}get headersReady(){return this._headersCapability.promise}async read(){await this._headersCapability.promise;if(this._storedError)throw this._storedError;if(this._cachedChunks.length>0){return{value:this._cachedChunks.shift(),done:!1}}if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();this._requests.push(t);return t.promise}cancel(t){this._done=!0;this._headersCapability.reject(t);for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0;this._manager.isPendingRequest(this._fullRequestId)&&this._manager.abortRequest(this._fullRequestId);this._fullRequestReader=null}}class PDFNetworkStreamRangeRequestReader{constructor(t,e,i){this._manager=t;this._url=t.url;this._requestId=t.request({begin:e,end:i,onHeadersReceived:this._onHeadersReceived.bind(this),onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)});this._requests=[];this._queuedChunk=null;this._done=!1;this._storedError=void 0;this.onProgress=null;this.onClosed=null}_onHeadersReceived(){const t=getResponseOrigin(this._manager.getRequestXhr(this._requestId)?.responseURL);if(t!==this._manager._responseOrigin){this._storedError=new Error(`Expected range response-origin "${t}" to match "${this._manager._responseOrigin}".`);this._onError(0)}}_close(){this.onClosed?.(this)}_onDone(t){const e=t.chunk;if(this._requests.length>0){this._requests.shift().resolve({value:e,done:!1})}else this._queuedChunk=e;this._done=!0;for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0;this._close()}_onError(t){this._storedError??=createResponseStatusError(t,this._url);for(const t of this._requests)t.reject(this._storedError);this._requests.length=0;this._queuedChunk=null}_onProgress(t){this.isStreamingSupported||this.onProgress?.({loaded:t.loaded})}get isStreamingSupported(){return!1}async read(){if(this._storedError)throw this._storedError;if(null!==this._queuedChunk){const t=this._queuedChunk;this._queuedChunk=null;return{value:t,done:!1}}if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();this._requests.push(t);return t.promise}cancel(t){this._done=!0;for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0;this._manager.isPendingRequest(this._requestId)&&this._manager.abortRequest(this._requestId);this._close()}}const Dt=/^[a-z][a-z0-9\-+.]+:/i;class PDFNodeStream{constructor(t){this.source=t;this.url=function parseUrlOrPath(t){if(Dt.test(t))return new URL(t);const e=process.getBuiltinModule("url");return new URL(e.pathToFileURL(t))}(t.url);assert("file:"===this.url.protocol,"PDFNodeStream only supports file:// URLs.");this._fullRequestReader=null;this._rangeRequestReaders=[]}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}getFullReader(){assert(!this._fullRequestReader,"PDFNodeStream.getFullReader can only be called once.");this._fullRequestReader=new PDFNodeStreamFsFullReader(this);return this._fullRequestReader}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const i=new PDFNodeStreamFsRangeReader(this,t,e);this._rangeRequestReaders.push(i);return i}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeRequestReaders.slice(0))e.cancel(t)}}class PDFNodeStreamFsFullReader{constructor(t){this._url=t.url;this._done=!1;this._storedError=null;this.onProgress=null;const e=t.source;this._contentLength=e.length;this._loaded=0;this._filename=null;this._disableRange=e.disableRange||!1;this._rangeChunkSize=e.rangeChunkSize;this._rangeChunkSize||this._disableRange||(this._disableRange=!0);this._isStreamingSupported=!e.disableStream;this._isRangeSupported=!e.disableRange;this._readableStream=null;this._readCapability=Promise.withResolvers();this._headersCapability=Promise.withResolvers();const i=process.getBuiltinModule("fs");i.promises.lstat(this._url).then((t=>{this._contentLength=t.size;this._setReadableStream(i.createReadStream(this._url));this._headersCapability.resolve()}),(t=>{"ENOENT"===t.code&&(t=new MissingPDFException(`Missing PDF "${this._url}".`));this._storedError=t;this._headersCapability.reject(t)}))}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._readCapability.promise;if(this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;const t=this._readableStream.read();if(null===t){this._readCapability=Promise.withResolvers();return this.read()}this._loaded+=t.length;this.onProgress?.({loaded:this._loaded,total:this._contentLength});return{value:new Uint8Array(t).buffer,done:!1}}cancel(t){this._readableStream?this._readableStream.destroy(t):this._error(t)}_error(t){this._storedError=t;this._readCapability.resolve()}_setReadableStream(t){this._readableStream=t;t.on("readable",(()=>{this._readCapability.resolve()}));t.on("end",(()=>{t.destroy();this._done=!0;this._readCapability.resolve()}));t.on("error",(t=>{this._error(t)}));!this._isStreamingSupported&&this._isRangeSupported&&this._error(new AbortException("streaming is disabled"));this._storedError&&this._readableStream.destroy(this._storedError)}}class PDFNodeStreamFsRangeReader{constructor(t,e,i){this._url=t.url;this._done=!1;this._storedError=null;this.onProgress=null;this._loaded=0;this._readableStream=null;this._readCapability=Promise.withResolvers();const s=t.source;this._isStreamingSupported=!s.disableStream;const n=process.getBuiltinModule("fs");this._setReadableStream(n.createReadStream(this._url,{start:e,end:i-1}))}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._readCapability.promise;if(this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;const t=this._readableStream.read();if(null===t){this._readCapability=Promise.withResolvers();return this.read()}this._loaded+=t.length;this.onProgress?.({loaded:this._loaded});return{value:new Uint8Array(t).buffer,done:!1}}cancel(t){this._readableStream?this._readableStream.destroy(t):this._error(t)}_error(t){this._storedError=t;this._readCapability.resolve()}_setReadableStream(t){this._readableStream=t;t.on("readable",(()=>{this._readCapability.resolve()}));t.on("end",(()=>{t.destroy();this._done=!0;this._readCapability.resolve()}));t.on("error",(t=>{this._error(t)}));this._storedError&&this._readableStream.destroy(this._storedError)}}const kt=30;class TextLayer{#Si=Promise.withResolvers();#pt=null;#Ci=!1;#Ti=!!globalThis.FontInspector?.enabled;#Mi=null;#Pi=null;#Di=0;#ki=0;#Ri=null;#Ii=null;#Fi=0;#Li=0;#Oi=Object.create(null);#Ni=[];#Bi=null;#Hi=[];#Ui=new WeakMap;#zi=null;static#ji=new Map;static#Gi=new Map;static#Vi=new WeakMap;static#$i=null;static#Wi=new Set;constructor({textContentSource:t,container:e,viewport:i}){if(t instanceof ReadableStream)this.#Bi=t;else{if("object"!=typeof t)throw new Error('No "textContentSource" parameter specified.');this.#Bi=new ReadableStream({start(e){e.enqueue(t);e.close()}})}this.#pt=this.#Ii=e;this.#Li=i.scale*(globalThis.devicePixelRatio||1);this.#Fi=i.rotation;this.#Pi={div:null,properties:null,ctx:null};const{pageWidth:s,pageHeight:n,pageX:r,pageY:a}=i.rawDims;this.#zi=[1,0,0,-1,-r,a+n];this.#ki=s;this.#Di=n;TextLayer.#qi();setLayerDimensions(e,i);this.#Si.promise.finally((()=>{TextLayer.#Wi.delete(this);this.#Pi=null;this.#Oi=null})).catch((()=>{}))}static get fontFamilyMap(){const{isWindows:t,isFirefox:e}=util_FeatureTest.platform;return shadow(this,"fontFamilyMap",new Map([["sans-serif",(t&&e?"Calibri, ":"")+"sans-serif"],["monospace",(t&&e?"Lucida Console, ":"")+"monospace"]]))}render(){const pump=()=>{this.#Ri.read().then((({value:t,done:e})=>{if(e)this.#Si.resolve();else{this.#Mi??=t.lang;Object.assign(this.#Oi,t.styles);this.#Xi(t.items);pump()}}),this.#Si.reject)};this.#Ri=this.#Bi.getReader();TextLayer.#Wi.add(this);pump();return this.#Si.promise}update({viewport:t,onBefore:e=null}){const i=t.scale*(globalThis.devicePixelRatio||1),s=t.rotation;if(s!==this.#Fi){e?.();this.#Fi=s;setLayerDimensions(this.#Ii,{rotation:s})}if(i!==this.#Li){e?.();this.#Li=i;const t={div:null,properties:null,ctx:TextLayer.#Yi(this.#Mi)};for(const e of this.#Hi){t.properties=this.#Ui.get(e);t.div=e;this.#Ki(t)}}}cancel(){const t=new AbortException("TextLayer task cancelled.");this.#Ri?.cancel(t).catch((()=>{}));this.#Ri=null;this.#Si.reject(t)}get textDivs(){return this.#Hi}get textContentItemsStr(){return this.#Ni}#Xi(t){if(this.#Ci)return;this.#Pi.ctx??=TextLayer.#Yi(this.#Mi);const e=this.#Hi,i=this.#Ni;for(const s of t){if(e.length>1e5){warn("Ignoring additional textDivs for performance reasons.");this.#Ci=!0;return}if(void 0!==s.str){i.push(s.str);this.#Qi(s)}else if("beginMarkedContentProps"===s.type||"beginMarkedContent"===s.type){const t=this.#pt;this.#pt=document.createElement("span");this.#pt.classList.add("markedContent");null!==s.id&&this.#pt.setAttribute("id",`${s.id}`);t.append(this.#pt)}else"endMarkedContent"===s.type&&(this.#pt=this.#pt.parentNode)}}#Qi(t){const e=document.createElement("span"),i={angle:0,canvasWidth:0,hasText:""!==t.str,hasEOL:t.hasEOL,fontSize:0};this.#Hi.push(e);const s=Util.transform(this.#zi,t.transform);let n=Math.atan2(s[1],s[0]);const r=this.#Oi[t.fontName];r.vertical&&(n+=Math.PI/2);let a=this.#Ti&&r.fontSubstitution||r.fontFamily;a=TextLayer.fontFamilyMap.get(a)||a;const o=Math.hypot(s[2],s[3]),l=o*TextLayer.#Ji(a,this.#Mi);let h,c;if(0===n){h=s[4];c=s[5]-l}else{h=s[4]+l*Math.sin(n);c=s[5]-l*Math.cos(n)}const d="calc(var(--scale-factor)*",u=e.style;if(this.#pt===this.#Ii){u.left=`${(100*h/this.#ki).toFixed(2)}%`;u.top=`${(100*c/this.#Di).toFixed(2)}%`}else{u.left=`${d}${h.toFixed(2)}px)`;u.top=`${d}${c.toFixed(2)}px)`}u.fontSize=`${d}${(TextLayer.#$i*o).toFixed(2)}px)`;u.fontFamily=a;i.fontSize=o;e.setAttribute("role","presentation");e.textContent=t.str;e.dir=t.dir;this.#Ti&&(e.dataset.fontName=r.fontSubstitutionLoadedName||t.fontName);0!==n&&(i.angle=n*(180/Math.PI));let p=!1;if(t.str.length>1)p=!0;else if(" "!==t.str&&t.transform[0]!==t.transform[3]){const e=Math.abs(t.transform[0]),i=Math.abs(t.transform[3]);e!==i&&Math.max(e,i)/Math.min(e,i)>1.5&&(p=!0)}p&&(i.canvasWidth=r.vertical?t.height:t.width);this.#Ui.set(e,i);this.#Pi.div=e;this.#Pi.properties=i;this.#Ki(this.#Pi);i.hasText&&this.#pt.append(e);if(i.hasEOL){const t=document.createElement("br");t.setAttribute("role","presentation");this.#pt.append(t)}}#Ki(t){const{div:e,properties:i,ctx:s}=t,{style:n}=e;let r="";TextLayer.#$i>1&&(r=`scale(${1/TextLayer.#$i})`);if(0!==i.canvasWidth&&i.hasText){const{fontFamily:t}=n,{canvasWidth:a,fontSize:o}=i;TextLayer.#Zi(s,o*this.#Li,t);const{width:l}=s.measureText(e.textContent);l>0&&(r=`scaleX(${a*this.#Li/l}) ${r}`)}0!==i.angle&&(r=`rotate(${i.angle}deg) ${r}`);r.length>0&&(n.transform=r)}static cleanup(){if(!(this.#Wi.size>0)){this.#ji.clear();for(const{canvas:t}of this.#Gi.values())t.remove();this.#Gi.clear()}}static#Yi(t=null){let e=this.#Gi.get(t||="");if(!e){const i=document.createElement("canvas");i.className="hiddenCanvasElement";i.lang=t;document.body.append(i);e=i.getContext("2d",{alpha:!1,willReadFrequently:!0});this.#Gi.set(t,e);this.#Vi.set(e,{size:0,family:""})}return e}static#Zi(t,e,i){const s=this.#Vi.get(t);if(e!==s.size||i!==s.family){t.font=`${e}px ${i}`;s.size=e;s.family=i}}static#qi(){if(null!==this.#$i)return;const t=document.createElement("div");t.style.opacity=0;t.style.lineHeight=1;t.style.fontSize="1px";t.style.position="absolute";t.textContent="X";document.body.append(t);this.#$i=t.getBoundingClientRect().height;t.remove()}static#Ji(t,e){const i=this.#ji.get(t);if(i)return i;const s=this.#Yi(e);s.canvas.width=s.canvas.height=kt;this.#Zi(s,kt,t);const n=s.measureText("");let r=n.fontBoundingBoxAscent,a=Math.abs(n.fontBoundingBoxDescent);if(r){const e=r/(r+a);this.#ji.set(t,e);s.canvas.width=s.canvas.height=0;return e}s.strokeStyle="red";s.clearRect(0,0,kt,kt);s.strokeText("g",0,0);let o=s.getImageData(0,0,kt,kt).data;a=0;for(let t=o.length-1-3;t>=0;t-=4)if(o[t]>0){a=Math.ceil(t/4/kt);break}s.clearRect(0,0,kt,kt);s.strokeText("A",0,kt);o=s.getImageData(0,0,kt,kt).data;r=0;for(let t=0,e=o.length;t<e;t+=4)if(o[t]>0){r=kt-Math.floor(t/4/kt);break}s.canvas.width=s.canvas.height=0;const l=r?r/(r+a):.8;this.#ji.set(t,l);return l}}class XfaText{static textContent(t){const e=[],i={items:e,styles:Object.create(null)};!function walk(t){if(!t)return;let i=null;const s=t.name;if("#text"===s)i=t.value;else{if(!XfaText.shouldBuildText(s))return;t?.attributes?.textContent?i=t.attributes.textContent:t.value&&(i=t.value)}null!==i&&e.push({str:i});if(t.children)for(const e of t.children)walk(e)}(t);return i}static shouldBuildText(t){return!("textarea"===t||"input"===t||"option"===t||"select"===t)}}const Rt=65536,It=i?class NodeCanvasFactory extends BaseCanvasFactory{_createCanvas(t,e){return process.getBuiltinModule("module").createRequire(import.meta.url)("@napi-rs/canvas").createCanvas(t,e)}}:class DOMCanvasFactory extends BaseCanvasFactory{constructor({ownerDocument:t=globalThis.document,enableHWA:e=!1}){super({enableHWA:e});this._document=t}_createCanvas(t,e){const i=this._document.createElement("canvas");i.width=t;i.height=e;return i}},Ft=i?class NodeCMapReaderFactory extends BaseCMapReaderFactory{async _fetch(t){return node_utils_fetchData(t)}}:DOMCMapReaderFactory,Lt=i?class NodeFilterFactory extends BaseFilterFactory{}:class DOMFilterFactory extends BaseFilterFactory{#ts;#es;#is;#ss;#ns;#rs;#y=0;constructor({docId:t,ownerDocument:e=globalThis.document}){super();this.#ss=t;this.#ns=e}get#A(){return this.#es||=new Map}get#as(){return this.#rs||=new Map}get#os(){if(!this.#is){const t=this.#ns.createElement("div"),{style:e}=t;e.visibility="hidden";e.contain="strict";e.width=e.height=0;e.position="absolute";e.top=e.left=0;e.zIndex=-1;const i=this.#ns.createElementNS(nt,"svg");i.setAttribute("width",0);i.setAttribute("height",0);this.#is=this.#ns.createElementNS(nt,"defs");t.append(i);i.append(this.#is);this.#ns.body.append(t)}return this.#is}#ls(t){if(1===t.length){const e=t[0],i=new Array(256);for(let t=0;t<256;t++)i[t]=e[t]/255;const s=i.join(",");return[s,s,s]}const[e,i,s]=t,n=new Array(256),r=new Array(256),a=new Array(256);for(let t=0;t<256;t++){n[t]=e[t]/255;r[t]=i[t]/255;a[t]=s[t]/255}return[n.join(","),r.join(","),a.join(",")]}#hs(t){if(void 0===this.#ts){this.#ts="";const t=this.#ns.URL;t!==this.#ns.baseURI&&(isDataScheme(t)?warn('#createUrl: ignore "data:"-URL for performance reasons.'):this.#ts=t.split("#",1)[0])}return`url(${this.#ts}#${t})`}addFilter(t){if(!t)return"none";let e=this.#A.get(t);if(e)return e;const[i,s,n]=this.#ls(t),r=1===t.length?i:`${i}${s}${n}`;e=this.#A.get(r);if(e){this.#A.set(t,e);return e}const a=`g_${this.#ss}_transfer_map_${this.#y++}`,o=this.#hs(a);this.#A.set(t,o);this.#A.set(r,o);const l=this.#cs(a);this.#ds(i,s,n,l);return o}addHCMFilter(t,e){const i=`${t}-${e}`,s="base";let n=this.#as.get(s);if(n?.key===i)return n.url;if(n){n.filter?.remove();n.key=i;n.url="none";n.filter=null}else{n={key:i,url:"none",filter:null};this.#as.set(s,n)}if(!t||!e)return n.url;const r=this.#us(t);t=Util.makeHexColor(...r);const a=this.#us(e);e=Util.makeHexColor(...a);this.#os.style.color="";if("#000000"===t&&"#ffffff"===e||t===e)return n.url;const o=new Array(256);for(let t=0;t<=255;t++){const e=t/255;o[t]=e<=.03928?e/12.92:((e+.055)/1.055)**2.4}const l=o.join(","),h=`g_${this.#ss}_hcm_filter`,c=n.filter=this.#cs(h);this.#ds(l,l,l,c);this.#ps(c);const getSteps=(t,e)=>{const i=r[t]/255,s=a[t]/255,n=new Array(e+1);for(let t=0;t<=e;t++)n[t]=i+t/e*(s-i);return n.join(",")};this.#ds(getSteps(0,5),getSteps(1,5),getSteps(2,5),c);n.url=this.#hs(h);return n.url}addAlphaFilter(t){let e=this.#A.get(t);if(e)return e;const[i]=this.#ls([t]),s=`alpha_${i}`;e=this.#A.get(s);if(e){this.#A.set(t,e);return e}const n=`g_${this.#ss}_alpha_map_${this.#y++}`,r=this.#hs(n);this.#A.set(t,r);this.#A.set(s,r);const a=this.#cs(n);this.#gs(i,a);return r}addLuminosityFilter(t){let e,i,s=this.#A.get(t||"luminosity");if(s)return s;if(t){[e]=this.#ls([t]);i=`luminosity_${e}`}else i="luminosity";s=this.#A.get(i);if(s){this.#A.set(t,s);return s}const n=`g_${this.#ss}_luminosity_map_${this.#y++}`,r=this.#hs(n);this.#A.set(t,r);this.#A.set(i,r);const a=this.#cs(n);this.#fs(a);t&&this.#gs(e,a);return r}addHighlightHCMFilter(t,e,i,s,n){const r=`${e}-${i}-${s}-${n}`;let a=this.#as.get(t);if(a?.key===r)return a.url;if(a){a.filter?.remove();a.key=r;a.url="none";a.filter=null}else{a={key:r,url:"none",filter:null};this.#as.set(t,a)}if(!e||!i)return a.url;const[o,l]=[e,i].map(this.#us.bind(this));let h=Math.round(.2126*o[0]+.7152*o[1]+.0722*o[2]),c=Math.round(.2126*l[0]+.7152*l[1]+.0722*l[2]),[d,u]=[s,n].map(this.#us.bind(this));c<h&&([h,c,d,u]=[c,h,u,d]);this.#os.style.color="";const getSteps=(t,e,i)=>{const s=new Array(256),n=(c-h)/i,r=t/255,a=(e-t)/(255*i);let o=0;for(let t=0;t<=i;t++){const e=Math.round(h+t*n),i=r+t*a;for(let t=o;t<=e;t++)s[t]=i;o=e+1}for(let t=o;t<256;t++)s[t]=s[o-1];return s.join(",")},p=`g_${this.#ss}_hcm_${t}_filter`,g=a.filter=this.#cs(p);this.#ps(g);this.#ds(getSteps(d[0],u[0],5),getSteps(d[1],u[1],5),getSteps(d[2],u[2],5),g);a.url=this.#hs(p);return a.url}destroy(t=!1){if(!t||!this.#rs?.size){this.#is?.parentNode.parentNode.remove();this.#is=null;this.#es?.clear();this.#es=null;this.#rs?.clear();this.#rs=null;this.#y=0}}#fs(t){const e=this.#ns.createElementNS(nt,"feColorMatrix");e.setAttribute("type","matrix");e.setAttribute("values","0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0.59 0.11 0 0");t.append(e)}#ps(t){const e=this.#ns.createElementNS(nt,"feColorMatrix");e.setAttribute("type","matrix");e.setAttribute("values","0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0 0 0 1 0");t.append(e)}#cs(t){const e=this.#ns.createElementNS(nt,"filter");e.setAttribute("color-interpolation-filters","sRGB");e.setAttribute("id",t);this.#os.append(e);return e}#ms(t,e,i){const s=this.#ns.createElementNS(nt,e);s.setAttribute("type","discrete");s.setAttribute("tableValues",i);t.append(s)}#ds(t,e,i,s){const n=this.#ns.createElementNS(nt,"feComponentTransfer");s.append(n);this.#ms(n,"feFuncR",t);this.#ms(n,"feFuncG",e);this.#ms(n,"feFuncB",i)}#gs(t,e){const i=this.#ns.createElementNS(nt,"feComponentTransfer");e.append(i);this.#ms(i,"feFuncA",t)}#us(t){this.#os.style.color=t;return getRGB(getComputedStyle(this.#os).getPropertyValue("color"))}},Ot=i?class NodeStandardFontDataFactory extends BaseStandardFontDataFactory{async _fetch(t){return node_utils_fetchData(t)}}:DOMStandardFontDataFactory;function getDocument(t={}){"string"==typeof t||t instanceof URL?t={url:t}:(t instanceof ArrayBuffer||ArrayBuffer.isView(t))&&(t={data:t});const e=new PDFDocumentLoadingTask,{docId:s}=e,n=t.url?function getUrlProp(t){if(t instanceof URL)return t.href;try{return new URL(t,window.location).href}catch{if(i&&"string"==typeof t)return t}throw new Error("Invalid PDF url data: either string or URL-object is expected in the url property.")}(t.url):null,r=t.data?function getDataProp(t){if(i&&"undefined"!=typeof Buffer&&t instanceof Buffer)throw new Error("Please provide binary data as `Uint8Array`, rather than `Buffer`.");if(t instanceof Uint8Array&&t.byteLength===t.buffer.byteLength)return t;if("string"==typeof t)return stringToBytes(t);if(t instanceof ArrayBuffer||ArrayBuffer.isView(t)||"object"==typeof t&&!isNaN(t?.length))return new Uint8Array(t);throw new Error("Invalid PDF binary data: either TypedArray, string, or array-like object is expected in the data property.")}(t.data):null,a=t.httpHeaders||null,o=!0===t.withCredentials,l=t.password??null,h=t.range instanceof PDFDataRangeTransport?t.range:null,c=Number.isInteger(t.rangeChunkSize)&&t.rangeChunkSize>0?t.rangeChunkSize:Rt;let d=t.worker instanceof PDFWorker?t.worker:null;const u=t.verbosity,p="string"!=typeof t.docBaseUrl||isDataScheme(t.docBaseUrl)?null:t.docBaseUrl,g="string"==typeof t.cMapUrl?t.cMapUrl:null,f=!1!==t.cMapPacked,m=t.CMapReaderFactory||Ft,b="string"==typeof t.standardFontDataUrl?t.standardFontDataUrl:null,v=t.StandardFontDataFactory||Ot,y=!0!==t.stopAtErrors,w=Number.isInteger(t.maxImageSize)&&t.maxImageSize>-1?t.maxImageSize:-1,A=!1!==t.isEvalSupported,x="boolean"==typeof t.isOffscreenCanvasSupported?t.isOffscreenCanvasSupported:!i,_="boolean"==typeof t.isImageDecoderSupported?t.isImageDecoderSupported:!i&&(util_FeatureTest.platform.isFirefox||!globalThis.chrome),E=Number.isInteger(t.canvasMaxAreaInBytes)?t.canvasMaxAreaInBytes:-1,S="boolean"==typeof t.disableFontFace?t.disableFontFace:i,C=!0===t.fontExtraProperties,T=!0===t.enableXfa,M=t.ownerDocument||globalThis.document,P=!0===t.disableRange,D=!0===t.disableStream,k=!0===t.disableAutoFetch,R=!0===t.pdfBug,I=t.CanvasFactory||It,L=t.FilterFactory||Lt,O=!0===t.enableHWA,N=h?h.length:t.length??NaN,B="boolean"==typeof t.useSystemFonts?t.useSystemFonts:!i&&!S,H="boolean"==typeof t.useWorkerFetch?t.useWorkerFetch:m===DOMCMapReaderFactory&&v===DOMStandardFontDataFactory&&g&&b&&isValidFetchUrl(g,document.baseURI)&&isValidFetchUrl(b,document.baseURI);setVerbosityLevel(u);const U={canvasFactory:new I({ownerDocument:M,enableHWA:O}),filterFactory:new L({docId:s,ownerDocument:M}),cMapReaderFactory:H?null:new m({baseUrl:g,isCompressed:f}),standardFontDataFactory:H?null:new v({baseUrl:b})};if(!d){const t={verbosity:u,port:GlobalWorkerOptions.workerPort};d=t.port?PDFWorker.fromPort(t):new PDFWorker(t);e._worker=d}const z={docId:s,apiVersion:"4.10.38",data:r,password:l,disableAutoFetch:k,rangeChunkSize:c,length:N,docBaseUrl:p,enableXfa:T,evaluatorOptions:{maxImageSize:w,disableFontFace:S,ignoreErrors:y,isEvalSupported:A,isOffscreenCanvasSupported:x,isImageDecoderSupported:_,canvasMaxAreaInBytes:E,fontExtraProperties:C,useSystemFonts:B,cMapUrl:H?g:null,standardFontDataUrl:H?b:null}},j={disableFontFace:S,fontExtraProperties:C,ownerDocument:M,pdfBug:R,styleElement:null,loadingParams:{disableAutoFetch:k,enableXfa:T}};d.promise.then((function(){if(e.destroyed)throw new Error("Loading aborted");if(d.destroyed)throw new Error("Worker was destroyed");const t=d.messageHandler.sendWithPromise("GetDocRequest",z,r?[r.buffer]:null);let l;if(h)l=new PDFDataTransportStream(h,{disableRange:P,disableStream:D});else if(!r){if(!n)throw new Error("getDocument - no `url` parameter provided.");let t;if(i)if(isValidFetchUrl(n)){if("undefined"==typeof fetch||"undefined"==typeof Response||!("body"in Response.prototype))throw new Error("getDocument - the Fetch API was disabled in Node.js, see `--no-experimental-fetch`.");t=PDFFetchStream}else t=PDFNodeStream;else t=isValidFetchUrl(n)?PDFFetchStream:PDFNetworkStream;l=new t({url:n,length:N,httpHeaders:a,withCredentials:o,rangeChunkSize:c,disableRange:P,disableStream:D})}return t.then((t=>{if(e.destroyed)throw new Error("Loading aborted");if(d.destroyed)throw new Error("Worker was destroyed");const i=new MessageHandler(s,t,d.port),n=new WorkerTransport(i,e,l,j,U);e._transport=n;i.send("Ready",null)}))})).catch(e._capability.reject);return e}function isRefProxy(t){return"object"==typeof t&&Number.isInteger(t?.num)&&t.num>=0&&Number.isInteger(t?.gen)&&t.gen>=0}class PDFDocumentLoadingTask{static#ss=0;constructor(){this._capability=Promise.withResolvers();this._transport=null;this._worker=null;this.docId="d"+PDFDocumentLoadingTask.#ss++;this.destroyed=!1;this.onPassword=null;this.onProgress=null}get promise(){return this._capability.promise}async destroy(){this.destroyed=!0;try{this._worker?.port&&(this._worker._pendingDestroy=!0);await(this._transport?.destroy())}catch(t){this._worker?.port&&delete this._worker._pendingDestroy;throw t}this._transport=null;this._worker?.destroy();this._worker=null}}class PDFDataRangeTransport{constructor(t,e,i=!1,s=null){this.length=t;this.initialData=e;this.progressiveDone=i;this.contentDispositionFilename=s;this._rangeListeners=[];this._progressListeners=[];this._progressiveReadListeners=[];this._progressiveDoneListeners=[];this._readyCapability=Promise.withResolvers()}addRangeListener(t){this._rangeListeners.push(t)}addProgressListener(t){this._progressListeners.push(t)}addProgressiveReadListener(t){this._progressiveReadListeners.push(t)}addProgressiveDoneListener(t){this._progressiveDoneListeners.push(t)}onDataRange(t,e){for(const i of this._rangeListeners)i(t,e)}onDataProgress(t,e){this._readyCapability.promise.then((()=>{for(const i of this._progressListeners)i(t,e)}))}onDataProgressiveRead(t){this._readyCapability.promise.then((()=>{for(const e of this._progressiveReadListeners)e(t)}))}onDataProgressiveDone(){this._readyCapability.promise.then((()=>{for(const t of this._progressiveDoneListeners)t()}))}transportReady(){this._readyCapability.resolve()}requestDataRange(t,e){unreachable("Abstract method PDFDataRangeTransport.requestDataRange")}abort(){}}class PDFDocumentProxy{constructor(t,e){this._pdfInfo=t;this._transport=e}get annotationStorage(){return this._transport.annotationStorage}get canvasFactory(){return this._transport.canvasFactory}get filterFactory(){return this._transport.filterFactory}get numPages(){return this._pdfInfo.numPages}get fingerprints(){return this._pdfInfo.fingerprints}get isPureXfa(){return shadow(this,"isPureXfa",!!this._transport._htmlForXfa)}get allXfaHtml(){return this._transport._htmlForXfa}getPage(t){return this._transport.getPage(t)}getPageIndex(t){return this._transport.getPageIndex(t)}getDestinations(){return this._transport.getDestinations()}getDestination(t){return this._transport.getDestination(t)}getPageLabels(){return this._transport.getPageLabels()}getPageLayout(){return this._transport.getPageLayout()}getPageMode(){return this._transport.getPageMode()}getViewerPreferences(){return this._transport.getViewerPreferences()}getOpenAction(){return this._transport.getOpenAction()}getAttachments(){return this._transport.getAttachments()}getJSActions(){return this._transport.getDocJSActions()}getOutline(){return this._transport.getOutline()}getOptionalContentConfig({intent:t="display"}={}){const{renderingIntent:e}=this._transport.getRenderingIntent(t);return this._transport.getOptionalContentConfig(e)}getPermissions(){return this._transport.getPermissions()}getMetadata(){return this._transport.getMetadata()}getMarkInfo(){return this._transport.getMarkInfo()}getData(){return this._transport.getData()}saveDocument(){return this._transport.saveDocument()}getDownloadInfo(){return this._transport.downloadInfoCapability.promise}cleanup(t=!1){return this._transport.startCleanup(t||this.isPureXfa)}destroy(){return this.loadingTask.destroy()}cachedPageNumber(t){return this._transport.cachedPageNumber(t)}get loadingParams(){return this._transport.loadingParams}get loadingTask(){return this._transport.loadingTask}getFieldObjects(){return this._transport.getFieldObjects()}hasJSActions(){return this._transport.hasJSActions()}getCalculationOrderIds(){return this._transport.getCalculationOrderIds()}}class PDFPageProxy{#bs=null;#vs=!1;constructor(t,e,i,s=!1){this._pageIndex=t;this._pageInfo=e;this._transport=i;this._stats=s?new StatTimer:null;this._pdfBug=s;this.commonObjs=i.commonObjs;this.objs=new PDFObjects;this._maybeCleanupAfterRender=!1;this._intentStates=new Map;this.destroyed=!1}get pageNumber(){return this._pageIndex+1}get rotate(){return this._pageInfo.rotate}get ref(){return this._pageInfo.ref}get userUnit(){return this._pageInfo.userUnit}get view(){return this._pageInfo.view}getViewport({scale:t,rotation:e=this.rotate,offsetX:i=0,offsetY:s=0,dontFlip:n=!1}={}){return new PageViewport({viewBox:this.view,userUnit:this.userUnit,scale:t,rotation:e,offsetX:i,offsetY:s,dontFlip:n})}getAnnotations({intent:t="display"}={}){const{renderingIntent:e}=this._transport.getRenderingIntent(t);return this._transport.getAnnotations(this._pageIndex,e)}getJSActions(){return this._transport.getPageJSActions(this._pageIndex)}get filterFactory(){return this._transport.filterFactory}get isPureXfa(){return shadow(this,"isPureXfa",!!this._transport._htmlForXfa)}async getXfa(){return this._transport._htmlForXfa?.children[this._pageIndex]||null}render({canvasContext:t,viewport:e,intent:i="display",annotationMode:s=g.ENABLE,transform:n=null,background:r=null,optionalContentConfigPromise:a=null,annotationCanvasMap:o=null,pageColors:h=null,printAnnotationStorage:c=null,isEditing:d=!1}){this._stats?.time("Overall");const u=this._transport.getRenderingIntent(i,s,c,d),{renderingIntent:p,cacheKey:f}=u;this.#vs=!1;this.#ys();a||=this._transport.getOptionalContentConfig(p);let m=this._intentStates.get(f);if(!m){m=Object.create(null);this._intentStates.set(f,m)}if(m.streamReaderCancelTimeout){clearTimeout(m.streamReaderCancelTimeout);m.streamReaderCancelTimeout=null}const b=!!(p&l);if(!m.displayReadyCapability){m.displayReadyCapability=Promise.withResolvers();m.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null};this._stats?.time("Page Request");this._pumpOperatorList(u)}const complete=t=>{m.renderTasks.delete(v);(this._maybeCleanupAfterRender||b)&&(this.#vs=!0);this.#ws(!b);if(t){v.capability.reject(t);this._abortOperatorList({intentState:m,reason:t instanceof Error?t:new Error(t)})}else v.capability.resolve();if(this._stats){this._stats.timeEnd("Rendering");this._stats.timeEnd("Overall");globalThis.Stats?.enabled&&globalThis.Stats.add(this.pageNumber,this._stats)}},v=new InternalRenderTask({callback:complete,params:{canvasContext:t,viewport:e,transform:n,background:r},objs:this.objs,commonObjs:this.commonObjs,annotationCanvasMap:o,operatorList:m.operatorList,pageIndex:this._pageIndex,canvasFactory:this._transport.canvasFactory,filterFactory:this._transport.filterFactory,useRequestAnimationFrame:!b,pdfBug:this._pdfBug,pageColors:h});(m.renderTasks||=new Set).add(v);const y=v.task;Promise.all([m.displayReadyCapability.promise,a]).then((([t,e])=>{if(this.destroyed)complete();else{this._stats?.time("Rendering");if(!(e.renderingIntent&p))throw new Error("Must use the same `intent`-argument when calling the `PDFPageProxy.render` and `PDFDocumentProxy.getOptionalContentConfig` methods.");v.initializeGraphics({transparency:t,optionalContentConfig:e});v.operatorListChanged()}})).catch(complete);return y}getOperatorList({intent:t="display",annotationMode:e=g.ENABLE,printAnnotationStorage:i=null,isEditing:s=!1}={}){const n=this._transport.getRenderingIntent(t,e,i,s,!0);let r,a=this._intentStates.get(n.cacheKey);if(!a){a=Object.create(null);this._intentStates.set(n.cacheKey,a)}if(!a.opListReadCapability){r=Object.create(null);r.operatorListChanged=function operatorListChanged(){if(a.operatorList.lastChunk){a.opListReadCapability.resolve(a.operatorList);a.renderTasks.delete(r)}};a.opListReadCapability=Promise.withResolvers();(a.renderTasks||=new Set).add(r);a.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null};this._stats?.time("Page Request");this._pumpOperatorList(n)}return a.opListReadCapability.promise}streamTextContent({includeMarkedContent:t=!1,disableNormalization:e=!1}={}){return this._transport.messageHandler.sendWithStream("GetTextContent",{pageIndex:this._pageIndex,includeMarkedContent:!0===t,disableNormalization:!0===e},{highWaterMark:100,size:t=>t.items.length})}getTextContent(t={}){if(this._transport._htmlForXfa)return this.getXfa().then((t=>XfaText.textContent(t)));const e=this.streamTextContent(t);return new Promise((function(t,i){const s=e.getReader(),n={items:[],styles:Object.create(null),lang:null};!function pump(){s.read().then((function({value:e,done:i}){if(i)t(n);else{n.lang??=e.lang;Object.assign(n.styles,e.styles);n.items.push(...e.items);pump()}}),i)}()}))}getStructTree(){return this._transport.getStructTree(this._pageIndex)}_destroy(){this.destroyed=!0;const t=[];for(const e of this._intentStates.values()){this._abortOperatorList({intentState:e,reason:new Error("Page was destroyed."),force:!0});if(!e.opListReadCapability)for(const i of e.renderTasks){t.push(i.completed);i.cancel()}}this.objs.clear();this.#vs=!1;this.#ys();return Promise.all(t)}cleanup(t=!1){this.#vs=!0;const e=this.#ws(!1);t&&e&&(this._stats&&=new StatTimer);return e}#ws(t=!1){this.#ys();if(!this.#vs||this.destroyed)return!1;if(t){this.#bs=setTimeout((()=>{this.#bs=null;this.#ws(!1)}),5e3);return!1}for(const{renderTasks:t,operatorList:e}of this._intentStates.values())if(t.size>0||!e.lastChunk)return!1;this._intentStates.clear();this.objs.clear();this.#vs=!1;return!0}#ys(){if(this.#bs){clearTimeout(this.#bs);this.#bs=null}}_startRenderPage(t,e){const i=this._intentStates.get(e);if(i){this._stats?.timeEnd("Page Request");i.displayReadyCapability?.resolve(t)}}_renderPageChunk(t,e){for(let i=0,s=t.length;i<s;i++){e.operatorList.fnArray.push(t.fnArray[i]);e.operatorList.argsArray.push(t.argsArray[i])}e.operatorList.lastChunk=t.lastChunk;e.operatorList.separateAnnots=t.separateAnnots;for(const t of e.renderTasks)t.operatorListChanged();t.lastChunk&&this.#ws(!0)}_pumpOperatorList({renderingIntent:t,cacheKey:e,annotationStorageSerializable:i,modifiedIds:s}){const{map:n,transfer:r}=i,a=this._transport.messageHandler.sendWithStream("GetOperatorList",{pageIndex:this._pageIndex,intent:t,cacheKey:e,annotationStorage:n,modifiedIds:s},r).getReader(),o=this._intentStates.get(e);o.streamReader=a;const pump=()=>{a.read().then((({value:t,done:e})=>{if(e)o.streamReader=null;else if(!this._transport.destroyed){this._renderPageChunk(t,o);pump()}}),(t=>{o.streamReader=null;if(!this._transport.destroyed){if(o.operatorList){o.operatorList.lastChunk=!0;for(const t of o.renderTasks)t.operatorListChanged();this.#ws(!0)}if(o.displayReadyCapability)o.displayReadyCapability.reject(t);else{if(!o.opListReadCapability)throw t;o.opListReadCapability.reject(t)}}}))};pump()}_abortOperatorList({intentState:t,reason:e,force:i=!1}){if(t.streamReader){if(t.streamReaderCancelTimeout){clearTimeout(t.streamReaderCancelTimeout);t.streamReaderCancelTimeout=null}if(!i){if(t.renderTasks.size>0)return;if(e instanceof RenderingCancelledException){let i=100;e.extraDelay>0&&e.extraDelay<1e3&&(i+=e.extraDelay);t.streamReaderCancelTimeout=setTimeout((()=>{t.streamReaderCancelTimeout=null;this._abortOperatorList({intentState:t,reason:e,force:!0})}),i);return}}t.streamReader.cancel(new AbortException(e.message)).catch((()=>{}));t.streamReader=null;if(!this._transport.destroyed){for(const[e,i]of this._intentStates)if(i===t){this._intentStates.delete(e);break}this.cleanup()}}}get stats(){return this._stats}}class LoopbackPort{#As=new Map;#xs=Promise.resolve();postMessage(t,e){const i={data:structuredClone(t,e?{transfer:e}:null)};this.#xs.then((()=>{for(const[t]of this.#As)t.call(this,i)}))}addEventListener(t,e,i=null){let s=null;if(i?.signal instanceof AbortSignal){const{signal:n}=i;if(n.aborted){warn("LoopbackPort - cannot use an `aborted` signal.");return}const onAbort=()=>this.removeEventListener(t,e);s=()=>n.removeEventListener("abort",onAbort);n.addEventListener("abort",onAbort)}this.#As.set(e,s)}removeEventListener(t,e){const i=this.#As.get(e);i?.();this.#As.delete(e)}terminate(){for(const[,t]of this.#As)t?.();this.#As.clear()}}class PDFWorker{static#_s=0;static#Es=!1;static#Ss;static{if(i){this.#Es=!0;GlobalWorkerOptions.workerSrc||="./pdf.worker.mjs"}this._isSameOrigin=(t,e)=>{let i;try{i=new URL(t);if(!i.origin||"null"===i.origin)return!1}catch{return!1}const s=new URL(e,i);return i.origin===s.origin};this._createCDNWrapper=t=>{const e=`await import("${t}");`;return URL.createObjectURL(new Blob([e],{type:"text/javascript"}))}}constructor({name:t=null,port:e=null,verbosity:i=getVerbosityLevel()}={}){this.name=t;this.destroyed=!1;this.verbosity=i;this._readyCapability=Promise.withResolvers();this._port=null;this._webWorker=null;this._messageHandler=null;if(e){if(PDFWorker.#Ss?.has(e))throw new Error("Cannot use more than one PDFWorker per port.");(PDFWorker.#Ss||=new WeakMap).set(e,this);this._initializeFromPort(e)}else this._initialize()}get promise(){return this._readyCapability.promise}#Cs(){this._readyCapability.resolve();this._messageHandler.send("configure",{verbosity:this.verbosity})}get port(){return this._port}get messageHandler(){return this._messageHandler}_initializeFromPort(t){this._port=t;this._messageHandler=new MessageHandler("main","worker",t);this._messageHandler.on("ready",(function(){}));this.#Cs()}_initialize(){if(PDFWorker.#Es||PDFWorker.#Ts){this._setupFakeWorker();return}let{workerSrc:t}=PDFWorker;try{PDFWorker._isSameOrigin(window.location.href,t)||(t=PDFWorker._createCDNWrapper(new URL(t,window.location).href));const e=new Worker(t,{type:"module"}),i=new MessageHandler("main","worker",e),terminateEarly=()=>{s.abort();i.destroy();e.terminate();this.destroyed?this._readyCapability.reject(new Error("Worker was destroyed")):this._setupFakeWorker()},s=new AbortController;e.addEventListener("error",(()=>{this._webWorker||terminateEarly()}),{signal:s.signal});i.on("test",(t=>{s.abort();if(!this.destroyed&&t){this._messageHandler=i;this._port=e;this._webWorker=e;this.#Cs()}else terminateEarly()}));i.on("ready",(t=>{s.abort();if(this.destroyed)terminateEarly();else try{sendTest()}catch{this._setupFakeWorker()}}));const sendTest=()=>{const t=new Uint8Array;i.send("test",t,[t.buffer])};sendTest();return}catch{info("The worker has been disabled.")}this._setupFakeWorker()}_setupFakeWorker(){if(!PDFWorker.#Es){warn("Setting up fake worker.");PDFWorker.#Es=!0}PDFWorker._setupFakeWorkerGlobal.then((t=>{if(this.destroyed){this._readyCapability.reject(new Error("Worker was destroyed"));return}const e=new LoopbackPort;this._port=e;const i="fake"+PDFWorker.#_s++,s=new MessageHandler(i+"_worker",i,e);t.setup(s,e);this._messageHandler=new MessageHandler(i,i+"_worker",e);this.#Cs()})).catch((t=>{this._readyCapability.reject(new Error(`Setting up fake worker failed: "${t.message}".`))}))}destroy(){this.destroyed=!0;this._webWorker?.terminate();this._webWorker=null;PDFWorker.#Ss?.delete(this._port);this._port=null;this._messageHandler?.destroy();this._messageHandler=null}static fromPort(t){if(!t?.port)throw new Error("PDFWorker.fromPort - invalid method signature.");const e=this.#Ss?.get(t.port);if(e){if(e._pendingDestroy)throw new Error("PDFWorker.fromPort - the worker is being destroyed.\nPlease remember to await `PDFDocumentLoadingTask.destroy()`-calls.");return e}return new PDFWorker(t)}static get workerSrc(){if(GlobalWorkerOptions.workerSrc)return GlobalWorkerOptions.workerSrc;throw new Error('No "GlobalWorkerOptions.workerSrc" specified.')}static get#Ts(){try{return globalThis.pdfjsWorker?.WorkerMessageHandler||null}catch{return null}}static get _setupFakeWorkerGlobal(){return shadow(this,"_setupFakeWorkerGlobal",(async()=>{if(this.#Ts)return this.#Ts;return(await import(this.workerSrc)).WorkerMessageHandler})())}}class WorkerTransport{#Ms=new Map;#Ps=new Map;#Ds=new Map;#ks=new Map;#Rs=null;constructor(t,e,i,s,n){this.messageHandler=t;this.loadingTask=e;this.commonObjs=new PDFObjects;this.fontLoader=new FontLoader({ownerDocument:s.ownerDocument,styleElement:s.styleElement});this.loadingParams=s.loadingParams;this._params=s;this.canvasFactory=n.canvasFactory;this.filterFactory=n.filterFactory;this.cMapReaderFactory=n.cMapReaderFactory;this.standardFontDataFactory=n.standardFontDataFactory;this.destroyed=!1;this.destroyCapability=null;this._networkStream=i;this._fullReader=null;this._lastProgress=null;this.downloadInfoCapability=Promise.withResolvers();this.setupMessageHandler()}#Is(t,e=null){const i=this.#Ms.get(t);if(i)return i;const s=this.messageHandler.sendWithPromise(t,e);this.#Ms.set(t,s);return s}get annotationStorage(){return shadow(this,"annotationStorage",new AnnotationStorage)}getRenderingIntent(t,e=g.ENABLE,i=null,s=!1,n=!1){let r=o,f=lt;switch(t){case"any":r=a;break;case"display":break;case"print":r=l;break;default:warn(`getRenderingIntent - invalid intent: ${t}`)}const m=r&l&&i instanceof PrintAnnotationStorage?i:this.annotationStorage;switch(e){case g.DISABLE:r+=d;break;case g.ENABLE:break;case g.ENABLE_FORMS:r+=h;break;case g.ENABLE_STORAGE:r+=c;f=m.serializable;break;default:warn(`getRenderingIntent - invalid annotationMode: ${e}`)}s&&(r+=u);n&&(r+=p);const{ids:b,hash:v}=m.modifiedIds;return{renderingIntent:r,cacheKey:[r,f.hash,v].join("_"),annotationStorageSerializable:f,modifiedIds:b}}destroy(){if(this.destroyCapability)return this.destroyCapability.promise;this.destroyed=!0;this.destroyCapability=Promise.withResolvers();this.#Rs?.reject(new Error("Worker was destroyed during onPassword callback"));const t=[];for(const e of this.#Ps.values())t.push(e._destroy());this.#Ps.clear();this.#Ds.clear();this.#ks.clear();this.hasOwnProperty("annotationStorage")&&this.annotationStorage.resetModified();const e=this.messageHandler.sendWithPromise("Terminate",null);t.push(e);Promise.all(t).then((()=>{this.commonObjs.clear();this.fontLoader.clear();this.#Ms.clear();this.filterFactory.destroy();TextLayer.cleanup();this._networkStream?.cancelAllRequests(new AbortException("Worker was terminated."));this.messageHandler?.destroy();this.messageHandler=null;this.destroyCapability.resolve()}),this.destroyCapability.reject);return this.destroyCapability.promise}setupMessageHandler(){const{messageHandler:t,loadingTask:e}=this;t.on("GetReader",((t,e)=>{assert(this._networkStream,"GetReader - no `IPDFStream` instance available.");this._fullReader=this._networkStream.getFullReader();this._fullReader.onProgress=t=>{this._lastProgress={loaded:t.loaded,total:t.total}};e.onPull=()=>{this._fullReader.read().then((function({value:t,done:i}){if(i)e.close();else{assert(t instanceof ArrayBuffer,"GetReader - expected an ArrayBuffer.");e.enqueue(new Uint8Array(t),1,[t])}})).catch((t=>{e.error(t)}))};e.onCancel=t=>{this._fullReader.cancel(t);e.ready.catch((t=>{if(!this.destroyed)throw t}))}}));t.on("ReaderHeadersReady",(async t=>{await this._fullReader.headersReady;const{isStreamingSupported:i,isRangeSupported:s,contentLength:n}=this._fullReader;if(!i||!s){this._lastProgress&&e.onProgress?.(this._lastProgress);this._fullReader.onProgress=t=>{e.onProgress?.({loaded:t.loaded,total:t.total})}}return{isStreamingSupported:i,isRangeSupported:s,contentLength:n}}));t.on("GetRangeReader",((t,e)=>{assert(this._networkStream,"GetRangeReader - no `IPDFStream` instance available.");const i=this._networkStream.getRangeReader(t.begin,t.end);if(i){e.onPull=()=>{i.read().then((function({value:t,done:i}){if(i)e.close();else{assert(t instanceof ArrayBuffer,"GetRangeReader - expected an ArrayBuffer.");e.enqueue(new Uint8Array(t),1,[t])}})).catch((t=>{e.error(t)}))};e.onCancel=t=>{i.cancel(t);e.ready.catch((t=>{if(!this.destroyed)throw t}))}}else e.close()}));t.on("GetDoc",(({pdfInfo:t})=>{this._numPages=t.numPages;this._htmlForXfa=t.htmlForXfa;delete t.htmlForXfa;e._capability.resolve(new PDFDocumentProxy(t,this))}));t.on("DocException",(t=>{e._capability.reject(wrapReason(t))}));t.on("PasswordRequest",(t=>{this.#Rs=Promise.withResolvers();try{if(!e.onPassword)throw wrapReason(t);const updatePassword=t=>{t instanceof Error?this.#Rs.reject(t):this.#Rs.resolve({password:t})};e.onPassword(updatePassword,t.code)}catch(t){this.#Rs.reject(t)}return this.#Rs.promise}));t.on("DataLoaded",(t=>{e.onProgress?.({loaded:t.length,total:t.length});this.downloadInfoCapability.resolve(t)}));t.on("StartRenderPage",(t=>{if(this.destroyed)return;this.#Ps.get(t.pageIndex)._startRenderPage(t.transparency,t.cacheKey)}));t.on("commonobj",(([e,i,s])=>{if(this.destroyed)return null;if(this.commonObjs.has(e))return null;switch(i){case"Font":const{disableFontFace:n,fontExtraProperties:r,pdfBug:a}=this._params;if("error"in s){const t=s.error;warn(`Error during font loading: ${t}`);this.commonObjs.resolve(e,t);break}const o=a&&globalThis.FontInspector?.enabled?(t,e)=>globalThis.FontInspector.fontAdded(t,e):null,l=new FontFaceObject(s,{disableFontFace:n,fontExtraProperties:r,inspectFont:o});this.fontLoader.bind(l).catch((()=>t.sendWithPromise("FontFallback",{id:e}))).finally((()=>{!r&&l.data&&(l.data=null);this.commonObjs.resolve(e,l)}));break;case"CopyLocalImage":const{imageRef:h}=s;assert(h,"The imageRef must be defined.");for(const t of this.#Ps.values())for(const[,i]of t.objs)if(i?.ref===h){if(!i.dataLen)return null;this.commonObjs.resolve(e,structuredClone(i));return i.dataLen}break;case"FontPath":case"Image":case"Pattern":this.commonObjs.resolve(e,s);break;default:throw new Error(`Got unknown common object type ${i}`)}return null}));t.on("obj",(([t,e,i,s])=>{if(this.destroyed)return;const n=this.#Ps.get(e);if(!n.objs.has(t))if(0!==n._intentStates.size)switch(i){case"Image":n.objs.resolve(t,s);s?.dataLen>1e7&&(n._maybeCleanupAfterRender=!0);break;case"Pattern":n.objs.resolve(t,s);break;default:throw new Error(`Got unknown object type ${i}`)}else s?.bitmap?.close()}));t.on("DocProgress",(t=>{this.destroyed||e.onProgress?.({loaded:t.loaded,total:t.total})}));t.on("FetchBuiltInCMap",(async t=>{if(this.destroyed)throw new Error("Worker was destroyed.");if(!this.cMapReaderFactory)throw new Error("CMapReaderFactory not initialized, see the `useWorkerFetch` parameter.");return this.cMapReaderFactory.fetch(t)}));t.on("FetchStandardFontData",(async t=>{if(this.destroyed)throw new Error("Worker was destroyed.");if(!this.standardFontDataFactory)throw new Error("StandardFontDataFactory not initialized, see the `useWorkerFetch` parameter.");return this.standardFontDataFactory.fetch(t)}))}getData(){return this.messageHandler.sendWithPromise("GetData",null)}saveDocument(){this.annotationStorage.size<=0&&warn("saveDocument called while `annotationStorage` is empty, please use the getData-method instead.");const{map:t,transfer:e}=this.annotationStorage.serializable;return this.messageHandler.sendWithPromise("SaveDocument",{isPureXfa:!!this._htmlForXfa,numPages:this._numPages,annotationStorage:t,filename:this._fullReader?.filename??null},e).finally((()=>{this.annotationStorage.resetModified()}))}getPage(t){if(!Number.isInteger(t)||t<=0||t>this._numPages)return Promise.reject(new Error("Invalid page request."));const e=t-1,i=this.#Ds.get(e);if(i)return i;const s=this.messageHandler.sendWithPromise("GetPage",{pageIndex:e}).then((i=>{if(this.destroyed)throw new Error("Transport destroyed");i.refStr&&this.#ks.set(i.refStr,t);const s=new PDFPageProxy(e,i,this,this._params.pdfBug);this.#Ps.set(e,s);return s}));this.#Ds.set(e,s);return s}getPageIndex(t){return isRefProxy(t)?this.messageHandler.sendWithPromise("GetPageIndex",{num:t.num,gen:t.gen}):Promise.reject(new Error("Invalid pageIndex request."))}getAnnotations(t,e){return this.messageHandler.sendWithPromise("GetAnnotations",{pageIndex:t,intent:e})}getFieldObjects(){return this.#Is("GetFieldObjects")}hasJSActions(){return this.#Is("HasJSActions")}getCalculationOrderIds(){return this.messageHandler.sendWithPromise("GetCalculationOrderIds",null)}getDestinations(){return this.messageHandler.sendWithPromise("GetDestinations",null)}getDestination(t){return"string"!=typeof t?Promise.reject(new Error("Invalid destination request.")):this.messageHandler.sendWithPromise("GetDestination",{id:t})}getPageLabels(){return this.messageHandler.sendWithPromise("GetPageLabels",null)}getPageLayout(){return this.messageHandler.sendWithPromise("GetPageLayout",null)}getPageMode(){return this.messageHandler.sendWithPromise("GetPageMode",null)}getViewerPreferences(){return this.messageHandler.sendWithPromise("GetViewerPreferences",null)}getOpenAction(){return this.messageHandler.sendWithPromise("GetOpenAction",null)}getAttachments(){return this.messageHandler.sendWithPromise("GetAttachments",null)}getDocJSActions(){return this.#Is("GetDocJSActions")}getPageJSActions(t){return this.messageHandler.sendWithPromise("GetPageJSActions",{pageIndex:t})}getStructTree(t){return this.messageHandler.sendWithPromise("GetStructTree",{pageIndex:t})}getOutline(){return this.messageHandler.sendWithPromise("GetOutline",null)}getOptionalContentConfig(t){return this.#Is("GetOptionalContentConfig").then((e=>new OptionalContentConfig(e,t)))}getPermissions(){return this.messageHandler.sendWithPromise("GetPermissions",null)}getMetadata(){const t="GetMetadata",e=this.#Ms.get(t);if(e)return e;const i=this.messageHandler.sendWithPromise(t,null).then((t=>({info:t[0],metadata:t[1]?new Metadata(t[1]):null,contentDispositionFilename:this._fullReader?.filename??null,contentLength:this._fullReader?.contentLength??null})));this.#Ms.set(t,i);return i}getMarkInfo(){return this.messageHandler.sendWithPromise("GetMarkInfo",null)}async startCleanup(t=!1){if(!this.destroyed){await this.messageHandler.sendWithPromise("Cleanup",null);for(const t of this.#Ps.values()){if(!t.cleanup())throw new Error(`startCleanup: Page ${t.pageNumber} is currently rendering.`)}this.commonObjs.clear();t||this.fontLoader.clear();this.#Ms.clear();this.filterFactory.destroy(!0);TextLayer.cleanup()}}cachedPageNumber(t){if(!isRefProxy(t))return null;const e=0===t.gen?`${t.num}R`:`${t.num}R${t.gen}`;return this.#ks.get(e)??null}}const Nt=Symbol("INITIAL_DATA");class PDFObjects{#Fs=Object.create(null);#Ls(t){return this.#Fs[t]||={...Promise.withResolvers(),data:Nt}}get(t,e=null){if(e){const i=this.#Ls(t);i.promise.then((()=>e(i.data)));return null}const i=this.#Fs[t];if(!i||i.data===Nt)throw new Error(`Requesting object that isn't resolved yet ${t}.`);return i.data}has(t){const e=this.#Fs[t];return!!e&&e.data!==Nt}delete(t){const e=this.#Fs[t];if(!e||e.data===Nt)return!1;delete this.#Fs[t];return!0}resolve(t,e=null){const i=this.#Ls(t);i.data=e;i.resolve()}clear(){for(const t in this.#Fs){const{data:e}=this.#Fs[t];e?.bitmap?.close()}this.#Fs=Object.create(null)}*[Symbol.iterator](){for(const t in this.#Fs){const{data:e}=this.#Fs[t];e!==Nt&&(yield[t,e])}}}class RenderTask{#Os=null;constructor(t){this.#Os=t;this.onContinue=null}get promise(){return this.#Os.capability.promise}cancel(t=0){this.#Os.cancel(null,t)}get separateAnnots(){const{separateAnnots:t}=this.#Os.operatorList;if(!t)return!1;const{annotationCanvasMap:e}=this.#Os;return t.form||t.canvas&&e?.size>0}}class InternalRenderTask{#Ns=null;static#Bs=new WeakSet;constructor({callback:t,params:e,objs:i,commonObjs:s,annotationCanvasMap:n,operatorList:r,pageIndex:a,canvasFactory:o,filterFactory:l,useRequestAnimationFrame:h=!1,pdfBug:c=!1,pageColors:d=null}){this.callback=t;this.params=e;this.objs=i;this.commonObjs=s;this.annotationCanvasMap=n;this.operatorListIdx=null;this.operatorList=r;this._pageIndex=a;this.canvasFactory=o;this.filterFactory=l;this._pdfBug=c;this.pageColors=d;this.running=!1;this.graphicsReadyCallback=null;this.graphicsReady=!1;this._useRequestAnimationFrame=!0===h&&"undefined"!=typeof window;this.cancelled=!1;this.capability=Promise.withResolvers();this.task=new RenderTask(this);this._cancelBound=this.cancel.bind(this);this._continueBound=this._continue.bind(this);this._scheduleNextBound=this._scheduleNext.bind(this);this._nextBound=this._next.bind(this);this._canvas=e.canvasContext.canvas}get completed(){return this.capability.promise.catch((function(){}))}initializeGraphics({transparency:t=!1,optionalContentConfig:e}){if(this.cancelled)return;if(this._canvas){if(InternalRenderTask.#Bs.has(this._canvas))throw new Error("Cannot use the same canvas during multiple render() operations. Use different canvas or ensure previous operations were cancelled or completed.");InternalRenderTask.#Bs.add(this._canvas)}if(this._pdfBug&&globalThis.StepperManager?.enabled){this.stepper=globalThis.StepperManager.create(this._pageIndex);this.stepper.init(this.operatorList);this.stepper.nextBreakPoint=this.stepper.getNextBreakPoint()}const{canvasContext:i,viewport:s,transform:n,background:r}=this.params;this.gfx=new CanvasGraphics(i,this.commonObjs,this.objs,this.canvasFactory,this.filterFactory,{optionalContentConfig:e},this.annotationCanvasMap,this.pageColors);this.gfx.beginDrawing({transform:n,viewport:s,transparency:t,background:r});this.operatorListIdx=0;this.graphicsReady=!0;this.graphicsReadyCallback?.()}cancel(t=null,e=0){this.running=!1;this.cancelled=!0;this.gfx?.endDrawing();if(this.#Ns){window.cancelAnimationFrame(this.#Ns);this.#Ns=null}InternalRenderTask.#Bs.delete(this._canvas);this.callback(t||new RenderingCancelledException(`Rendering cancelled, page ${this._pageIndex+1}`,e))}operatorListChanged(){if(this.graphicsReady){this.stepper?.updateOperatorList(this.operatorList);this.running||this._continue()}else this.graphicsReadyCallback||=this._continueBound}_continue(){this.running=!0;this.cancelled||(this.task.onContinue?this.task.onContinue(this._scheduleNextBound):this._scheduleNext())}_scheduleNext(){this._useRequestAnimationFrame?this.#Ns=window.requestAnimationFrame((()=>{this.#Ns=null;this._nextBound().catch(this._cancelBound)})):Promise.resolve().then(this._nextBound).catch(this._cancelBound)}async _next(){if(!this.cancelled){this.operatorListIdx=this.gfx.executeOperatorList(this.operatorList,this.operatorListIdx,this._continueBound,this.stepper);if(this.operatorListIdx===this.operatorList.argsArray.length){this.running=!1;if(this.operatorList.lastChunk){this.gfx.endDrawing();InternalRenderTask.#Bs.delete(this._canvas);this.callback()}}}}}const Bt="4.10.38",Ht="f9bea397f";__webpack_require__(670);function makeColorComp(t){return Math.floor(255*Math.max(0,Math.min(1,t))).toString(16).padStart(2,"0")}function scaleAndClamp(t){return Math.max(0,Math.min(255,255*t))}class ColorConverters{static CMYK_G([t,e,i,s]){return["G",1-Math.min(1,.3*t+.59*i+.11*e+s)]}static G_CMYK([t]){return["CMYK",0,0,0,1-t]}static G_RGB([t]){return["RGB",t,t,t]}static G_rgb([t]){return[t=scaleAndClamp(t),t,t]}static G_HTML([t]){const e=makeColorComp(t);return`#${e}${e}${e}`}static RGB_G([t,e,i]){return["G",.3*t+.59*e+.11*i]}static RGB_rgb(t){return t.map(scaleAndClamp)}static RGB_HTML(t){return`#${t.map(makeColorComp).join("")}`}static T_HTML(){return"#00000000"}static T_rgb(){return[null]}static CMYK_RGB([t,e,i,s]){return["RGB",1-Math.min(1,t+s),1-Math.min(1,i+s),1-Math.min(1,e+s)]}static CMYK_rgb([t,e,i,s]){return[scaleAndClamp(1-Math.min(1,t+s)),scaleAndClamp(1-Math.min(1,i+s)),scaleAndClamp(1-Math.min(1,e+s))]}static CMYK_HTML(t){const e=this.CMYK_RGB(t).slice(1);return this.RGB_HTML(e)}static RGB_CMYK([t,e,i]){const s=1-t,n=1-e,r=1-i;return["CMYK",s,n,r,Math.min(s,n,r)]}}class BaseSVGFactory{create(t,e,i=!1){if(t<=0||e<=0)throw new Error("Invalid SVG dimensions");const s=this._createSVG("svg:svg");s.setAttribute("version","1.1");if(!i){s.setAttribute("width",`${t}px`);s.setAttribute("height",`${e}px`)}s.setAttribute("preserveAspectRatio","none");s.setAttribute("viewBox",`0 0 ${t} ${e}`);return s}createElement(t){if("string"!=typeof t)throw new Error("Invalid SVG element type");return this._createSVG(t)}_createSVG(t){unreachable("Abstract method `_createSVG` called.")}}class DOMSVGFactory extends BaseSVGFactory{_createSVG(t){return document.createElementNS(nt,t)}}class XfaLayer{static setupStorage(t,e,i,s,n){const r=s.getValue(e,{value:null});switch(i.name){case"textarea":null!==r.value&&(t.textContent=r.value);if("print"===n)break;t.addEventListener("input",(t=>{s.setValue(e,{value:t.target.value})}));break;case"input":if("radio"===i.attributes.type||"checkbox"===i.attributes.type){r.value===i.attributes.xfaOn?t.setAttribute("checked",!0):r.value===i.attributes.xfaOff&&t.removeAttribute("checked");if("print"===n)break;t.addEventListener("change",(t=>{s.setValue(e,{value:t.target.checked?t.target.getAttribute("xfaOn"):t.target.getAttribute("xfaOff")})}))}else{null!==r.value&&t.setAttribute("value",r.value);if("print"===n)break;t.addEventListener("input",(t=>{s.setValue(e,{value:t.target.value})}))}break;case"select":if(null!==r.value){t.setAttribute("value",r.value);for(const t of i.children)t.attributes.value===r.value?t.attributes.selected=!0:t.attributes.hasOwnProperty("selected")&&delete t.attributes.selected}t.addEventListener("input",(t=>{const i=t.target.options,n=-1===i.selectedIndex?"":i[i.selectedIndex].value;s.setValue(e,{value:n})}))}}static setAttributes({html:t,element:e,storage:i=null,intent:s,linkService:n}){const{attributes:r}=e,a=t instanceof HTMLAnchorElement;"radio"===r.type&&(r.name=`${r.name}-${s}`);for(const[e,i]of Object.entries(r))if(null!=i)switch(e){case"class":i.length&&t.setAttribute(e,i.join(" "));break;case"dataId":break;case"id":t.setAttribute("data-element-id",i);break;case"style":Object.assign(t.style,i);break;case"textContent":t.textContent=i;break;default:(!a||"href"!==e&&"newWindow"!==e)&&t.setAttribute(e,i)}a&&n.addLinkAttributes(t,r.href,r.newWindow);i&&r.dataId&&this.setupStorage(t,r.dataId,e,i)}static render(t){const e=t.annotationStorage,i=t.linkService,s=t.xfaHtml,n=t.intent||"display",r=document.createElement(s.name);s.attributes&&this.setAttributes({html:r,element:s,intent:n,linkService:i});const a="richText"!==n,o=t.div;o.append(r);if(t.viewport){const e=`matrix(${t.viewport.transform.join(",")})`;o.style.transform=e}a&&o.setAttribute("class","xfaLayer xfaFont");const l=[];if(0===s.children.length){if(s.value){const t=document.createTextNode(s.value);r.append(t);a&&XfaText.shouldBuildText(s.name)&&l.push(t)}return{textDivs:l}}const h=[[s,-1,r]];for(;h.length>0;){const[t,s,r]=h.at(-1);if(s+1===t.children.length){h.pop();continue}const o=t.children[++h.at(-1)[1]];if(null===o)continue;const{name:c}=o;if("#text"===c){const t=document.createTextNode(o.value);l.push(t);r.append(t);continue}const d=o?.attributes?.xmlns?document.createElementNS(o.attributes.xmlns,c):document.createElement(c);r.append(d);o.attributes&&this.setAttributes({html:d,element:o,storage:e,intent:n,linkService:i});if(o.children?.length>0)h.push([o,-1,d]);else if(o.value){const t=document.createTextNode(o.value);a&&XfaText.shouldBuildText(c)&&l.push(t);d.append(t)}}for(const t of o.querySelectorAll(".xfaNonInteractive input, .xfaNonInteractive textarea"))t.setAttribute("readOnly",!0);return{textDivs:l}}static update(t){const e=`matrix(${t.viewport.transform.join(",")})`;t.div.style.transform=e;t.div.hidden=!1}}const Ut=1e3,zt=new WeakSet;function getRectDims(t){return{width:t[2]-t[0],height:t[3]-t[1]}}class AnnotationElementFactory{static create(t){switch(t.data.annotationType){case C:return new LinkAnnotationElement(t);case S:return new TextAnnotationElement(t);case G:switch(t.data.fieldType){case"Tx":return new TextWidgetAnnotationElement(t);case"Btn":return t.data.radioButton?new RadioButtonWidgetAnnotationElement(t):t.data.checkBox?new CheckboxWidgetAnnotationElement(t):new PushButtonWidgetAnnotationElement(t);case"Ch":return new ChoiceWidgetAnnotationElement(t);case"Sig":return new SignatureWidgetAnnotationElement(t)}return new WidgetAnnotationElement(t);case z:return new PopupAnnotationElement(t);case T:return new FreeTextAnnotationElement(t);case M:return new LineAnnotationElement(t);case P:return new SquareAnnotationElement(t);case D:return new CircleAnnotationElement(t);case R:return new PolylineAnnotationElement(t);case H:return new CaretAnnotationElement(t);case U:return new InkAnnotationElement(t);case k:return new PolygonAnnotationElement(t);case I:return new HighlightAnnotationElement(t);case L:return new UnderlineAnnotationElement(t);case O:return new SquigglyAnnotationElement(t);case N:return new StrikeOutAnnotationElement(t);case B:return new StampAnnotationElement(t);case j:return new FileAttachmentAnnotationElement(t);default:return new AnnotationElement(t)}}}class AnnotationElement{#Hs=null;#Us=!1;#zs=null;constructor(t,{isRenderable:e=!1,ignoreBorder:i=!1,createQuadrilaterals:s=!1}={}){this.isRenderable=e;this.data=t.data;this.layer=t.layer;this.linkService=t.linkService;this.downloadManager=t.downloadManager;this.imageResourcesPath=t.imageResourcesPath;this.renderForms=t.renderForms;this.svgFactory=t.svgFactory;this.annotationStorage=t.annotationStorage;this.enableScripting=t.enableScripting;this.hasJSActions=t.hasJSActions;this._fieldObjects=t.fieldObjects;this.parent=t.parent;e&&(this.container=this._createContainer(i));s&&this._createQuadrilaterals()}static _hasPopupData({titleObj:t,contentsObj:e,richText:i}){return!!(t?.str||e?.str||i?.str)}get _isEditable(){return this.data.isEditable}get hasPopupData(){return AnnotationElement._hasPopupData(this.data)}updateEdited(t){if(!this.container)return;this.#Hs||={rect:this.data.rect.slice(0)};const{rect:e}=t;e&&this.#js(e);this.#zs?.popup.updateEdited(t)}resetEdited(){if(this.#Hs){this.#js(this.#Hs.rect);this.#zs?.popup.resetEdited();this.#Hs=null}}#js(t){const{container:{style:e},data:{rect:i,rotation:s},parent:{viewport:{rawDims:{pageWidth:n,pageHeight:r,pageX:a,pageY:o}}}}=this;i?.splice(0,4,...t);const{width:l,height:h}=getRectDims(t);e.left=100*(t[0]-a)/n+"%";e.top=100*(r-t[3]+o)/r+"%";if(0===s){e.width=100*l/n+"%";e.height=100*h/r+"%"}else this.setRotation(s)}_createContainer(t){const{data:e,parent:{page:i,viewport:s}}=this,n=document.createElement("section");n.setAttribute("data-annotation-id",e.id);this instanceof WidgetAnnotationElement||(n.tabIndex=Ut);const{style:r}=n;r.zIndex=this.parent.zIndex++;e.alternativeText&&(n.title=e.alternativeText);e.noRotate&&n.classList.add("norotate");if(!e.rect||this instanceof PopupAnnotationElement){const{rotation:t}=e;e.hasOwnCanvas||0===t||this.setRotation(t,n);return n}const{width:a,height:o}=getRectDims(e.rect);if(!t&&e.borderStyle.width>0){r.borderWidth=`${e.borderStyle.width}px`;const t=e.borderStyle.horizontalCornerRadius,i=e.borderStyle.verticalCornerRadius;if(t>0||i>0){const e=`calc(${t}px * var(--scale-factor)) / calc(${i}px * var(--scale-factor))`;r.borderRadius=e}else if(this instanceof RadioButtonWidgetAnnotationElement){const t=`calc(${a}px * var(--scale-factor)) / calc(${o}px * var(--scale-factor))`;r.borderRadius=t}switch(e.borderStyle.style){case V:r.borderStyle="solid";break;case $:r.borderStyle="dashed";break;case W:warn("Unimplemented border style: beveled");break;case q:warn("Unimplemented border style: inset");break;case X:r.borderBottomStyle="solid"}const s=e.borderColor||null;if(s){this.#Us=!0;r.borderColor=Util.makeHexColor(0|s[0],0|s[1],0|s[2])}else r.borderWidth=0}const l=Util.normalizeRect([e.rect[0],i.view[3]-e.rect[1]+i.view[1],e.rect[2],i.view[3]-e.rect[3]+i.view[1]]),{pageWidth:h,pageHeight:c,pageX:d,pageY:u}=s.rawDims;r.left=100*(l[0]-d)/h+"%";r.top=100*(l[1]-u)/c+"%";const{rotation:p}=e;if(e.hasOwnCanvas||0===p){r.width=100*a/h+"%";r.height=100*o/c+"%"}else this.setRotation(p,n);return n}setRotation(t,e=this.container){if(!this.data.rect)return;const{pageWidth:i,pageHeight:s}=this.parent.viewport.rawDims,{width:n,height:r}=getRectDims(this.data.rect);let a,o;if(t%180==0){a=100*n/i;o=100*r/s}else{a=100*r/i;o=100*n/s}e.style.width=`${a}%`;e.style.height=`${o}%`;e.setAttribute("data-main-rotation",(360-t)%360)}get _commonActions(){const setColor=(t,e,i)=>{const s=i.detail[t],n=s[0],r=s.slice(1);i.target.style[e]=ColorConverters[`${n}_HTML`](r);this.annotationStorage.setValue(this.data.id,{[e]:ColorConverters[`${n}_rgb`](r)})};return shadow(this,"_commonActions",{display:t=>{const{display:e}=t.detail,i=e%2==1;this.container.style.visibility=i?"hidden":"visible";this.annotationStorage.setValue(this.data.id,{noView:i,noPrint:1===e||2===e})},print:t=>{this.annotationStorage.setValue(this.data.id,{noPrint:!t.detail.print})},hidden:t=>{const{hidden:e}=t.detail;this.container.style.visibility=e?"hidden":"visible";this.annotationStorage.setValue(this.data.id,{noPrint:e,noView:e})},focus:t=>{setTimeout((()=>t.target.focus({preventScroll:!1})),0)},userName:t=>{t.target.title=t.detail.userName},readonly:t=>{t.target.disabled=t.detail.readonly},required:t=>{this._setRequired(t.target,t.detail.required)},bgColor:t=>{setColor("bgColor","backgroundColor",t)},fillColor:t=>{setColor("fillColor","backgroundColor",t)},fgColor:t=>{setColor("fgColor","color",t)},textColor:t=>{setColor("textColor","color",t)},borderColor:t=>{setColor("borderColor","borderColor",t)},strokeColor:t=>{setColor("strokeColor","borderColor",t)},rotation:t=>{const e=t.detail.rotation;this.setRotation(e);this.annotationStorage.setValue(this.data.id,{rotation:e})}})}_dispatchEventFromSandbox(t,e){const i=this._commonActions;for(const s of Object.keys(e.detail)){const n=t[s]||i[s];n?.(e)}}_setDefaultPropertiesFromJS(t){if(!this.enableScripting)return;const e=this.annotationStorage.getRawValue(this.data.id);if(!e)return;const i=this._commonActions;for(const[s,n]of Object.entries(e)){const r=i[s];if(r){r({detail:{[s]:n},target:t});delete e[s]}}}_createQuadrilaterals(){if(!this.container)return;const{quadPoints:t}=this.data;if(!t)return;const[e,i,s,n]=this.data.rect.map((t=>Math.fround(t)));if(8===t.length){const[r,a,o,l]=t.subarray(2,6);if(s===r&&n===a&&e===o&&i===l)return}const{style:r}=this.container;let a;if(this.#Us){const{borderColor:t,borderWidth:e}=r;r.borderWidth=0;a=["url('data:image/svg+xml;utf8,",'<svg xmlns="http://www.w3.org/2000/svg"',' preserveAspectRatio="none" viewBox="0 0 1 1">',`<g fill="transparent" stroke="${t}" stroke-width="${e}">`];this.container.classList.add("hasBorder")}const o=s-e,l=n-i,{svgFactory:h}=this,c=h.createElement("svg");c.classList.add("quadrilateralsContainer");c.setAttribute("width",0);c.setAttribute("height",0);const d=h.createElement("defs");c.append(d);const u=h.createElement("clipPath"),p=`clippath_${this.data.id}`;u.setAttribute("id",p);u.setAttribute("clipPathUnits","objectBoundingBox");d.append(u);for(let i=2,s=t.length;i<s;i+=8){const s=t[i],r=t[i+1],c=t[i+2],d=t[i+3],p=h.createElement("rect"),g=(c-e)/o,f=(n-r)/l,m=(s-c)/o,b=(r-d)/l;p.setAttribute("x",g);p.setAttribute("y",f);p.setAttribute("width",m);p.setAttribute("height",b);u.append(p);a?.push(`<rect vector-effect="non-scaling-stroke" x="${g}" y="${f}" width="${m}" height="${b}"/>`)}if(this.#Us){a.push("</g></svg>')");r.backgroundImage=a.join("")}this.container.append(c);this.container.style.clipPath=`url(#${p})`}_createPopup(){const{data:t}=this,e=this.#zs=new PopupAnnotationElement({data:{color:t.color,titleObj:t.titleObj,modificationDate:t.modificationDate,contentsObj:t.contentsObj,richText:t.richText,parentRect:t.rect,borderStyle:0,id:`popup_${t.id}`,rotation:t.rotation},parent:this.parent,elements:[this]});this.parent.div.append(e.render())}render(){unreachable("Abstract method `AnnotationElement.render` called")}_getElementsByName(t,e=null){const i=[];if(this._fieldObjects){const s=this._fieldObjects[t];if(s)for(const{page:t,id:n,exportValues:r}of s){if(-1===t)continue;if(n===e)continue;const s="string"==typeof r?r:null,a=document.querySelector(`[data-element-id="${n}"]`);!a||zt.has(a)?i.push({id:n,exportValue:s,domElement:a}):warn(`_getElementsByName - element not allowed: ${n}`)}return i}for(const s of document.getElementsByName(t)){const{exportValue:t}=s,n=s.getAttribute("data-element-id");n!==e&&(zt.has(s)&&i.push({id:n,exportValue:t,domElement:s}))}return i}show(){this.container&&(this.container.hidden=!1);this.popup?.maybeShow()}hide(){this.container&&(this.container.hidden=!0);this.popup?.forceHide()}getElementsToTriggerPopup(){return this.container}addHighlightArea(){const t=this.getElementsToTriggerPopup();if(Array.isArray(t))for(const e of t)e.classList.add("highlightArea");else t.classList.add("highlightArea")}_editOnDoubleClick(){if(!this._isEditable)return;const{annotationEditorType:t,data:{id:e}}=this;this.container.addEventListener("dblclick",(()=>{this.linkService.eventBus?.dispatch("switchannotationeditormode",{source:this,mode:t,editId:e})}))}}class LinkAnnotationElement extends AnnotationElement{constructor(t,e=null){super(t,{isRenderable:!0,ignoreBorder:!!e?.ignoreBorder,createQuadrilaterals:!0});this.isTooltipOnly=t.data.isTooltipOnly}render(){const{data:t,linkService:e}=this,i=document.createElement("a");i.setAttribute("data-element-id",t.id);let s=!1;if(t.url){e.addLinkAttributes(i,t.url,t.newWindow);s=!0}else if(t.action){this._bindNamedAction(i,t.action);s=!0}else if(t.attachment){this.#Gs(i,t.attachment,t.attachmentDest);s=!0}else if(t.setOCGState){this.#Vs(i,t.setOCGState);s=!0}else if(t.dest){this._bindLink(i,t.dest);s=!0}else{if(t.actions&&(t.actions.Action||t.actions["Mouse Up"]||t.actions["Mouse Down"])&&this.enableScripting&&this.hasJSActions){this._bindJSAction(i,t);s=!0}if(t.resetForm){this._bindResetFormAction(i,t.resetForm);s=!0}else if(this.isTooltipOnly&&!s){this._bindLink(i,"");s=!0}}this.container.classList.add("linkAnnotation");s&&this.container.append(i);return this.container}#$s(){this.container.setAttribute("data-internal-link","")}_bindLink(t,e){t.href=this.linkService.getDestinationHash(e);t.onclick=()=>{e&&this.linkService.goToDestination(e);return!1};(e||""===e)&&this.#$s()}_bindNamedAction(t,e){t.href=this.linkService.getAnchorUrl("");t.onclick=()=>{this.linkService.executeNamedAction(e);return!1};this.#$s()}#Gs(t,e,i=null){t.href=this.linkService.getAnchorUrl("");e.description&&(t.title=e.description);t.onclick=()=>{this.downloadManager?.openOrDownloadData(e.content,e.filename,i);return!1};this.#$s()}#Vs(t,e){t.href=this.linkService.getAnchorUrl("");t.onclick=()=>{this.linkService.executeSetOCGState(e);return!1};this.#$s()}_bindJSAction(t,e){t.href=this.linkService.getAnchorUrl("");const i=new Map([["Action","onclick"],["Mouse Up","onmouseup"],["Mouse Down","onmousedown"]]);for(const s of Object.keys(e.actions)){const n=i.get(s);n&&(t[n]=()=>{this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e.id,name:s}});return!1})}t.onclick||(t.onclick=()=>!1);this.#$s()}_bindResetFormAction(t,e){const i=t.onclick;i||(t.href=this.linkService.getAnchorUrl(""));this.#$s();if(this._fieldObjects)t.onclick=()=>{i?.();const{fields:t,refs:s,include:n}=e,r=[];if(0!==t.length||0!==s.length){const e=new Set(s);for(const i of t){const t=this._fieldObjects[i]||[];for(const{id:i}of t)e.add(i)}for(const t of Object.values(this._fieldObjects))for(const i of t)e.has(i.id)===n&&r.push(i)}else for(const t of Object.values(this._fieldObjects))r.push(...t);const a=this.annotationStorage,o=[];for(const t of r){const{id:e}=t;o.push(e);switch(t.type){case"text":{const i=t.defaultValue||"";a.setValue(e,{value:i});break}case"checkbox":case"radiobutton":{const i=t.defaultValue===t.exportValues;a.setValue(e,{value:i});break}case"combobox":case"listbox":{const i=t.defaultValue||"";a.setValue(e,{value:i});break}default:continue}const i=document.querySelector(`[data-element-id="${e}"]`);i&&(zt.has(i)?i.dispatchEvent(new Event("resetform")):warn(`_bindResetFormAction - element not allowed: ${e}`))}this.enableScripting&&this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:"app",ids:o,name:"ResetForm"}});return!1};else{warn('_bindResetFormAction - "resetForm" action not supported, ensure that the `fieldObjects` parameter is provided.');i||(t.onclick=()=>!1)}}}class TextAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!0})}render(){this.container.classList.add("textAnnotation");const t=document.createElement("img");t.src=this.imageResourcesPath+"annotation-"+this.data.name.toLowerCase()+".svg";t.setAttribute("data-l10n-id","pdfjs-text-annotation-type");t.setAttribute("data-l10n-args",JSON.stringify({type:this.data.name}));!this.data.popupRef&&this.hasPopupData&&this._createPopup();this.container.append(t);return this.container}}class WidgetAnnotationElement extends AnnotationElement{render(){return this.container}showElementAndHideCanvas(t){if(this.data.hasOwnCanvas){"CANVAS"===t.previousSibling?.nodeName&&(t.previousSibling.hidden=!0);t.hidden=!1}}_getKeyModifier(t){return util_FeatureTest.platform.isMac?t.metaKey:t.ctrlKey}_setEventListener(t,e,i,s,n){i.includes("mouse")?t.addEventListener(i,(t=>{this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:s,value:n(t),shift:t.shiftKey,modifier:this._getKeyModifier(t)}})})):t.addEventListener(i,(t=>{if("blur"===i){if(!e.focused||!t.relatedTarget)return;e.focused=!1}else if("focus"===i){if(e.focused)return;e.focused=!0}n&&this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:s,value:n(t)}})}))}_setEventListeners(t,e,i,s){for(const[n,r]of i)if("Action"===r||this.data.actions?.[r]){"Focus"!==r&&"Blur"!==r||(e||={focused:!1});this._setEventListener(t,e,n,r,s);"Focus"!==r||this.data.actions?.Blur?"Blur"!==r||this.data.actions?.Focus||this._setEventListener(t,e,"focus","Focus",null):this._setEventListener(t,e,"blur","Blur",null)}}_setBackgroundColor(t){const e=this.data.backgroundColor||null;t.style.backgroundColor=null===e?"transparent":Util.makeHexColor(e[0],e[1],e[2])}_setTextStyle(t){const e=["left","center","right"],{fontColor:i}=this.data.defaultAppearanceData,s=this.data.defaultAppearanceData.fontSize||9,n=t.style;let a;const roundToOneDecimal=t=>Math.round(10*t)/10;if(this.data.multiLine){const t=Math.abs(this.data.rect[3]-this.data.rect[1]-2),e=t/(Math.round(t/(r*s))||1);a=Math.min(s,roundToOneDecimal(e/r))}else{const t=Math.abs(this.data.rect[3]-this.data.rect[1]-2);a=Math.min(s,roundToOneDecimal(t/r))}n.fontSize=`calc(${a}px * var(--scale-factor))`;n.color=Util.makeHexColor(i[0],i[1],i[2]);null!==this.data.textAlignment&&(n.textAlign=e[this.data.textAlignment])}_setRequired(t,e){e?t.setAttribute("required",!0):t.removeAttribute("required");t.setAttribute("aria-required",e)}}class TextWidgetAnnotationElement extends WidgetAnnotationElement{constructor(t){super(t,{isRenderable:t.renderForms||t.data.hasOwnCanvas||!t.data.hasAppearance&&!!t.data.fieldValue})}setPropertyOnSiblings(t,e,i,s){const n=this.annotationStorage;for(const r of this._getElementsByName(t.name,t.id)){r.domElement&&(r.domElement[e]=i);n.setValue(r.id,{[s]:i})}}render(){const t=this.annotationStorage,e=this.data.id;this.container.classList.add("textWidgetAnnotation");let i=null;if(this.renderForms){const s=t.getValue(e,{value:this.data.fieldValue});let n=s.value||"";const r=t.getValue(e,{charLimit:this.data.maxLen}).charLimit;r&&n.length>r&&(n=n.slice(0,r));let a=s.formattedValue||this.data.textContent?.join("\n")||null;a&&this.data.comb&&(a=a.replaceAll(/\s+/g,""));const o={userValue:n,formattedValue:a,lastCommittedValue:null,commitKey:1,focused:!1};if(this.data.multiLine){i=document.createElement("textarea");i.textContent=a??n;this.data.doNotScroll&&(i.style.overflowY="hidden")}else{i=document.createElement("input");i.type="text";i.setAttribute("value",a??n);this.data.doNotScroll&&(i.style.overflowX="hidden")}this.data.hasOwnCanvas&&(i.hidden=!0);zt.add(i);i.setAttribute("data-element-id",e);i.disabled=this.data.readOnly;i.name=this.data.fieldName;i.tabIndex=Ut;this._setRequired(i,this.data.required);r&&(i.maxLength=r);i.addEventListener("input",(s=>{t.setValue(e,{value:s.target.value});this.setPropertyOnSiblings(i,"value",s.target.value,"value");o.formattedValue=null}));i.addEventListener("resetform",(t=>{const e=this.data.defaultFieldValue??"";i.value=o.userValue=e;o.formattedValue=null}));let blurListener=t=>{const{formattedValue:e}=o;null!=e&&(t.target.value=e);t.target.scrollLeft=0};if(this.enableScripting&&this.hasJSActions){i.addEventListener("focus",(t=>{if(o.focused)return;const{target:e}=t;o.userValue&&(e.value=o.userValue);o.lastCommittedValue=e.value;o.commitKey=1;this.data.actions?.Focus||(o.focused=!0)}));i.addEventListener("updatefromsandbox",(i=>{this.showElementAndHideCanvas(i.target);const s={value(i){o.userValue=i.detail.value??"";t.setValue(e,{value:o.userValue.toString()});i.target.value=o.userValue},formattedValue(i){const{formattedValue:s}=i.detail;o.formattedValue=s;null!=s&&i.target!==document.activeElement&&(i.target.value=s);t.setValue(e,{formattedValue:s})},selRange(t){t.target.setSelectionRange(...t.detail.selRange)},charLimit:i=>{const{charLimit:s}=i.detail,{target:n}=i;if(0===s){n.removeAttribute("maxLength");return}n.setAttribute("maxLength",s);let r=o.userValue;if(r&&!(r.length<=s)){r=r.slice(0,s);n.value=o.userValue=r;t.setValue(e,{value:r});this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:r,willCommit:!0,commitKey:1,selStart:n.selectionStart,selEnd:n.selectionEnd}})}}};this._dispatchEventFromSandbox(s,i)}));i.addEventListener("keydown",(t=>{o.commitKey=1;let i=-1;"Escape"===t.key?i=0:"Enter"!==t.key||this.data.multiLine?"Tab"===t.key&&(o.commitKey=3):i=2;if(-1===i)return;const{value:s}=t.target;if(o.lastCommittedValue!==s){o.lastCommittedValue=s;o.userValue=s;this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:s,willCommit:!0,commitKey:i,selStart:t.target.selectionStart,selEnd:t.target.selectionEnd}})}}));const s=blurListener;blurListener=null;i.addEventListener("blur",(t=>{if(!o.focused||!t.relatedTarget)return;this.data.actions?.Blur||(o.focused=!1);const{value:i}=t.target;o.userValue=i;o.lastCommittedValue!==i&&this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:i,willCommit:!0,commitKey:o.commitKey,selStart:t.target.selectionStart,selEnd:t.target.selectionEnd}});s(t)}));this.data.actions?.Keystroke&&i.addEventListener("beforeinput",(t=>{o.lastCommittedValue=null;const{data:i,target:s}=t,{value:n,selectionStart:r,selectionEnd:a}=s;let l=r,h=a;switch(t.inputType){case"deleteWordBackward":{const t=n.substring(0,r).match(/\w*[^\w]*$/);t&&(l-=t[0].length);break}case"deleteWordForward":{const t=n.substring(r).match(/^[^\w]*\w*/);t&&(h+=t[0].length);break}case"deleteContentBackward":r===a&&(l-=1);break;case"deleteContentForward":r===a&&(h+=1)}t.preventDefault();this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:n,change:i||"",willCommit:!1,selStart:l,selEnd:h}})}));this._setEventListeners(i,o,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],(t=>t.target.value))}blurListener&&i.addEventListener("blur",blurListener);if(this.data.comb){const t=(this.data.rect[2]-this.data.rect[0])/r;i.classList.add("comb");i.style.letterSpacing=`calc(${t}px * var(--scale-factor) - 1ch)`}}else{i=document.createElement("div");i.textContent=this.data.fieldValue;i.style.verticalAlign="middle";i.style.display="table-cell";this.data.hasOwnCanvas&&(i.hidden=!0)}this._setTextStyle(i);this._setBackgroundColor(i);this._setDefaultPropertiesFromJS(i);this.container.append(i);return this.container}}class SignatureWidgetAnnotationElement extends WidgetAnnotationElement{constructor(t){super(t,{isRenderable:!!t.data.hasOwnCanvas})}}class CheckboxWidgetAnnotationElement extends WidgetAnnotationElement{constructor(t){super(t,{isRenderable:t.renderForms})}render(){const t=this.annotationStorage,e=this.data,i=e.id;let s=t.getValue(i,{value:e.exportValue===e.fieldValue}).value;if("string"==typeof s){s="Off"!==s;t.setValue(i,{value:s})}this.container.classList.add("buttonWidgetAnnotation","checkBox");const n=document.createElement("input");zt.add(n);n.setAttribute("data-element-id",i);n.disabled=e.readOnly;this._setRequired(n,this.data.required);n.type="checkbox";n.name=e.fieldName;s&&n.setAttribute("checked",!0);n.setAttribute("exportValue",e.exportValue);n.tabIndex=Ut;n.addEventListener("change",(s=>{const{name:n,checked:r}=s.target;for(const s of this._getElementsByName(n,i)){const i=r&&s.exportValue===e.exportValue;s.domElement&&(s.domElement.checked=i);t.setValue(s.id,{value:i})}t.setValue(i,{value:r})}));n.addEventListener("resetform",(t=>{const i=e.defaultFieldValue||"Off";t.target.checked=i===e.exportValue}));if(this.enableScripting&&this.hasJSActions){n.addEventListener("updatefromsandbox",(e=>{const s={value(e){e.target.checked="Off"!==e.detail.value;t.setValue(i,{value:e.target.checked})}};this._dispatchEventFromSandbox(s,e)}));this._setEventListeners(n,null,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],(t=>t.target.checked))}this._setBackgroundColor(n);this._setDefaultPropertiesFromJS(n);this.container.append(n);return this.container}}class RadioButtonWidgetAnnotationElement extends WidgetAnnotationElement{constructor(t){super(t,{isRenderable:t.renderForms})}render(){this.container.classList.add("buttonWidgetAnnotation","radioButton");const t=this.annotationStorage,e=this.data,i=e.id;let s=t.getValue(i,{value:e.fieldValue===e.buttonValue}).value;if("string"==typeof s){s=s!==e.buttonValue;t.setValue(i,{value:s})}if(s)for(const s of this._getElementsByName(e.fieldName,i))t.setValue(s.id,{value:!1});const n=document.createElement("input");zt.add(n);n.setAttribute("data-element-id",i);n.disabled=e.readOnly;this._setRequired(n,this.data.required);n.type="radio";n.name=e.fieldName;s&&n.setAttribute("checked",!0);n.tabIndex=Ut;n.addEventListener("change",(e=>{const{name:s,checked:n}=e.target;for(const e of this._getElementsByName(s,i))t.setValue(e.id,{value:!1});t.setValue(i,{value:n})}));n.addEventListener("resetform",(t=>{const i=e.defaultFieldValue;t.target.checked=null!=i&&i===e.buttonValue}));if(this.enableScripting&&this.hasJSActions){const s=e.buttonValue;n.addEventListener("updatefromsandbox",(e=>{const n={value:e=>{const n=s===e.detail.value;for(const s of this._getElementsByName(e.target.name)){const e=n&&s.id===i;s.domElement&&(s.domElement.checked=e);t.setValue(s.id,{value:e})}}};this._dispatchEventFromSandbox(n,e)}));this._setEventListeners(n,null,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],(t=>t.target.checked))}this._setBackgroundColor(n);this._setDefaultPropertiesFromJS(n);this.container.append(n);return this.container}}class PushButtonWidgetAnnotationElement extends LinkAnnotationElement{constructor(t){super(t,{ignoreBorder:t.data.hasAppearance})}render(){const t=super.render();t.classList.add("buttonWidgetAnnotation","pushButton");const e=t.lastChild;if(this.enableScripting&&this.hasJSActions&&e){this._setDefaultPropertiesFromJS(e);e.addEventListener("updatefromsandbox",(t=>{this._dispatchEventFromSandbox({},t)}))}return t}}class ChoiceWidgetAnnotationElement extends WidgetAnnotationElement{constructor(t){super(t,{isRenderable:t.renderForms})}render(){this.container.classList.add("choiceWidgetAnnotation");const t=this.annotationStorage,e=this.data.id,i=t.getValue(e,{value:this.data.fieldValue}),s=document.createElement("select");zt.add(s);s.setAttribute("data-element-id",e);s.disabled=this.data.readOnly;this._setRequired(s,this.data.required);s.name=this.data.fieldName;s.tabIndex=Ut;let n=this.data.combo&&this.data.options.length>0;if(!this.data.combo){s.size=this.data.options.length;this.data.multiSelect&&(s.multiple=!0)}s.addEventListener("resetform",(t=>{const e=this.data.defaultFieldValue;for(const t of s.options)t.selected=t.value===e}));for(const t of this.data.options){const e=document.createElement("option");e.textContent=t.displayValue;e.value=t.exportValue;if(i.value.includes(t.exportValue)){e.setAttribute("selected",!0);n=!1}s.append(e)}let r=null;if(n){const t=document.createElement("option");t.value=" ";t.setAttribute("hidden",!0);t.setAttribute("selected",!0);s.prepend(t);r=()=>{t.remove();s.removeEventListener("input",r);r=null};s.addEventListener("input",r)}const getValue=t=>{const e=t?"value":"textContent",{options:i,multiple:n}=s;return n?Array.prototype.filter.call(i,(t=>t.selected)).map((t=>t[e])):-1===i.selectedIndex?null:i[i.selectedIndex][e]};let a=getValue(!1);const getItems=t=>{const e=t.target.options;return Array.prototype.map.call(e,(t=>({displayValue:t.textContent,exportValue:t.value})))};if(this.enableScripting&&this.hasJSActions){s.addEventListener("updatefromsandbox",(i=>{const n={value(i){r?.();const n=i.detail.value,o=new Set(Array.isArray(n)?n:[n]);for(const t of s.options)t.selected=o.has(t.value);t.setValue(e,{value:getValue(!0)});a=getValue(!1)},multipleSelection(t){s.multiple=!0},remove(i){const n=s.options,r=i.detail.remove;n[r].selected=!1;s.remove(r);if(n.length>0){-1===Array.prototype.findIndex.call(n,(t=>t.selected))&&(n[0].selected=!0)}t.setValue(e,{value:getValue(!0),items:getItems(i)});a=getValue(!1)},clear(i){for(;0!==s.length;)s.remove(0);t.setValue(e,{value:null,items:[]});a=getValue(!1)},insert(i){const{index:n,displayValue:r,exportValue:o}=i.detail.insert,l=s.children[n],h=document.createElement("option");h.textContent=r;h.value=o;l?l.before(h):s.append(h);t.setValue(e,{value:getValue(!0),items:getItems(i)});a=getValue(!1)},items(i){const{items:n}=i.detail;for(;0!==s.length;)s.remove(0);for(const t of n){const{displayValue:e,exportValue:i}=t,n=document.createElement("option");n.textContent=e;n.value=i;s.append(n)}s.options.length>0&&(s.options[0].selected=!0);t.setValue(e,{value:getValue(!0),items:getItems(i)});a=getValue(!1)},indices(i){const s=new Set(i.detail.indices);for(const t of i.target.options)t.selected=s.has(t.index);t.setValue(e,{value:getValue(!0)});a=getValue(!1)},editable(t){t.target.disabled=!t.detail.editable}};this._dispatchEventFromSandbox(n,i)}));s.addEventListener("input",(i=>{const s=getValue(!0),n=getValue(!1);t.setValue(e,{value:s});i.preventDefault();this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:a,change:n,changeEx:s,willCommit:!1,commitKey:1,keyDown:!1}})}));this._setEventListeners(s,null,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"],["input","Action"],["input","Validate"]],(t=>t.target.value))}else s.addEventListener("input",(function(i){t.setValue(e,{value:getValue(!0)})}));this.data.combo&&this._setTextStyle(s);this._setBackgroundColor(s);this._setDefaultPropertiesFromJS(s);this.container.append(s);return this.container}}class PopupAnnotationElement extends AnnotationElement{constructor(t){const{data:e,elements:i}=t;super(t,{isRenderable:AnnotationElement._hasPopupData(e)});this.elements=i;this.popup=null}render(){this.container.classList.add("popupAnnotation");const t=this.popup=new PopupElement({container:this.container,color:this.data.color,titleObj:this.data.titleObj,modificationDate:this.data.modificationDate,contentsObj:this.data.contentsObj,richText:this.data.richText,rect:this.data.rect,parentRect:this.data.parentRect||null,parent:this.parent,elements:this.elements,open:this.data.open}),e=[];for(const i of this.elements){i.popup=t;i.container.ariaHasPopup="dialog";e.push(i.data.id);i.addHighlightArea()}this.container.setAttribute("aria-controls",e.map((t=>`${st}${t}`)).join(","));return this.container}}class PopupElement{#Ws=this.#qs.bind(this);#Xs=this.#Ys.bind(this);#Ks=this.#Qs.bind(this);#Js=this.#Zs.bind(this);#tn=null;#pt=null;#en=null;#in=null;#sn=null;#nn=null;#rn=null;#an=!1;#on=null;#C=null;#ln=null;#hn=null;#cn=null;#Hs=null;#dn=!1;constructor({container:t,color:e,elements:i,titleObj:s,modificationDate:n,contentsObj:r,richText:a,parent:o,rect:l,parentRect:h,open:c}){this.#pt=t;this.#cn=s;this.#en=r;this.#hn=a;this.#nn=o;this.#tn=e;this.#ln=l;this.#rn=h;this.#sn=i;this.#in=PDFDateString.toDateObject(n);this.trigger=i.flatMap((t=>t.getElementsToTriggerPopup()));for(const t of this.trigger){t.addEventListener("click",this.#Js);t.addEventListener("mouseenter",this.#Ks);t.addEventListener("mouseleave",this.#Xs);t.classList.add("popupTriggerArea")}for(const t of i)t.container?.addEventListener("keydown",this.#Ws);this.#pt.hidden=!0;c&&this.#Zs()}render(){if(this.#on)return;const t=this.#on=document.createElement("div");t.className="popup";if(this.#tn){const e=t.style.outlineColor=Util.makeHexColor(...this.#tn);if(CSS.supports("background-color","color-mix(in srgb, red 30%, white)"))t.style.backgroundColor=`color-mix(in srgb, ${e} 30%, white)`;else{const e=.7;t.style.backgroundColor=Util.makeHexColor(...this.#tn.map((t=>Math.floor(e*(255-t)+t))))}}const e=document.createElement("span");e.className="header";const i=document.createElement("h1");e.append(i);({dir:i.dir,str:i.textContent}=this.#cn);t.append(e);if(this.#in){const t=document.createElement("span");t.classList.add("popupDate");t.setAttribute("data-l10n-id","pdfjs-annotation-date-time-string");t.setAttribute("data-l10n-args",JSON.stringify({dateObj:this.#in.valueOf()}));e.append(t)}const s=this.#un;if(s){XfaLayer.render({xfaHtml:s,intent:"richText",div:t});t.lastChild.classList.add("richText","popupContent")}else{const e=this._formatContents(this.#en);t.append(e)}this.#pt.append(t)}get#un(){const t=this.#hn,e=this.#en;return!t?.str||e?.str&&e.str!==t.str?null:this.#hn.html||null}get#pn(){return this.#un?.attributes?.style?.fontSize||0}get#gn(){return this.#un?.attributes?.style?.color||null}#fn(t){const e=[],i={str:t,html:{name:"div",attributes:{dir:"auto"},children:[{name:"p",children:e}]}},s={style:{color:this.#gn,fontSize:this.#pn?`calc(${this.#pn}px * var(--scale-factor))`:""}};for(const i of t.split("\n"))e.push({name:"span",value:i,attributes:s});return i}_formatContents({str:t,dir:e}){const i=document.createElement("p");i.classList.add("popupContent");i.dir=e;const s=t.split(/(?:\r\n?|\n)/);for(let t=0,e=s.length;t<e;++t){const n=s[t];i.append(document.createTextNode(n));t<e-1&&i.append(document.createElement("br"))}return i}#qs(t){t.altKey||t.shiftKey||t.ctrlKey||t.metaKey||("Enter"===t.key||"Escape"===t.key&&this.#an)&&this.#Zs()}updateEdited({rect:t,popupContent:e}){this.#Hs||={contentsObj:this.#en,richText:this.#hn};t&&(this.#C=null);if(e){this.#hn=this.#fn(e);this.#en=null}this.#on?.remove();this.#on=null}resetEdited(){if(this.#Hs){({contentsObj:this.#en,richText:this.#hn}=this.#Hs);this.#Hs=null;this.#on?.remove();this.#on=null;this.#C=null}}#mn(){if(null!==this.#C)return;const{page:{view:t},viewport:{rawDims:{pageWidth:e,pageHeight:i,pageX:s,pageY:n}}}=this.#nn;let r=!!this.#rn,a=r?this.#rn:this.#ln;for(const t of this.#sn)if(!a||null!==Util.intersect(t.data.rect,a)){a=t.data.rect;r=!0;break}const o=Util.normalizeRect([a[0],t[3]-a[1]+t[1],a[2],t[3]-a[3]+t[1]]),l=r?a[2]-a[0]+5:0,h=o[0]+l,c=o[1];this.#C=[100*(h-s)/e,100*(c-n)/i];const{style:d}=this.#pt;d.left=`${this.#C[0]}%`;d.top=`${this.#C[1]}%`}#Zs(){this.#an=!this.#an;if(this.#an){this.#Qs();this.#pt.addEventListener("click",this.#Js);this.#pt.addEventListener("keydown",this.#Ws)}else{this.#Ys();this.#pt.removeEventListener("click",this.#Js);this.#pt.removeEventListener("keydown",this.#Ws)}}#Qs(){this.#on||this.render();if(this.isVisible)this.#an&&this.#pt.classList.add("focused");else{this.#mn();this.#pt.hidden=!1;this.#pt.style.zIndex=parseInt(this.#pt.style.zIndex)+1e3}}#Ys(){this.#pt.classList.remove("focused");if(!this.#an&&this.isVisible){this.#pt.hidden=!0;this.#pt.style.zIndex=parseInt(this.#pt.style.zIndex)-1e3}}forceHide(){this.#dn=this.isVisible;this.#dn&&(this.#pt.hidden=!0)}maybeShow(){if(this.#dn){this.#on||this.#Qs();this.#dn=!1;this.#pt.hidden=!1}}get isVisible(){return!1===this.#pt.hidden}}class FreeTextAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0});this.textContent=t.data.textContent;this.textPosition=t.data.textPosition;this.annotationEditorType=f.FREETEXT}render(){this.container.classList.add("freeTextAnnotation");if(this.textContent){const t=document.createElement("div");t.classList.add("annotationTextContent");t.setAttribute("role","comment");for(const e of this.textContent){const i=document.createElement("span");i.textContent=e;t.append(i)}this.container.append(t)}!this.data.popupRef&&this.hasPopupData&&this._createPopup();this._editOnDoubleClick();return this.container}}class LineAnnotationElement extends AnnotationElement{#bn=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){this.container.classList.add("lineAnnotation");const t=this.data,{width:e,height:i}=getRectDims(t.rect),s=this.svgFactory.create(e,i,!0),n=this.#bn=this.svgFactory.createElement("svg:line");n.setAttribute("x1",t.rect[2]-t.lineCoordinates[0]);n.setAttribute("y1",t.rect[3]-t.lineCoordinates[1]);n.setAttribute("x2",t.rect[2]-t.lineCoordinates[2]);n.setAttribute("y2",t.rect[3]-t.lineCoordinates[3]);n.setAttribute("stroke-width",t.borderStyle.width||1);n.setAttribute("stroke","transparent");n.setAttribute("fill","transparent");s.append(n);this.container.append(s);!t.popupRef&&this.hasPopupData&&this._createPopup();return this.container}getElementsToTriggerPopup(){return this.#bn}addHighlightArea(){this.container.classList.add("highlightArea")}}class SquareAnnotationElement extends AnnotationElement{#vn=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){this.container.classList.add("squareAnnotation");const t=this.data,{width:e,height:i}=getRectDims(t.rect),s=this.svgFactory.create(e,i,!0),n=t.borderStyle.width,r=this.#vn=this.svgFactory.createElement("svg:rect");r.setAttribute("x",n/2);r.setAttribute("y",n/2);r.setAttribute("width",e-n);r.setAttribute("height",i-n);r.setAttribute("stroke-width",n||1);r.setAttribute("stroke","transparent");r.setAttribute("fill","transparent");s.append(r);this.container.append(s);!t.popupRef&&this.hasPopupData&&this._createPopup();return this.container}getElementsToTriggerPopup(){return this.#vn}addHighlightArea(){this.container.classList.add("highlightArea")}}class CircleAnnotationElement extends AnnotationElement{#yn=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){this.container.classList.add("circleAnnotation");const t=this.data,{width:e,height:i}=getRectDims(t.rect),s=this.svgFactory.create(e,i,!0),n=t.borderStyle.width,r=this.#yn=this.svgFactory.createElement("svg:ellipse");r.setAttribute("cx",e/2);r.setAttribute("cy",i/2);r.setAttribute("rx",e/2-n/2);r.setAttribute("ry",i/2-n/2);r.setAttribute("stroke-width",n||1);r.setAttribute("stroke","transparent");r.setAttribute("fill","transparent");s.append(r);this.container.append(s);!t.popupRef&&this.hasPopupData&&this._createPopup();return this.container}getElementsToTriggerPopup(){return this.#yn}addHighlightArea(){this.container.classList.add("highlightArea")}}class PolylineAnnotationElement extends AnnotationElement{#wn=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0});this.containerClassName="polylineAnnotation";this.svgElementName="svg:polyline"}render(){this.container.classList.add(this.containerClassName);const{data:{rect:t,vertices:e,borderStyle:i,popupRef:s}}=this;if(!e)return this.container;const{width:n,height:r}=getRectDims(t),a=this.svgFactory.create(n,r,!0);let o=[];for(let i=0,s=e.length;i<s;i+=2){const s=e[i]-t[0],n=t[3]-e[i+1];o.push(`${s},${n}`)}o=o.join(" ");const l=this.#wn=this.svgFactory.createElement(this.svgElementName);l.setAttribute("points",o);l.setAttribute("stroke-width",i.width||1);l.setAttribute("stroke","transparent");l.setAttribute("fill","transparent");a.append(l);this.container.append(a);!s&&this.hasPopupData&&this._createPopup();return this.container}getElementsToTriggerPopup(){return this.#wn}addHighlightArea(){this.container.classList.add("highlightArea")}}class PolygonAnnotationElement extends PolylineAnnotationElement{constructor(t){super(t);this.containerClassName="polygonAnnotation";this.svgElementName="svg:polygon"}}class CaretAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){this.container.classList.add("caretAnnotation");!this.data.popupRef&&this.hasPopupData&&this._createPopup();return this.container}}class InkAnnotationElement extends AnnotationElement{#An=null;#xn=[];constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0});this.containerClassName="inkAnnotation";this.svgElementName="svg:polyline";this.annotationEditorType="InkHighlight"===this.data.it?f.HIGHLIGHT:f.INK}#_n(t,e){switch(t){case 90:return{transform:`rotate(90) translate(${-e[0]},${e[1]}) scale(1,-1)`,width:e[3]-e[1],height:e[2]-e[0]};case 180:return{transform:`rotate(180) translate(${-e[2]},${e[1]}) scale(1,-1)`,width:e[2]-e[0],height:e[3]-e[1]};case 270:return{transform:`rotate(270) translate(${-e[2]},${e[3]}) scale(1,-1)`,width:e[3]-e[1],height:e[2]-e[0]};default:return{transform:`translate(${-e[0]},${e[3]}) scale(1,-1)`,width:e[2]-e[0],height:e[3]-e[1]}}}render(){this.container.classList.add(this.containerClassName);const{data:{rect:t,rotation:e,inkLists:i,borderStyle:s,popupRef:n}}=this,{transform:r,width:a,height:o}=this.#_n(e,t),l=this.svgFactory.create(a,o,!0),h=this.#An=this.svgFactory.createElement("svg:g");l.append(h);h.setAttribute("stroke-width",s.width||1);h.setAttribute("stroke-linecap","round");h.setAttribute("stroke-linejoin","round");h.setAttribute("stroke-miterlimit",10);h.setAttribute("stroke","transparent");h.setAttribute("fill","transparent");h.setAttribute("transform",r);for(let t=0,e=i.length;t<e;t++){const e=this.svgFactory.createElement(this.svgElementName);this.#xn.push(e);e.setAttribute("points",i[t].join(","));h.append(e)}!n&&this.hasPopupData&&this._createPopup();this.container.append(l);this._editOnDoubleClick();return this.container}updateEdited(t){super.updateEdited(t);const{thickness:e,points:i,rect:s}=t,n=this.#An;e>=0&&n.setAttribute("stroke-width",e||1);if(i)for(let t=0,e=this.#xn.length;t<e;t++)this.#xn[t].setAttribute("points",i[t].join(","));if(s){const{transform:t,width:e,height:i}=this.#_n(this.data.rotation,s);n.parentElement.setAttribute("viewBox",`0 0 ${e} ${i}`);n.setAttribute("transform",t)}}getElementsToTriggerPopup(){return this.#xn}addHighlightArea(){this.container.classList.add("highlightArea")}}class HighlightAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0});this.annotationEditorType=f.HIGHLIGHT}render(){!this.data.popupRef&&this.hasPopupData&&this._createPopup();this.container.classList.add("highlightAnnotation");this._editOnDoubleClick();return this.container}}class UnderlineAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){!this.data.popupRef&&this.hasPopupData&&this._createPopup();this.container.classList.add("underlineAnnotation");return this.container}}class SquigglyAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){!this.data.popupRef&&this.hasPopupData&&this._createPopup();this.container.classList.add("squigglyAnnotation");return this.container}}class StrikeOutAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){!this.data.popupRef&&this.hasPopupData&&this._createPopup();this.container.classList.add("strikeoutAnnotation");return this.container}}class StampAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0});this.annotationEditorType=f.STAMP}render(){this.container.classList.add("stampAnnotation");this.container.setAttribute("role","img");!this.data.popupRef&&this.hasPopupData&&this._createPopup();this._editOnDoubleClick();return this.container}}class FileAttachmentAnnotationElement extends AnnotationElement{#En=null;constructor(t){super(t,{isRenderable:!0});const{file:e}=this.data;this.filename=e.filename;this.content=e.content;this.linkService.eventBus?.dispatch("fileattachmentannotation",{source:this,...e})}render(){this.container.classList.add("fileAttachmentAnnotation");const{container:t,data:e}=this;let i;if(e.hasAppearance||0===e.fillAlpha)i=document.createElement("div");else{i=document.createElement("img");i.src=`${this.imageResourcesPath}annotation-${/paperclip/i.test(e.name)?"paperclip":"pushpin"}.svg`;e.fillAlpha&&e.fillAlpha<1&&(i.style=`filter: opacity(${Math.round(100*e.fillAlpha)}%);`)}i.addEventListener("dblclick",this.#Sn.bind(this));this.#En=i;const{isMac:s}=util_FeatureTest.platform;t.addEventListener("keydown",(t=>{"Enter"===t.key&&(s?t.metaKey:t.ctrlKey)&&this.#Sn()}));!e.popupRef&&this.hasPopupData?this._createPopup():i.classList.add("popupTriggerArea");t.append(i);return t}getElementsToTriggerPopup(){return this.#En}addHighlightArea(){this.container.classList.add("highlightArea")}#Sn(){this.downloadManager?.openOrDownloadData(this.content,this.filename)}}class AnnotationLayer{#Cn=null;#Tn=null;#Mn=new Map;#Pn=null;constructor({div:t,accessibilityManager:e,annotationCanvasMap:i,annotationEditorUIManager:s,page:n,viewport:r,structTreeLayer:a}){this.div=t;this.#Cn=e;this.#Tn=i;this.#Pn=a||null;this.page=n;this.viewport=r;this.zIndex=0;this._annotationEditorUIManager=s}hasEditableAnnotations(){return this.#Mn.size>0}async#Dn(t,e){const i=t.firstChild||t,s=i.id=`${st}${e}`,n=await(this.#Pn?.getAriaAttributes(s));if(n)for(const[t,e]of n)i.setAttribute(t,e);this.div.append(t);this.#Cn?.moveElementInDOM(this.div,t,i,!1)}async render(t){const{annotations:e}=t,i=this.div;setLayerDimensions(i,this.viewport);const s=new Map,n={data:null,layer:i,linkService:t.linkService,downloadManager:t.downloadManager,imageResourcesPath:t.imageResourcesPath||"",renderForms:!1!==t.renderForms,svgFactory:new DOMSVGFactory,annotationStorage:t.annotationStorage||new AnnotationStorage,enableScripting:!0===t.enableScripting,hasJSActions:t.hasJSActions,fieldObjects:t.fieldObjects,parent:this,elements:null};for(const t of e){if(t.noHTML)continue;const e=t.annotationType===z;if(e){const e=s.get(t.id);if(!e)continue;n.elements=e}else{const{width:e,height:i}=getRectDims(t.rect);if(e<=0||i<=0)continue}n.data=t;const i=AnnotationElementFactory.create(n);if(!i.isRenderable)continue;if(!e&&t.popupRef){const e=s.get(t.popupRef);e?e.push(i):s.set(t.popupRef,[i])}const r=i.render();t.hidden&&(r.style.visibility="hidden");await this.#Dn(r,t.id);if(i._isEditable){this.#Mn.set(i.data.id,i);this._annotationEditorUIManager?.renderAnnotationElement(i)}}this.#kn()}update({viewport:t}){const e=this.div;this.viewport=t;setLayerDimensions(e,{rotation:t.rotation});this.#kn();e.hidden=!1}#kn(){if(!this.#Tn)return;const t=this.div;for(const[e,i]of this.#Tn){const s=t.querySelector(`[data-annotation-id="${e}"]`);if(!s)continue;i.className="annotationContent";const{firstChild:n}=s;n?"CANVAS"===n.nodeName?n.replaceWith(i):n.classList.contains("annotationContent")?n.after(i):n.before(i):s.append(i)}this.#Tn.clear()}getEditableAnnotations(){return Array.from(this.#Mn.values())}getEditableAnnotation(t){return this.#Mn.get(t)}}const jt=/\r\n?|\n/g;class FreeTextEditor extends AnnotationEditor{#tn;#Rn="";#In=`${this.id}-editor`;#Fn=null;#pn;static _freeTextDefaultContent="";static _internalPadding=0;static _defaultColor=null;static _defaultFontSize=10;static get _keyboardManager(){const t=FreeTextEditor.prototype,arrowChecker=t=>t.isEmpty(),e=AnnotationEditorUIManager.TRANSLATE_SMALL,i=AnnotationEditorUIManager.TRANSLATE_BIG;return shadow(this,"_keyboardManager",new KeyboardManager([[["ctrl+s","mac+meta+s","ctrl+p","mac+meta+p"],t.commitOrRemove,{bubbles:!0}],[["ctrl+Enter","mac+meta+Enter","Escape","mac+Escape"],t.commitOrRemove],[["ArrowLeft","mac+ArrowLeft"],t._translateEmpty,{args:[-e,0],checker:arrowChecker}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],t._translateEmpty,{args:[-i,0],checker:arrowChecker}],[["ArrowRight","mac+ArrowRight"],t._translateEmpty,{args:[e,0],checker:arrowChecker}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],t._translateEmpty,{args:[i,0],checker:arrowChecker}],[["ArrowUp","mac+ArrowUp"],t._translateEmpty,{args:[0,-e],checker:arrowChecker}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],t._translateEmpty,{args:[0,-i],checker:arrowChecker}],[["ArrowDown","mac+ArrowDown"],t._translateEmpty,{args:[0,e],checker:arrowChecker}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],t._translateEmpty,{args:[0,i],checker:arrowChecker}]]))}static _type="freetext";static _editorType=f.FREETEXT;constructor(t){super({...t,name:"freeTextEditor"});this.#tn=t.color||FreeTextEditor._defaultColor||AnnotationEditor._defaultLineColor;this.#pn=t.fontSize||FreeTextEditor._defaultFontSize}static initialize(t,e){AnnotationEditor.initialize(t,e);const i=getComputedStyle(document.documentElement);this._internalPadding=parseFloat(i.getPropertyValue("--freetext-padding"))}static updateDefaultParams(t,e){switch(t){case m.FREETEXT_SIZE:FreeTextEditor._defaultFontSize=e;break;case m.FREETEXT_COLOR:FreeTextEditor._defaultColor=e}}updateParams(t,e){switch(t){case m.FREETEXT_SIZE:this.#Ln(e);break;case m.FREETEXT_COLOR:this.#On(e)}}static get defaultPropertiesToUpdate(){return[[m.FREETEXT_SIZE,FreeTextEditor._defaultFontSize],[m.FREETEXT_COLOR,FreeTextEditor._defaultColor||AnnotationEditor._defaultLineColor]]}get propertiesToUpdate(){return[[m.FREETEXT_SIZE,this.#pn],[m.FREETEXT_COLOR,this.#tn]]}#Ln(t){const setFontsize=t=>{this.editorDiv.style.fontSize=`calc(${t}px * var(--scale-factor))`;this.translate(0,-(t-this.#pn)*this.parentScale);this.#pn=t;this.#Nn()},e=this.#pn;this.addCommands({cmd:setFontsize.bind(this,t),undo:setFontsize.bind(this,e),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:m.FREETEXT_SIZE,overwriteIfSameType:!0,keepUndo:!0})}#On(t){const setColor=t=>{this.#tn=this.editorDiv.style.color=t},e=this.#tn;this.addCommands({cmd:setColor.bind(this,t),undo:setColor.bind(this,e),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:m.FREETEXT_COLOR,overwriteIfSameType:!0,keepUndo:!0})}_translateEmpty(t,e){this._uiManager.translateSelectedEditors(t,e,!0)}getInitialTranslation(){const t=this.parentScale;return[-FreeTextEditor._internalPadding*t,-(FreeTextEditor._internalPadding+this.#pn)*t]}rebuild(){if(this.parent){super.rebuild();null!==this.div&&(this.isAttachedToDOM||this.parent.add(this))}}enableEditMode(){if(this.isInEditMode())return;this.parent.setEditingState(!1);this.parent.updateToolbar(f.FREETEXT);super.enableEditMode();this.overlayDiv.classList.remove("enabled");this.editorDiv.contentEditable=!0;this._isDraggable=!1;this.div.removeAttribute("aria-activedescendant");this.#Fn=new AbortController;const t=this._uiManager.combinedSignal(this.#Fn);this.editorDiv.addEventListener("keydown",this.editorDivKeydown.bind(this),{signal:t});this.editorDiv.addEventListener("focus",this.editorDivFocus.bind(this),{signal:t});this.editorDiv.addEventListener("blur",this.editorDivBlur.bind(this),{signal:t});this.editorDiv.addEventListener("input",this.editorDivInput.bind(this),{signal:t});this.editorDiv.addEventListener("paste",this.editorDivPaste.bind(this),{signal:t})}disableEditMode(){if(this.isInEditMode()){this.parent.setEditingState(!0);super.disableEditMode();this.overlayDiv.classList.add("enabled");this.editorDiv.contentEditable=!1;this.div.setAttribute("aria-activedescendant",this.#In);this._isDraggable=!0;this.#Fn?.abort();this.#Fn=null;this.div.focus({preventScroll:!0});this.isEditing=!1;this.parent.div.classList.add("freetextEditing")}}focusin(t){if(this._focusEventsAllowed){super.focusin(t);t.target!==this.editorDiv&&this.editorDiv.focus()}}onceAdded(t){if(!this.width){this.enableEditMode();t&&this.editorDiv.focus();this._initialOptions?.isCentered&&this.center();this._initialOptions=null}}isEmpty(){return!this.editorDiv||""===this.editorDiv.innerText.trim()}remove(){this.isEditing=!1;if(this.parent){this.parent.setEditingState(!0);this.parent.div.classList.add("freetextEditing")}super.remove()}#Bn(){const t=[];this.editorDiv.normalize();let e=null;for(const i of this.editorDiv.childNodes)if(e?.nodeType!==Node.TEXT_NODE||"BR"!==i.nodeName){t.push(FreeTextEditor.#Hn(i));e=i}return t.join("\n")}#Nn(){const[t,e]=this.parentDimensions;let i;if(this.isAttachedToDOM)i=this.div.getBoundingClientRect();else{const{currentLayer:t,div:e}=this,s=e.style.display,n=e.classList.contains("hidden");e.classList.remove("hidden");e.style.display="hidden";t.div.append(this.div);i=e.getBoundingClientRect();e.remove();e.style.display=s;e.classList.toggle("hidden",n)}if(this.rotation%180==this.parentRotation%180){this.width=i.width/t;this.height=i.height/e}else{this.width=i.height/t;this.height=i.width/e}this.fixAndSetPosition()}commit(){if(!this.isInEditMode())return;super.commit();this.disableEditMode();const t=this.#Rn,e=this.#Rn=this.#Bn().trimEnd();if(t===e)return;const setText=t=>{this.#Rn=t;if(t){this.#Un();this._uiManager.rebuild(this);this.#Nn()}else this.remove()};this.addCommands({cmd:()=>{setText(e)},undo:()=>{setText(t)},mustExec:!1});this.#Nn()}shouldGetKeyboardEvents(){return this.isInEditMode()}enterInEditMode(){this.enableEditMode();this.editorDiv.focus()}dblclick(t){this.enterInEditMode()}keydown(t){if(t.target===this.div&&"Enter"===t.key){this.enterInEditMode();t.preventDefault()}}editorDivKeydown(t){FreeTextEditor._keyboardManager.exec(this,t)}editorDivFocus(t){this.isEditing=!0}editorDivBlur(t){this.isEditing=!1}editorDivInput(t){this.parent.div.classList.toggle("freetextEditing",this.isEmpty())}disableEditing(){this.editorDiv.setAttribute("role","comment");this.editorDiv.removeAttribute("aria-multiline")}enableEditing(){this.editorDiv.setAttribute("role","textbox");this.editorDiv.setAttribute("aria-multiline",!0)}render(){if(this.div)return this.div;let t,e;if(this.width){t=this.x;e=this.y}super.render();this.editorDiv=document.createElement("div");this.editorDiv.className="internal";this.editorDiv.setAttribute("id",this.#In);this.editorDiv.setAttribute("data-l10n-id","pdfjs-free-text2");this.editorDiv.setAttribute("data-l10n-attrs","default-content");this.enableEditing();this.editorDiv.contentEditable=!0;const{style:i}=this.editorDiv;i.fontSize=`calc(${this.#pn}px * var(--scale-factor))`;i.color=this.#tn;this.div.append(this.editorDiv);this.overlayDiv=document.createElement("div");this.overlayDiv.classList.add("overlay","enabled");this.div.append(this.overlayDiv);bindEvents(this,this.div,["dblclick","keydown"]);if(this.width){const[i,s]=this.parentDimensions;if(this.annotationElementId){const{position:n}=this._initialData;let[r,a]=this.getInitialTranslation();[r,a]=this.pageTranslationToScreen(r,a);const[o,l]=this.pageDimensions,[h,c]=this.pageTranslation;let d,u;switch(this.rotation){case 0:d=t+(n[0]-h)/o;u=e+this.height-(n[1]-c)/l;break;case 90:d=t+(n[0]-h)/o;u=e-(n[1]-c)/l;[r,a]=[a,-r];break;case 180:d=t-this.width+(n[0]-h)/o;u=e-(n[1]-c)/l;[r,a]=[-r,-a];break;case 270:d=t+(n[0]-h-this.height*l)/o;u=e+(n[1]-c-this.width*o)/l;[r,a]=[-a,r]}this.setAt(d*i,u*s,r,a)}else this.setAt(t*i,e*s,this.width*i,this.height*s);this.#Un();this._isDraggable=!0;this.editorDiv.contentEditable=!1}else{this._isDraggable=!1;this.editorDiv.contentEditable=!0}return this.div}static#Hn(t){return(t.nodeType===Node.TEXT_NODE?t.nodeValue:t.innerText).replaceAll(jt,"")}editorDivPaste(t){const e=t.clipboardData||window.clipboardData,{types:i}=e;if(1===i.length&&"text/plain"===i[0])return;t.preventDefault();const s=FreeTextEditor.#zn(e.getData("text")||"").replaceAll(jt,"\n");if(!s)return;const n=window.getSelection();if(!n.rangeCount)return;this.editorDiv.normalize();n.deleteFromDocument();const r=n.getRangeAt(0);if(!s.includes("\n")){r.insertNode(document.createTextNode(s));this.editorDiv.normalize();n.collapseToStart();return}const{startContainer:a,startOffset:o}=r,l=[],h=[];if(a.nodeType===Node.TEXT_NODE){const t=a.parentElement;h.push(a.nodeValue.slice(o).replaceAll(jt,""));if(t!==this.editorDiv){let e=l;for(const i of this.editorDiv.childNodes)i!==t?e.push(FreeTextEditor.#Hn(i)):e=h}l.push(a.nodeValue.slice(0,o).replaceAll(jt,""))}else if(a===this.editorDiv){let t=l,e=0;for(const i of this.editorDiv.childNodes){e++===o&&(t=h);t.push(FreeTextEditor.#Hn(i))}}this.#Rn=`${l.join("\n")}${s}${h.join("\n")}`;this.#Un();const c=new Range;let d=l.reduce(((t,e)=>t+e.length),0);for(const{firstChild:t}of this.editorDiv.childNodes)if(t.nodeType===Node.TEXT_NODE){const e=t.nodeValue.length;if(d<=e){c.setStart(t,d);c.setEnd(t,d);break}d-=e}n.removeAllRanges();n.addRange(c)}#Un(){this.editorDiv.replaceChildren();if(this.#Rn)for(const t of this.#Rn.split("\n")){const e=document.createElement("div");e.append(t?document.createTextNode(t):document.createElement("br"));this.editorDiv.append(e)}}#jn(){return this.#Rn.replaceAll(" "," ")}static#zn(t){return t.replaceAll(" "," ")}get contentDiv(){return this.editorDiv}static async deserialize(t,e,i){let s=null;if(t instanceof FreeTextAnnotationElement){const{data:{defaultAppearanceData:{fontSize:e,fontColor:i},rect:n,rotation:r,id:a,popupRef:o},textContent:l,textPosition:h,parent:{page:{pageNumber:c}}}=t;if(!l||0===l.length)return null;s=t={annotationType:f.FREETEXT,color:Array.from(i),fontSize:e,value:l.join("\n"),position:h,pageIndex:c-1,rect:n.slice(0),rotation:r,id:a,deleted:!1,popupRef:o}}const n=await super.deserialize(t,e,i);n.#pn=t.fontSize;n.#tn=Util.makeHexColor(...t.color);n.#Rn=FreeTextEditor.#zn(t.value);n.annotationElementId=t.id||null;n._initialData=s;return n}serialize(t=!1){if(this.isEmpty())return null;if(this.deleted)return this.serializeDeleted();const e=FreeTextEditor._internalPadding*this.parentScale,i=this.getRect(e,e),s=AnnotationEditor._colorManager.convert(this.isAttachedToDOM?getComputedStyle(this.editorDiv).color:this.#tn),n={annotationType:f.FREETEXT,color:s,fontSize:this.#pn,value:this.#jn(),pageIndex:this.pageIndex,rect:i,rotation:this.rotation,structTreeParentId:this._structTreeParentId};if(t)return n;if(this.annotationElementId&&!this.#Gn(n))return null;n.id=this.annotationElementId;return n}#Gn(t){const{value:e,fontSize:i,color:s,pageIndex:n}=this._initialData;return this._hasBeenMoved||t.value!==e||t.fontSize!==i||t.color.some(((t,e)=>t!==s[e]))||t.pageIndex!==n}renderAnnotationElement(t){const e=super.renderAnnotationElement(t);if(this.deleted)return e;const{style:i}=e;i.fontSize=`calc(${this.#pn}px * var(--scale-factor))`;i.color=this.#tn;e.replaceChildren();for(const t of this.#Rn.split("\n")){const i=document.createElement("div");i.append(t?document.createTextNode(t):document.createElement("br"));e.append(i)}const s=FreeTextEditor._internalPadding*this.parentScale;t.updateEdited({rect:this.getRect(s,s),popupContent:this.#Rn});return e}resetAnnotationElement(t){super.resetAnnotationElement(t);t.resetEdited()}}class Outline{static PRECISION=1e-4;toSVGPath(){unreachable("Abstract method `toSVGPath` must be implemented.")}get box(){unreachable("Abstract getter `box` must be implemented.")}serialize(t,e){unreachable("Abstract method `serialize` must be implemented.")}static _rescale(t,e,i,s,n,r){r||=new Float32Array(t.length);for(let a=0,o=t.length;a<o;a+=2){r[a]=e+t[a]*s;r[a+1]=i+t[a+1]*n}return r}static _rescaleAndSwap(t,e,i,s,n,r){r||=new Float32Array(t.length);for(let a=0,o=t.length;a<o;a+=2){r[a]=e+t[a+1]*s;r[a+1]=i+t[a]*n}return r}static _translate(t,e,i,s){s||=new Float32Array(t.length);for(let n=0,r=t.length;n<r;n+=2){s[n]=e+t[n];s[n+1]=i+t[n+1]}return s}static svgRound(t){return Math.round(1e4*t)}static _normalizePoint(t,e,i,s,n){switch(n){case 90:return[1-e/i,t/s];case 180:return[1-t/i,1-e/s];case 270:return[e/i,1-t/s];default:return[t/i,e/s]}}static _normalizePagePoint(t,e,i){switch(i){case 90:return[1-e,t];case 180:return[1-t,1-e];case 270:return[e,1-t];default:return[t,e]}}static createBezierPoints(t,e,i,s,n,r){return[(t+5*i)/6,(e+5*s)/6,(5*i+n)/6,(5*s+r)/6,(i+n)/2,(s+r)/2]}}class FreeDrawOutliner{#Vn;#$n=[];#Wn;#qn;#Xn=[];#Yn=new Float32Array(18);#Kn;#Qn;#Jn;#Zn;#tr;#er;#ir=[];static#sr=8;static#nr=2;static#rr=FreeDrawOutliner.#sr+FreeDrawOutliner.#nr;constructor({x:t,y:e},i,s,n,r,a=0){this.#Vn=i;this.#er=n*s;this.#qn=r;this.#Yn.set([NaN,NaN,NaN,NaN,t,e],6);this.#Wn=a;this.#Zn=FreeDrawOutliner.#sr*s;this.#Jn=FreeDrawOutliner.#rr*s;this.#tr=s;this.#ir.push(t,e)}isEmpty(){return isNaN(this.#Yn[8])}#ar(){const t=this.#Yn.subarray(4,6),e=this.#Yn.subarray(16,18),[i,s,n,r]=this.#Vn;return[(this.#Kn+(t[0]-e[0])/2-i)/n,(this.#Qn+(t[1]-e[1])/2-s)/r,(this.#Kn+(e[0]-t[0])/2-i)/n,(this.#Qn+(e[1]-t[1])/2-s)/r]}add({x:t,y:e}){this.#Kn=t;this.#Qn=e;const[i,s,n,r]=this.#Vn;let[a,o,l,h]=this.#Yn.subarray(8,12);const c=t-l,d=e-h,u=Math.hypot(c,d);if(u<this.#Jn)return!1;const p=u-this.#Zn,g=p/u,f=g*c,m=g*d;let b=a,v=o;a=l;o=h;l+=f;h+=m;this.#ir?.push(t,e);const y=f/p,w=-m/p*this.#er,A=y*this.#er;this.#Yn.set(this.#Yn.subarray(2,8),0);this.#Yn.set([l+w,h+A],4);this.#Yn.set(this.#Yn.subarray(14,18),12);this.#Yn.set([l-w,h-A],16);if(isNaN(this.#Yn[6])){if(0===this.#Xn.length){this.#Yn.set([a+w,o+A],2);this.#Xn.push(NaN,NaN,NaN,NaN,(a+w-i)/n,(o+A-s)/r);this.#Yn.set([a-w,o-A],14);this.#$n.push(NaN,NaN,NaN,NaN,(a-w-i)/n,(o-A-s)/r)}this.#Yn.set([b,v,a,o,l,h],6);return!this.isEmpty()}this.#Yn.set([b,v,a,o,l,h],6);if(Math.abs(Math.atan2(v-o,b-a)-Math.atan2(m,f))<Math.PI/2){[a,o,l,h]=this.#Yn.subarray(2,6);this.#Xn.push(NaN,NaN,NaN,NaN,((a+l)/2-i)/n,((o+h)/2-s)/r);[a,o,b,v]=this.#Yn.subarray(14,18);this.#$n.push(NaN,NaN,NaN,NaN,((b+a)/2-i)/n,((v+o)/2-s)/r);return!0}[b,v,a,o,l,h]=this.#Yn.subarray(0,6);this.#Xn.push(((b+5*a)/6-i)/n,((v+5*o)/6-s)/r,((5*a+l)/6-i)/n,((5*o+h)/6-s)/r,((a+l)/2-i)/n,((o+h)/2-s)/r);[l,h,a,o,b,v]=this.#Yn.subarray(12,18);this.#$n.push(((b+5*a)/6-i)/n,((v+5*o)/6-s)/r,((5*a+l)/6-i)/n,((5*o+h)/6-s)/r,((a+l)/2-i)/n,((o+h)/2-s)/r);return!0}toSVGPath(){if(this.isEmpty())return"";const t=this.#Xn,e=this.#$n;if(isNaN(this.#Yn[6])&&!this.isEmpty())return this.#or();const i=[];i.push(`M${t[4]} ${t[5]}`);for(let e=6;e<t.length;e+=6)isNaN(t[e])?i.push(`L${t[e+4]} ${t[e+5]}`):i.push(`C${t[e]} ${t[e+1]} ${t[e+2]} ${t[e+3]} ${t[e+4]} ${t[e+5]}`);this.#lr(i);for(let t=e.length-6;t>=6;t-=6)isNaN(e[t])?i.push(`L${e[t+4]} ${e[t+5]}`):i.push(`C${e[t]} ${e[t+1]} ${e[t+2]} ${e[t+3]} ${e[t+4]} ${e[t+5]}`);this.#hr(i);return i.join(" ")}#or(){const[t,e,i,s]=this.#Vn,[n,r,a,o]=this.#ar();return`M${(this.#Yn[2]-t)/i} ${(this.#Yn[3]-e)/s} L${(this.#Yn[4]-t)/i} ${(this.#Yn[5]-e)/s} L${n} ${r} L${a} ${o} L${(this.#Yn[16]-t)/i} ${(this.#Yn[17]-e)/s} L${(this.#Yn[14]-t)/i} ${(this.#Yn[15]-e)/s} Z`}#hr(t){const e=this.#$n;t.push(`L${e[4]} ${e[5]} Z`)}#lr(t){const[e,i,s,n]=this.#Vn,r=this.#Yn.subarray(4,6),a=this.#Yn.subarray(16,18),[o,l,h,c]=this.#ar();t.push(`L${(r[0]-e)/s} ${(r[1]-i)/n} L${o} ${l} L${h} ${c} L${(a[0]-e)/s} ${(a[1]-i)/n}`)}newFreeDrawOutline(t,e,i,s,n,r){return new FreeDrawOutline(t,e,i,s,n,r)}getOutlines(){const t=this.#Xn,e=this.#$n,i=this.#Yn,[s,n,r,a]=this.#Vn,o=new Float32Array((this.#ir?.length??0)+2);for(let t=0,e=o.length-2;t<e;t+=2){o[t]=(this.#ir[t]-s)/r;o[t+1]=(this.#ir[t+1]-n)/a}o[o.length-2]=(this.#Kn-s)/r;o[o.length-1]=(this.#Qn-n)/a;if(isNaN(i[6])&&!this.isEmpty())return this.#cr(o);const l=new Float32Array(this.#Xn.length+24+this.#$n.length);let h=t.length;for(let e=0;e<h;e+=2)if(isNaN(t[e]))l[e]=l[e+1]=NaN;else{l[e]=t[e];l[e+1]=t[e+1]}h=this.#dr(l,h);for(let t=e.length-6;t>=6;t-=6)for(let i=0;i<6;i+=2)if(isNaN(e[t+i])){l[h]=l[h+1]=NaN;h+=2}else{l[h]=e[t+i];l[h+1]=e[t+i+1];h+=2}this.#ur(l,h);return this.newFreeDrawOutline(l,o,this.#Vn,this.#tr,this.#Wn,this.#qn)}#cr(t){const e=this.#Yn,[i,s,n,r]=this.#Vn,[a,o,l,h]=this.#ar(),c=new Float32Array(36);c.set([NaN,NaN,NaN,NaN,(e[2]-i)/n,(e[3]-s)/r,NaN,NaN,NaN,NaN,(e[4]-i)/n,(e[5]-s)/r,NaN,NaN,NaN,NaN,a,o,NaN,NaN,NaN,NaN,l,h,NaN,NaN,NaN,NaN,(e[16]-i)/n,(e[17]-s)/r,NaN,NaN,NaN,NaN,(e[14]-i)/n,(e[15]-s)/r],0);return this.newFreeDrawOutline(c,t,this.#Vn,this.#tr,this.#Wn,this.#qn)}#ur(t,e){const i=this.#$n;t.set([NaN,NaN,NaN,NaN,i[4],i[5]],e);return e+6}#dr(t,e){const i=this.#Yn.subarray(4,6),s=this.#Yn.subarray(16,18),[n,r,a,o]=this.#Vn,[l,h,c,d]=this.#ar();t.set([NaN,NaN,NaN,NaN,(i[0]-n)/a,(i[1]-r)/o,NaN,NaN,NaN,NaN,l,h,NaN,NaN,NaN,NaN,c,d,NaN,NaN,NaN,NaN,(s[0]-n)/a,(s[1]-r)/o],e);return e+24}}class FreeDrawOutline extends Outline{#Vn;#pr=new Float32Array(4);#Wn;#qn;#ir;#tr;#gr;constructor(t,e,i,s,n,r){super();this.#gr=t;this.#ir=e;this.#Vn=i;this.#tr=s;this.#Wn=n;this.#qn=r;this.lastPoint=[NaN,NaN];this.#fr(r);const[a,o,l,h]=this.#pr;for(let e=0,i=t.length;e<i;e+=2){t[e]=(t[e]-a)/l;t[e+1]=(t[e+1]-o)/h}for(let t=0,i=e.length;t<i;t+=2){e[t]=(e[t]-a)/l;e[t+1]=(e[t+1]-o)/h}}toSVGPath(){const t=[`M${this.#gr[4]} ${this.#gr[5]}`];for(let e=6,i=this.#gr.length;e<i;e+=6)isNaN(this.#gr[e])?t.push(`L${this.#gr[e+4]} ${this.#gr[e+5]}`):t.push(`C${this.#gr[e]} ${this.#gr[e+1]} ${this.#gr[e+2]} ${this.#gr[e+3]} ${this.#gr[e+4]} ${this.#gr[e+5]}`);t.push("Z");return t.join(" ")}serialize([t,e,i,s],n){const r=i-t,a=s-e;let o,l;switch(n){case 0:o=Outline._rescale(this.#gr,t,s,r,-a);l=Outline._rescale(this.#ir,t,s,r,-a);break;case 90:o=Outline._rescaleAndSwap(this.#gr,t,e,r,a);l=Outline._rescaleAndSwap(this.#ir,t,e,r,a);break;case 180:o=Outline._rescale(this.#gr,i,e,-r,a);l=Outline._rescale(this.#ir,i,e,-r,a);break;case 270:o=Outline._rescaleAndSwap(this.#gr,i,s,-r,-a);l=Outline._rescaleAndSwap(this.#ir,i,s,-r,-a)}return{outline:Array.from(o),points:[Array.from(l)]}}#fr(t){const e=this.#gr;let i=e[4],s=e[5],n=i,r=s,a=i,o=s,l=i,h=s;const c=t?Math.max:Math.min;for(let t=6,d=e.length;t<d;t+=6){if(isNaN(e[t])){n=Math.min(n,e[t+4]);r=Math.min(r,e[t+5]);a=Math.max(a,e[t+4]);o=Math.max(o,e[t+5]);if(h<e[t+5]){l=e[t+4];h=e[t+5]}else h===e[t+5]&&(l=c(l,e[t+4]))}else{const d=Util.bezierBoundingBox(i,s,...e.slice(t,t+6));n=Math.min(n,d[0]);r=Math.min(r,d[1]);a=Math.max(a,d[2]);o=Math.max(o,d[3]);if(h<d[3]){l=d[2];h=d[3]}else h===d[3]&&(l=c(l,d[2]))}i=e[t+4];s=e[t+5]}const d=this.#pr;d[0]=n-this.#Wn;d[1]=r-this.#Wn;d[2]=a-n+2*this.#Wn;d[3]=o-r+2*this.#Wn;this.lastPoint=[l,h]}get box(){return this.#pr}newOutliner(t,e,i,s,n,r=0){return new FreeDrawOutliner(t,e,i,s,n,r)}getNewOutline(t,e){const[i,s,n,r]=this.#pr,[a,o,l,h]=this.#Vn,c=n*l,d=r*h,u=i*l+a,p=s*h+o,g=this.newOutliner({x:this.#ir[0]*c+u,y:this.#ir[1]*d+p},this.#Vn,this.#tr,t,this.#qn,e??this.#Wn);for(let t=2;t<this.#ir.length;t+=2)g.add({x:this.#ir[t]*c+u,y:this.#ir[t+1]*d+p});return g.getOutlines()}}class HighlightOutliner{#Vn;#mr;#br=[];#vr=[];constructor(t,e=0,i=0,s=!0){let n=1/0,r=-1/0,a=1/0,o=-1/0;const l=10**-4;for(const{x:i,y:s,width:h,height:c}of t){const t=Math.floor((i-e)/l)*l,d=Math.ceil((i+h+e)/l)*l,u=Math.floor((s-e)/l)*l,p=Math.ceil((s+c+e)/l)*l,g=[t,u,p,!0],f=[d,u,p,!1];this.#br.push(g,f);n=Math.min(n,t);r=Math.max(r,d);a=Math.min(a,u);o=Math.max(o,p)}const h=r-n+2*i,c=o-a+2*i,d=n-i,u=a-i,p=this.#br.at(s?-1:-2),g=[p[0],p[2]];for(const t of this.#br){const[e,i,s]=t;t[0]=(e-d)/h;t[1]=(i-u)/c;t[2]=(s-u)/c}this.#Vn=new Float32Array([d,u,h,c]);this.#mr=g}getOutlines(){this.#br.sort(((t,e)=>t[0]-e[0]||t[1]-e[1]||t[2]-e[2]));const t=[];for(const e of this.#br)if(e[3]){t.push(...this.#yr(e));this.#wr(e)}else{this.#Ar(e);t.push(...this.#yr(e))}return this.#xr(t)}#xr(t){const e=[],i=new Set;for(const i of t){const[t,s,n]=i;e.push([t,s,i],[t,n,i])}e.sort(((t,e)=>t[1]-e[1]||t[0]-e[0]));for(let t=0,s=e.length;t<s;t+=2){const s=e[t][2],n=e[t+1][2];s.push(n);n.push(s);i.add(s);i.add(n)}const s=[];let n;for(;i.size>0;){const t=i.values().next().value;let[e,r,a,o,l]=t;i.delete(t);let h=e,c=r;n=[e,a];s.push(n);for(;;){let t;if(i.has(o))t=o;else{if(!i.has(l))break;t=l}i.delete(t);[e,r,a,o,l]=t;if(h!==e){n.push(h,c,e,c===r?r:a);h=e}c=c===r?a:r}n.push(h,c)}return new HighlightOutline(s,this.#Vn,this.#mr)}#_r(t){const e=this.#vr;let i=0,s=e.length-1;for(;i<=s;){const n=i+s>>1,r=e[n][0];if(r===t)return n;r<t?i=n+1:s=n-1}return s+1}#wr([,t,e]){const i=this.#_r(t);this.#vr.splice(i,0,[t,e])}#Ar([,t,e]){const i=this.#_r(t);for(let s=i;s<this.#vr.length;s++){const[i,n]=this.#vr[s];if(i!==t)break;if(i===t&&n===e){this.#vr.splice(s,1);return}}for(let s=i-1;s>=0;s--){const[i,n]=this.#vr[s];if(i!==t)break;if(i===t&&n===e){this.#vr.splice(s,1);return}}}#yr(t){const[e,i,s]=t,n=[[e,i,s]],r=this.#_r(s);for(let t=0;t<r;t++){const[i,s]=this.#vr[t];for(let t=0,r=n.length;t<r;t++){const[,a,o]=n[t];if(!(s<=a||o<=i))if(a>=i)if(o>s)n[t][1]=s;else{if(1===r)return[];n.splice(t,1);t--;r--}else{n[t][2]=i;o>s&&n.push([e,s,o])}}}return n}}class HighlightOutline extends Outline{#Vn;#Er;constructor(t,e,i){super();this.#Er=t;this.#Vn=e;this.lastPoint=i}toSVGPath(){const t=[];for(const e of this.#Er){let[i,s]=e;t.push(`M${i} ${s}`);for(let n=2;n<e.length;n+=2){const r=e[n],a=e[n+1];if(r===i){t.push(`V${a}`);s=a}else if(a===s){t.push(`H${r}`);i=r}}t.push("Z")}return t.join(" ")}serialize([t,e,i,s],n){const r=[],a=i-t,o=s-e;for(const e of this.#Er){const i=new Array(e.length);for(let n=0;n<e.length;n+=2){i[n]=t+e[n]*a;i[n+1]=s-e[n+1]*o}r.push(i)}return r}get box(){return this.#Vn}get classNamesForOutlining(){return["highlightOutline"]}}class FreeHighlightOutliner extends FreeDrawOutliner{newFreeDrawOutline(t,e,i,s,n,r){return new FreeHighlightOutline(t,e,i,s,n,r)}}class FreeHighlightOutline extends FreeDrawOutline{newOutliner(t,e,i,s,n,r=0){return new FreeHighlightOutliner(t,e,i,s,n,r)}}class ColorPicker{#Sr=null;#Cr=null;#Tr;#Mr=null;#Pr=!1;#Dr=!1;#r=null;#kr;#Rr=null;#f=null;#Ir;static#Fr=null;static get _keyboardManager(){return shadow(this,"_keyboardManager",new KeyboardManager([[["Escape","mac+Escape"],ColorPicker.prototype._hideDropdownFromKeyboard],[[" ","mac+ "],ColorPicker.prototype._colorSelectFromKeyboard],[["ArrowDown","ArrowRight","mac+ArrowDown","mac+ArrowRight"],ColorPicker.prototype._moveToNext],[["ArrowUp","ArrowLeft","mac+ArrowUp","mac+ArrowLeft"],ColorPicker.prototype._moveToPrevious],[["Home","mac+Home"],ColorPicker.prototype._moveToBeginning],[["End","mac+End"],ColorPicker.prototype._moveToEnd]]))}constructor({editor:t=null,uiManager:e=null}){if(t){this.#Dr=!1;this.#Ir=m.HIGHLIGHT_COLOR;this.#r=t}else{this.#Dr=!0;this.#Ir=m.HIGHLIGHT_DEFAULT_COLOR}this.#f=t?._uiManager||e;this.#kr=this.#f._eventBus;this.#Tr=t?.color||this.#f?.highlightColors.values().next().value||"#FFFF98";ColorPicker.#Fr||=Object.freeze({blue:"pdfjs-editor-colorpicker-blue",green:"pdfjs-editor-colorpicker-green",pink:"pdfjs-editor-colorpicker-pink",red:"pdfjs-editor-colorpicker-red",yellow:"pdfjs-editor-colorpicker-yellow"})}renderButton(){const t=this.#Sr=document.createElement("button");t.className="colorPicker";t.tabIndex="0";t.setAttribute("data-l10n-id","pdfjs-editor-colorpicker-button");t.setAttribute("aria-haspopup",!0);const e=this.#f._signal;t.addEventListener("click",this.#Lr.bind(this),{signal:e});t.addEventListener("keydown",this.#qs.bind(this),{signal:e});const i=this.#Cr=document.createElement("span");i.className="swatch";i.setAttribute("aria-hidden",!0);i.style.backgroundColor=this.#Tr;t.append(i);return t}renderMainDropdown(){const t=this.#Mr=this.#Or();t.setAttribute("aria-orientation","horizontal");t.setAttribute("aria-labelledby","highlightColorPickerLabel");return t}#Or(){const t=document.createElement("div"),e=this.#f._signal;t.addEventListener("contextmenu",noContextMenu,{signal:e});t.className="dropdown";t.role="listbox";t.setAttribute("aria-multiselectable",!1);t.setAttribute("aria-orientation","vertical");t.setAttribute("data-l10n-id","pdfjs-editor-colorpicker-dropdown");for(const[i,s]of this.#f.highlightColors){const n=document.createElement("button");n.tabIndex="0";n.role="option";n.setAttribute("data-color",s);n.title=i;n.setAttribute("data-l10n-id",ColorPicker.#Fr[i]);const r=document.createElement("span");n.append(r);r.className="swatch";r.style.backgroundColor=s;n.setAttribute("aria-selected",s===this.#Tr);n.addEventListener("click",this.#Nr.bind(this,s),{signal:e});t.append(n)}t.addEventListener("keydown",this.#qs.bind(this),{signal:e});return t}#Nr(t,e){e.stopPropagation();this.#kr.dispatch("switchannotationeditorparams",{source:this,type:this.#Ir,value:t})}_colorSelectFromKeyboard(t){if(t.target===this.#Sr){this.#Lr(t);return}const e=t.target.getAttribute("data-color");e&&this.#Nr(e,t)}_moveToNext(t){this.#Br?t.target!==this.#Sr?t.target.nextSibling?.focus():this.#Mr.firstChild?.focus():this.#Lr(t)}_moveToPrevious(t){if(t.target!==this.#Mr?.firstChild&&t.target!==this.#Sr){this.#Br||this.#Lr(t);t.target.previousSibling?.focus()}else this.#Br&&this._hideDropdownFromKeyboard()}_moveToBeginning(t){this.#Br?this.#Mr.firstChild?.focus():this.#Lr(t)}_moveToEnd(t){this.#Br?this.#Mr.lastChild?.focus():this.#Lr(t)}#qs(t){ColorPicker._keyboardManager.exec(this,t)}#Lr(t){if(this.#Br){this.hideDropdown();return}this.#Pr=0===t.detail;if(!this.#Rr){this.#Rr=new AbortController;window.addEventListener("pointerdown",this.#h.bind(this),{signal:this.#f.combinedSignal(this.#Rr)})}if(this.#Mr){this.#Mr.classList.remove("hidden");return}const e=this.#Mr=this.#Or();this.#Sr.append(e)}#h(t){this.#Mr?.contains(t.target)||this.hideDropdown()}hideDropdown(){this.#Mr?.classList.add("hidden");this.#Rr?.abort();this.#Rr=null}get#Br(){return this.#Mr&&!this.#Mr.classList.contains("hidden")}_hideDropdownFromKeyboard(){if(!this.#Dr)if(this.#Br){this.hideDropdown();this.#Sr.focus({preventScroll:!0,focusVisible:this.#Pr})}else this.#r?.unselect()}updateColor(t){this.#Cr&&(this.#Cr.style.backgroundColor=t);if(!this.#Mr)return;const e=this.#f.highlightColors.values();for(const i of this.#Mr.children)i.setAttribute("aria-selected",e.next().value===t)}destroy(){this.#Sr?.remove();this.#Sr=null;this.#Cr=null;this.#Mr?.remove();this.#Mr=null}}class HighlightEditor extends AnnotationEditor{#Hr=null;#Ur=0;#zr;#jr=null;#n=null;#Gr=null;#Vr=null;#$r=0;#Wr=null;#qr=null;#y=null;#Xr=!1;#mr=null;#Yr;#Kr=null;#Qr="";#er;#Jr="";static _defaultColor=null;static _defaultOpacity=1;static _defaultThickness=12;static _type="highlight";static _editorType=f.HIGHLIGHT;static _freeHighlightId=-1;static _freeHighlight=null;static _freeHighlightClipId="";static get _keyboardManager(){const t=HighlightEditor.prototype;return shadow(this,"_keyboardManager",new KeyboardManager([[["ArrowLeft","mac+ArrowLeft"],t._moveCaret,{args:[0]}],[["ArrowRight","mac+ArrowRight"],t._moveCaret,{args:[1]}],[["ArrowUp","mac+ArrowUp"],t._moveCaret,{args:[2]}],[["ArrowDown","mac+ArrowDown"],t._moveCaret,{args:[3]}]]))}constructor(t){super({...t,name:"highlightEditor"});this.color=t.color||HighlightEditor._defaultColor;this.#er=t.thickness||HighlightEditor._defaultThickness;this.#Yr=t.opacity||HighlightEditor._defaultOpacity;this.#zr=t.boxes||null;this.#Jr=t.methodOfCreation||"";this.#Qr=t.text||"";this._isDraggable=!1;if(t.highlightId>-1){this.#Xr=!0;this.#Zr(t);this.#ta()}else if(this.#zr){this.#Hr=t.anchorNode;this.#Ur=t.anchorOffset;this.#Vr=t.focusNode;this.#$r=t.focusOffset;this.#ea();this.#ta();this.rotate(this.rotation)}}get telemetryInitialData(){return{action:"added",type:this.#Xr?"free_highlight":"highlight",color:this._uiManager.highlightColorNames.get(this.color),thickness:this.#er,methodOfCreation:this.#Jr}}get telemetryFinalData(){return{type:"highlight",color:this._uiManager.highlightColorNames.get(this.color)}}static computeTelemetryFinalData(t){return{numberOfColors:t.get("color").size}}#ea(){const t=new HighlightOutliner(this.#zr,.001);this.#qr=t.getOutlines();[this.x,this.y,this.width,this.height]=this.#qr.box;const e=new HighlightOutliner(this.#zr,.0025,.001,"ltr"===this._uiManager.direction);this.#Gr=e.getOutlines();const{lastPoint:i}=this.#Gr;this.#mr=[(i[0]-this.x)/this.width,(i[1]-this.y)/this.height]}#Zr({highlightOutlines:t,highlightId:e,clipPathId:i}){this.#qr=t;this.#Gr=t.getNewOutline(this.#er/2****,.0025);if(e>=0){this.#y=e;this.#jr=i;this.parent.drawLayer.finalizeDraw(e,{bbox:t.box,path:{d:t.toSVGPath()}});this.#Kr=this.parent.drawLayer.drawOutline({rootClass:{highlightOutline:!0,free:!0},bbox:this.#Gr.box,path:{d:this.#Gr.toSVGPath()}},!0)}else if(this.parent){const e=this.parent.viewport.rotation;this.parent.drawLayer.updateProperties(this.#y,{bbox:HighlightEditor.#ia(this.#qr.box,(e-this.rotation+360)%360),path:{d:t.toSVGPath()}});this.parent.drawLayer.updateProperties(this.#Kr,{bbox:HighlightEditor.#ia(this.#Gr.box,e),path:{d:this.#Gr.toSVGPath()}})}const[s,n,r,a]=t.box;switch(this.rotation){case 0:this.x=s;this.y=n;this.width=r;this.height=a;break;case 90:{const[t,e]=this.parentDimensions;this.x=n;this.y=1-s;this.width=r*e/t;this.height=a*t/e;break}case 180:this.x=1-s;this.y=1-n;this.width=r;this.height=a;break;case 270:{const[t,e]=this.parentDimensions;this.x=1-n;this.y=s;this.width=r*e/t;this.height=a*t/e;break}}const{lastPoint:o}=this.#Gr;this.#mr=[(o[0]-s)/r,(o[1]-n)/a]}static initialize(t,e){AnnotationEditor.initialize(t,e);HighlightEditor._defaultColor||=e.highlightColors?.values().next().value||"#fff066"}static updateDefaultParams(t,e){switch(t){case m.HIGHLIGHT_DEFAULT_COLOR:HighlightEditor._defaultColor=e;break;case m.HIGHLIGHT_THICKNESS:HighlightEditor._defaultThickness=e}}translateInPage(t,e){}get toolbarPosition(){return this.#mr}updateParams(t,e){switch(t){case m.HIGHLIGHT_COLOR:this.#On(e);break;case m.HIGHLIGHT_THICKNESS:this.#sa(e)}}static get defaultPropertiesToUpdate(){return[[m.HIGHLIGHT_DEFAULT_COLOR,HighlightEditor._defaultColor],[m.HIGHLIGHT_THICKNESS,HighlightEditor._defaultThickness]]}get propertiesToUpdate(){return[[m.HIGHLIGHT_COLOR,this.color||HighlightEditor._defaultColor],[m.HIGHLIGHT_THICKNESS,this.#er||HighlightEditor._defaultThickness],[m.HIGHLIGHT_FREE,this.#Xr]]}#On(t){const setColorAndOpacity=(t,e)=>{this.color=t;this.#Yr=e;this.parent?.drawLayer.updateProperties(this.#y,{root:{fill:t,"fill-opacity":e}});this.#n?.updateColor(t)},e=this.color,i=this.#Yr;this.addCommands({cmd:setColorAndOpacity.bind(this,t,HighlightEditor._defaultOpacity),undo:setColorAndOpacity.bind(this,e,i),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:m.HIGHLIGHT_COLOR,overwriteIfSameType:!0,keepUndo:!0});this._reportTelemetry({action:"color_changed",color:this._uiManager.highlightColorNames.get(t)},!0)}#sa(t){const e=this.#er,setThickness=t=>{this.#er=t;this.#na(t)};this.addCommands({cmd:setThickness.bind(this,t),undo:setThickness.bind(this,e),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:m.INK_THICKNESS,overwriteIfSameType:!0,keepUndo:!0});this._reportTelemetry({action:"thickness_changed",thickness:t},!0)}async addEditToolbar(){const t=await super.addEditToolbar();if(!t)return null;if(this._uiManager.highlightColors){this.#n=new ColorPicker({editor:this});t.addColorPicker(this.#n)}return t}disableEditing(){super.disableEditing();this.div.classList.toggle("disabled",!0)}enableEditing(){super.enableEditing();this.div.classList.toggle("disabled",!1)}fixAndSetPosition(){return super.fixAndSetPosition(this.#ra())}getBaseTranslation(){return[0,0]}getRect(t,e){return super.getRect(t,e,this.#ra())}onceAdded(t){this.annotationElementId||this.parent.addUndoableEditor(this);t&&this.div.focus()}remove(){this.#aa();this._reportTelemetry({action:"deleted"});super.remove()}rebuild(){if(this.parent){super.rebuild();if(null!==this.div){this.#ta();this.isAttachedToDOM||this.parent.add(this)}}}setParent(t){let e=!1;if(this.parent&&!t)this.#aa();else if(t){this.#ta(t);e=!this.parent&&this.div?.classList.contains("selectedEditor")}super.setParent(t);this.show(this._isVisible);e&&this.select()}#na(t){if(!this.#Xr)return;this.#Zr({highlightOutlines:this.#qr.getNewOutline(t/2)});this.fixAndSetPosition();const[e,i]=this.parentDimensions;this.setDims(this.width*e,this.height*i)}#aa(){if(null!==this.#y&&this.parent){this.parent.drawLayer.remove(this.#y);this.#y=null;this.parent.drawLayer.remove(this.#Kr);this.#Kr=null}}#ta(t=this.parent){if(null===this.#y){({id:this.#y,clipPathId:this.#jr}=t.drawLayer.draw({bbox:this.#qr.box,root:{viewBox:"0 0 1 1",fill:this.color,"fill-opacity":this.#Yr},rootClass:{highlight:!0,free:this.#Xr},path:{d:this.#qr.toSVGPath()}},!1,!0));this.#Kr=t.drawLayer.drawOutline({rootClass:{highlightOutline:!0,free:this.#Xr},bbox:this.#Gr.box,path:{d:this.#Gr.toSVGPath()}},this.#Xr);this.#Wr&&(this.#Wr.style.clipPath=this.#jr)}}static#ia([t,e,i,s],n){switch(n){case 90:return[1-e-s,t,s,i];case 180:return[1-t-i,1-e-s,i,s];case 270:return[e,1-t-i,s,i]}return[t,e,i,s]}rotate(t){const{drawLayer:e}=this.parent;let i;if(this.#Xr){t=(t-this.rotation+360)%360;i=HighlightEditor.#ia(this.#qr.box,t)}else i=HighlightEditor.#ia([this.x,this.y,this.width,this.height],t);e.updateProperties(this.#y,{bbox:i,root:{"data-main-rotation":t}});e.updateProperties(this.#Kr,{bbox:HighlightEditor.#ia(this.#Gr.box,t),root:{"data-main-rotation":t}})}render(){if(this.div)return this.div;const t=super.render();if(this.#Qr){t.setAttribute("aria-label",this.#Qr);t.setAttribute("role","mark")}this.#Xr?t.classList.add("free"):this.div.addEventListener("keydown",this.#oa.bind(this),{signal:this._uiManager._signal});const e=this.#Wr=document.createElement("div");t.append(e);e.setAttribute("aria-hidden","true");e.className="internal";e.style.clipPath=this.#jr;const[i,s]=this.parentDimensions;this.setDims(this.width*i,this.height*s);bindEvents(this,this.#Wr,["pointerover","pointerleave"]);this.enableEditing();return t}pointerover(){this.isSelected||this.parent?.drawLayer.updateProperties(this.#Kr,{rootClass:{hovered:!0}})}pointerleave(){this.isSelected||this.parent?.drawLayer.updateProperties(this.#Kr,{rootClass:{hovered:!1}})}#oa(t){HighlightEditor._keyboardManager.exec(this,t)}_moveCaret(t){this.parent.unselect(this);switch(t){case 0:case 2:this.#la(!0);break;case 1:case 3:this.#la(!1)}}#la(t){if(!this.#Hr)return;const e=window.getSelection();t?e.setPosition(this.#Hr,this.#Ur):e.setPosition(this.#Vr,this.#$r)}select(){super.select();this.#Kr&&this.parent?.drawLayer.updateProperties(this.#Kr,{rootClass:{hovered:!1,selected:!0}})}unselect(){super.unselect();if(this.#Kr){this.parent?.drawLayer.updateProperties(this.#Kr,{rootClass:{selected:!1}});this.#Xr||this.#la(!1)}}get _mustFixPosition(){return!this.#Xr}show(t=this._isVisible){super.show(t);if(this.parent){this.parent.drawLayer.updateProperties(this.#y,{rootClass:{hidden:!t}});this.parent.drawLayer.updateProperties(this.#Kr,{rootClass:{hidden:!t}})}}#ra(){return this.#Xr?this.rotation:0}#ha(){if(this.#Xr)return null;const[t,e]=this.pageDimensions,[i,s]=this.pageTranslation,n=this.#zr,r=new Float32Array(8*n.length);let a=0;for(const{x:o,y:l,width:h,height:c}of n){const n=o*t+i,d=(1-l)*e+s;r[a]=r[a+4]=n;r[a+1]=r[a+3]=d;r[a+2]=r[a+6]=n+h*t;r[a+5]=r[a+7]=d-c*e;a+=8}return r}#ca(t){return this.#qr.serialize(t,this.#ra())}static startHighlighting(t,e,{target:i,x:s,y:n}){const{x:r,y:a,width:o,height:l}=i.getBoundingClientRect(),h=new AbortController,c=t.combinedSignal(h),pointerUpCallback=e=>{h.abort();this.#da(t,e)};window.addEventListener("blur",pointerUpCallback,{signal:c});window.addEventListener("pointerup",pointerUpCallback,{signal:c});window.addEventListener("pointerdown",stopEvent,{capture:!0,passive:!1,signal:c});window.addEventListener("contextmenu",noContextMenu,{signal:c});i.addEventListener("pointermove",this.#ua.bind(this,t),{signal:c});this._freeHighlight=new FreeHighlightOutliner({x:s,y:n},[r,a,o,l],t.scale,this._defaultThickness/2,e,.001);({id:this._freeHighlightId,clipPathId:this._freeHighlightClipId}=t.drawLayer.draw({bbox:[0,0,1,1],root:{viewBox:"0 0 1 1",fill:this._defaultColor,"fill-opacity":this._defaultOpacity},rootClass:{highlight:!0,free:!0},path:{d:this._freeHighlight.toSVGPath()}},!0,!0))}static#ua(t,e){this._freeHighlight.add(e)&&t.drawLayer.updateProperties(this._freeHighlightId,{path:{d:this._freeHighlight.toSVGPath()}})}static#da(t,e){this._freeHighlight.isEmpty()?t.drawLayer.remove(this._freeHighlightId):t.createAndAddNewEditor(e,!1,{highlightId:this._freeHighlightId,highlightOutlines:this._freeHighlight.getOutlines(),clipPathId:this._freeHighlightClipId,methodOfCreation:"main_toolbar"});this._freeHighlightId=-1;this._freeHighlight=null;this._freeHighlightClipId=""}static async deserialize(t,e,i){let s=null;if(t instanceof HighlightAnnotationElement){const{data:{quadPoints:e,rect:i,rotation:n,id:r,color:a,opacity:o,popupRef:l},parent:{page:{pageNumber:h}}}=t;s=t={annotationType:f.HIGHLIGHT,color:Array.from(a),opacity:o,quadPoints:e,boxes:null,pageIndex:h-1,rect:i.slice(0),rotation:n,id:r,deleted:!1,popupRef:l}}else if(t instanceof InkAnnotationElement){const{data:{inkLists:e,rect:i,rotation:n,id:r,color:a,borderStyle:{rawWidth:o},popupRef:l},parent:{page:{pageNumber:h}}}=t;s=t={annotationType:f.HIGHLIGHT,color:Array.from(a),thickness:o,inkLists:e,boxes:null,pageIndex:h-1,rect:i.slice(0),rotation:n,id:r,deleted:!1,popupRef:l}}const{color:n,quadPoints:r,inkLists:a,opacity:o}=t,l=await super.deserialize(t,e,i);l.color=Util.makeHexColor(...n);l.#Yr=o||1;a&&(l.#er=t.thickness);l.annotationElementId=t.id||null;l._initialData=s;const[h,c]=l.pageDimensions,[d,u]=l.pageTranslation;if(r){const t=l.#zr=[];for(let e=0;e<r.length;e+=8)t.push({x:(r[e]-d)/h,y:1-(r[e+1]-u)/c,width:(r[e+2]-r[e])/h,height:(r[e+1]-r[e+5])/c});l.#ea();l.#ta();l.rotate(l.rotation)}else if(a){l.#Xr=!0;const t=a[0],i={x:t[0]-d,y:c-(t[1]-u)},s=new FreeHighlightOutliner(i,[0,0,h,c],1,l.#er/2,!0,.001);for(let e=0,n=t.length;e<n;e+=2){i.x=t[e]-d;i.y=c-(t[e+1]-u);s.add(i)}const{id:n,clipPathId:r}=e.drawLayer.draw({bbox:[0,0,1,1],root:{viewBox:"0 0 1 1",fill:l.color,"fill-opacity":l._defaultOpacity},rootClass:{highlight:!0,free:!0},path:{d:s.toSVGPath()}},!0,!0);l.#Zr({highlightOutlines:s.getOutlines(),highlightId:n,clipPathId:r});l.#ta()}return l}serialize(t=!1){if(this.isEmpty()||t)return null;if(this.deleted)return this.serializeDeleted();const e=this.getRect(0,0),i=AnnotationEditor._colorManager.convert(this.color),s={annotationType:f.HIGHLIGHT,color:i,opacity:this.#Yr,thickness:this.#er,quadPoints:this.#ha(),outlines:this.#ca(e),pageIndex:this.pageIndex,rect:e,rotation:this.#ra(),structTreeParentId:this._structTreeParentId};if(this.annotationElementId&&!this.#Gn(s))return null;s.id=this.annotationElementId;return s}#Gn(t){const{color:e}=this._initialData;return t.color.some(((t,i)=>t!==e[i]))}renderAnnotationElement(t){t.updateEdited({rect:this.getRect(0,0)});return null}static canCreateNewEmptyEditor(){return!1}}class DrawingOptions{#pa=Object.create(null);updateProperty(t,e){this[t]=e;this.updateSVGProperty(t,e)}updateProperties(t){if(t)for(const[e,i]of Object.entries(t))this.updateProperty(e,i)}updateSVGProperty(t,e){this.#pa[t]=e}toSVGProperties(){const t=this.#pa;this.#pa=Object.create(null);return{root:t}}reset(){this.#pa=Object.create(null)}updateAll(t=this){this.updateProperties(t)}clone(){unreachable("Not implemented")}}class DrawingEditor extends AnnotationEditor{#ga=null;#fa;_drawId=null;static _currentDrawId=-1;static _currentParent=null;static#ma=null;static#ba=null;static#va=null;static#ya=NaN;static#wa=null;static#Aa=null;static#xa=NaN;static _INNER_MARGIN=3;constructor(t){super(t);this.#fa=t.mustBeCommitted||!1;if(t.drawOutlines){this.#_a(t);this.#ta()}}#_a({drawOutlines:t,drawId:e,drawingOptions:i}){this.#ga=t;this._drawingOptions||=i;if(e>=0){this._drawId=e;this.parent.drawLayer.finalizeDraw(e,t.defaultProperties)}else this._drawId=this.#Ea(t,this.parent);this.#Sa(t.box)}#Ea(t,e){const{id:i}=e.drawLayer.draw(DrawingEditor._mergeSVGProperties(this._drawingOptions.toSVGProperties(),t.defaultSVGProperties),!1,!1);return i}static _mergeSVGProperties(t,e){const i=new Set(Object.keys(t));for(const[s,n]of Object.entries(e))i.has(s)?Object.assign(t[s],n):t[s]=n;return t}static getDefaultDrawingOptions(t){unreachable("Not implemented")}static get typesMap(){unreachable("Not implemented")}static get isDrawer(){return!0}static get supportMultipleDrawings(){return!1}static updateDefaultParams(t,e){const i=this.typesMap.get(t);i&&this._defaultDrawingOptions.updateProperty(i,e);if(this._currentParent){DrawingEditor.#ma.updateProperty(i,e);this._currentParent.drawLayer.updateProperties(this._currentDrawId,this._defaultDrawingOptions.toSVGProperties())}}updateParams(t,e){const i=this.constructor.typesMap.get(t);i&&this._updateProperty(t,i,e)}static get defaultPropertiesToUpdate(){const t=[],e=this._defaultDrawingOptions;for(const[i,s]of this.typesMap)t.push([i,e[s]]);return t}get propertiesToUpdate(){const t=[],{_drawingOptions:e}=this;for(const[i,s]of this.constructor.typesMap)t.push([i,e[s]]);return t}_updateProperty(t,e,i){const s=this._drawingOptions,n=s[e],setter=t=>{s.updateProperty(e,t);const i=this.#ga.updateProperty(e,t);i&&this.#Sa(i);this.parent?.drawLayer.updateProperties(this._drawId,s.toSVGProperties())};this.addCommands({cmd:setter.bind(this,i),undo:setter.bind(this,n),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:t,overwriteIfSameType:!0,keepUndo:!0})}_onResizing(){this.parent?.drawLayer.updateProperties(this._drawId,DrawingEditor._mergeSVGProperties(this.#ga.getPathResizingSVGProperties(this.#Ca()),{bbox:this.#Ta()}))}_onResized(){this.parent?.drawLayer.updateProperties(this._drawId,DrawingEditor._mergeSVGProperties(this.#ga.getPathResizedSVGProperties(this.#Ca()),{bbox:this.#Ta()}))}_onTranslating(t,e){this.parent?.drawLayer.updateProperties(this._drawId,{bbox:this.#Ta(t,e)})}_onTranslated(){this.parent?.drawLayer.updateProperties(this._drawId,DrawingEditor._mergeSVGProperties(this.#ga.getPathTranslatedSVGProperties(this.#Ca(),this.parentDimensions),{bbox:this.#Ta()}))}_onStartDragging(){this.parent?.drawLayer.updateProperties(this._drawId,{rootClass:{moving:!0}})}_onStopDragging(){this.parent?.drawLayer.updateProperties(this._drawId,{rootClass:{moving:!1}})}commit(){super.commit();this.disableEditMode();this.disableEditing()}disableEditing(){super.disableEditing();this.div.classList.toggle("disabled",!0)}enableEditing(){super.enableEditing();this.div.classList.toggle("disabled",!1)}getBaseTranslation(){return[0,0]}get isResizable(){return!0}onceAdded(t){this.annotationElementId||this.parent.addUndoableEditor(this);this._isDraggable=!0;if(this.#fa){this.#fa=!1;this.commit();this.parent.setSelected(this);t&&this.isOnScreen&&this.div.focus()}}remove(){this.#aa();super.remove()}rebuild(){if(this.parent){super.rebuild();if(null!==this.div){this.#ta();this.#Sa(this.#ga.box);this.isAttachedToDOM||this.parent.add(this)}}}setParent(t){let e=!1;if(this.parent&&!t){this._uiManager.removeShouldRescale(this);this.#aa()}else if(t){this._uiManager.addShouldRescale(this);this.#ta(t);e=!this.parent&&this.div?.classList.contains("selectedEditor")}super.setParent(t);e&&this.select()}#aa(){if(null!==this._drawId&&this.parent){this.parent.drawLayer.remove(this._drawId);this._drawId=null;this._drawingOptions.reset()}}#ta(t=this.parent){if(null===this._drawId||this.parent!==t)if(null===this._drawId){this._drawingOptions.updateAll();this._drawId=this.#Ea(this.#ga,t)}else this.parent.drawLayer.updateParent(this._drawId,t.drawLayer)}#Ma([t,e,i,s]){const{parentDimensions:[n,r],rotation:a}=this;switch(a){case 90:return[e,1-t,i*(r/n),s*(n/r)];case 180:return[1-t,1-e,i,s];case 270:return[1-e,t,i*(r/n),s*(n/r)];default:return[t,e,i,s]}}#Ca(){const{x:t,y:e,width:i,height:s,parentDimensions:[n,r],rotation:a}=this;switch(a){case 90:return[1-e,t,i*(n/r),s*(r/n)];case 180:return[1-t,1-e,i,s];case 270:return[e,1-t,i*(n/r),s*(r/n)];default:return[t,e,i,s]}}#Sa(t){[this.x,this.y,this.width,this.height]=this.#Ma(t);if(this.div){this.fixAndSetPosition();const[t,e]=this.parentDimensions;this.setDims(this.width*t,this.height*e)}this._onResized()}#Ta(){const{x:t,y:e,width:i,height:s,rotation:n,parentRotation:r,parentDimensions:[a,o]}=this;switch((4*n+r)/90){case 1:return[1-e-s,t,s,i];case 2:return[1-t-i,1-e-s,i,s];case 3:return[e,1-t-i,s,i];case 4:return[t,e-i*(a/o),s*(o/a),i*(a/o)];case 5:return[1-e,t,i*(a/o),s*(o/a)];case 6:return[1-t-s*(o/a),1-e,s*(o/a),i*(a/o)];case 7:return[e-i*(a/o),1-t-s*(o/a),i*(a/o),s*(o/a)];case 8:return[t-i,e-s,i,s];case 9:return[1-e,t-i,s,i];case 10:return[1-t,1-e,i,s];case 11:return[e-s,1-t,s,i];case 12:return[t-s*(o/a),e,s*(o/a),i*(a/o)];case 13:return[1-e-i*(a/o),t-s*(o/a),i*(a/o),s*(o/a)];case 14:return[1-t,1-e-i*(a/o),s*(o/a),i*(a/o)];case 15:return[e,1-t,i*(a/o),s*(o/a)];default:return[t,e,i,s]}}rotate(){this.parent&&this.parent.drawLayer.updateProperties(this._drawId,DrawingEditor._mergeSVGProperties({bbox:this.#Ta()},this.#ga.updateRotation((this.parentRotation-this.rotation+360)%360)))}onScaleChanging(){this.parent&&this.#Sa(this.#ga.updateParentDimensions(this.parentDimensions,this.parent.scale))}static onScaleChangingWhenDrawing(){}render(){if(this.div)return this.div;const t=super.render();t.classList.add("draw");const e=document.createElement("div");t.append(e);e.setAttribute("aria-hidden","true");e.className="internal";const[i,s]=this.parentDimensions;this.setDims(this.width*i,this.height*s);this._uiManager.addShouldRescale(this);this.disableEditing();return t}static createDrawerInstance(t,e,i,s,n){unreachable("Not implemented")}static startDrawing(t,e,i,s){const{target:n,offsetX:r,offsetY:a,pointerId:o,pointerType:l}=s;if(DrawingEditor.#wa&&DrawingEditor.#wa!==l)return;const{viewport:{rotation:h}}=t,{width:c,height:d}=n.getBoundingClientRect(),u=DrawingEditor.#ba=new AbortController,p=t.combinedSignal(u);DrawingEditor.#ya||=o;DrawingEditor.#wa??=l;window.addEventListener("pointerup",(t=>{DrawingEditor.#ya===t.pointerId?this._endDraw(t):DrawingEditor.#Aa?.delete(t.pointerId)}),{signal:p});window.addEventListener("pointercancel",(t=>{DrawingEditor.#ya===t.pointerId?this._currentParent.endDrawingSession():DrawingEditor.#Aa?.delete(t.pointerId)}),{signal:p});window.addEventListener("pointerdown",(t=>{if(DrawingEditor.#wa===t.pointerType){(DrawingEditor.#Aa||=new Set).add(t.pointerId);if(DrawingEditor.#ma.isCancellable()){DrawingEditor.#ma.removeLastElement();DrawingEditor.#ma.isEmpty()?this._currentParent.endDrawingSession(!0):this._endDraw(null)}}}),{capture:!0,passive:!1,signal:p});window.addEventListener("contextmenu",noContextMenu,{signal:p});n.addEventListener("pointermove",this._drawMove.bind(this),{signal:p});n.addEventListener("touchmove",(t=>{t.timeStamp===DrawingEditor.#xa&&stopEvent(t)}),{signal:p});t.toggleDrawing();e._editorUndoBar?.hide();if(DrawingEditor.#ma)t.drawLayer.updateProperties(this._currentDrawId,DrawingEditor.#ma.startNew(r,a,c,d,h));else{e.updateUIForDefaultProperties(this);DrawingEditor.#ma=this.createDrawerInstance(r,a,c,d,h);DrawingEditor.#va=this.getDefaultDrawingOptions();this._currentParent=t;({id:this._currentDrawId}=t.drawLayer.draw(this._mergeSVGProperties(DrawingEditor.#va.toSVGProperties(),DrawingEditor.#ma.defaultSVGProperties),!0,!1))}}static _drawMove(t){DrawingEditor.#xa=-1;if(!DrawingEditor.#ma)return;const{offsetX:e,offsetY:i,pointerId:s}=t;if(DrawingEditor.#ya===s)if(DrawingEditor.#Aa?.size>=1)this._endDraw(t);else{this._currentParent.drawLayer.updateProperties(this._currentDrawId,DrawingEditor.#ma.add(e,i));DrawingEditor.#xa=t.timeStamp;stopEvent(t)}}static _cleanup(t){if(t){this._currentDrawId=-1;this._currentParent=null;DrawingEditor.#ma=null;DrawingEditor.#va=null;DrawingEditor.#wa=null;DrawingEditor.#xa=NaN}if(DrawingEditor.#ba){DrawingEditor.#ba.abort();DrawingEditor.#ba=null;DrawingEditor.#ya=NaN;DrawingEditor.#Aa=null}}static _endDraw(t){const e=this._currentParent;if(e){e.toggleDrawing(!0);this._cleanup(!1);t&&e.drawLayer.updateProperties(this._currentDrawId,DrawingEditor.#ma.end(t.offsetX,t.offsetY));if(this.supportMultipleDrawings){const t=DrawingEditor.#ma,i=this._currentDrawId,s=t.getLastElement();e.addCommands({cmd:()=>{e.drawLayer.updateProperties(i,t.setLastElement(s))},undo:()=>{e.drawLayer.updateProperties(i,t.removeLastElement())},mustExec:!1,type:m.DRAW_STEP})}else this.endDrawing(!1)}}static endDrawing(t){const e=this._currentParent;if(!e)return null;e.toggleDrawing(!0);e.cleanUndoStack(m.DRAW_STEP);if(!DrawingEditor.#ma.isEmpty()){const{pageDimensions:[i,s],scale:n}=e,r=e.createAndAddNewEditor({offsetX:0,offsetY:0},!1,{drawId:this._currentDrawId,drawOutlines:DrawingEditor.#ma.getOutlines(i*n,s*n,n,this._INNER_MARGIN),drawingOptions:DrawingEditor.#va,mustBeCommitted:!t});this._cleanup(!0);return r}e.drawLayer.remove(this._currentDrawId);this._cleanup(!0);return null}createDrawingOptions(t){}static deserializeDraw(t,e,i,s,n,r){unreachable("Not implemented")}static async deserialize(t,e,i){const{rawDims:{pageWidth:s,pageHeight:n,pageX:r,pageY:a}}=e.viewport,o=this.deserializeDraw(r,a,s,n,this._INNER_MARGIN,t),l=await super.deserialize(t,e,i);l.createDrawingOptions(t);l.#_a({drawOutlines:o});l.#ta();l.onScaleChanging();l.rotate();return l}serializeDraw(t){const[e,i]=this.pageTranslation,[s,n]=this.pageDimensions;return this.#ga.serialize([e,i,s,n],t)}renderAnnotationElement(t){t.updateEdited({rect:this.getRect(0,0)});return null}static canCreateNewEmptyEditor(){return!1}}class InkDrawOutliner{#Yn=new Float64Array(6);#bn;#Pa;#Fi;#er;#ir;#Da="";#ka=0;#Er=new InkDrawOutline;#Ra;#Ia;constructor(t,e,i,s,n,r){this.#Ra=i;this.#Ia=s;this.#Fi=n;this.#er=r;[t,e]=this.#Fa(t,e);const a=this.#bn=[NaN,NaN,NaN,NaN,t,e];this.#ir=[t,e];this.#Pa=[{line:a,points:this.#ir}];this.#Yn.set(a,0)}updateProperty(t,e){"stroke-width"===t&&(this.#er=e)}#Fa(t,e){return Outline._normalizePoint(t,e,this.#Ra,this.#Ia,this.#Fi)}isEmpty(){return!this.#Pa||0===this.#Pa.length}isCancellable(){return this.#ir.length<=10}add(t,e){[t,e]=this.#Fa(t,e);const[i,s,n,r]=this.#Yn.subarray(2,6),a=t-n,o=e-r;if(Math.hypot(this.#Ra*a,this.#Ia*o)<=2)return null;this.#ir.push(t,e);if(isNaN(i)){this.#Yn.set([n,r,t,e],2);this.#bn.push(NaN,NaN,NaN,NaN,t,e);return{path:{d:this.toSVGPath()}}}isNaN(this.#Yn[0])&&this.#bn.splice(6,6);this.#Yn.set([i,s,n,r,t,e],0);this.#bn.push(...Outline.createBezierPoints(i,s,n,r,t,e));return{path:{d:this.toSVGPath()}}}end(t,e){const i=this.add(t,e);return i||(2===this.#ir.length?{path:{d:this.toSVGPath()}}:null)}startNew(t,e,i,s,n){this.#Ra=i;this.#Ia=s;this.#Fi=n;[t,e]=this.#Fa(t,e);const r=this.#bn=[NaN,NaN,NaN,NaN,t,e];this.#ir=[t,e];const a=this.#Pa.at(-1);if(a){a.line=new Float32Array(a.line);a.points=new Float32Array(a.points)}this.#Pa.push({line:r,points:this.#ir});this.#Yn.set(r,0);this.#ka=0;this.toSVGPath();return null}getLastElement(){return this.#Pa.at(-1)}setLastElement(t){if(!this.#Pa)return this.#Er.setLastElement(t);this.#Pa.push(t);this.#bn=t.line;this.#ir=t.points;this.#ka=0;return{path:{d:this.toSVGPath()}}}removeLastElement(){if(!this.#Pa)return this.#Er.removeLastElement();this.#Pa.pop();this.#Da="";for(let t=0,e=this.#Pa.length;t<e;t++){const{line:e,points:i}=this.#Pa[t];this.#bn=e;this.#ir=i;this.#ka=0;this.toSVGPath()}return{path:{d:this.#Da}}}toSVGPath(){const t=Outline.svgRound(this.#bn[4]),e=Outline.svgRound(this.#bn[5]);if(2===this.#ir.length){this.#Da=`${this.#Da} M ${t} ${e} Z`;return this.#Da}if(this.#ir.length<=6){const i=this.#Da.lastIndexOf("M");this.#Da=`${this.#Da.slice(0,i)} M ${t} ${e}`;this.#ka=6}if(4===this.#ir.length){const t=Outline.svgRound(this.#bn[10]),e=Outline.svgRound(this.#bn[11]);this.#Da=`${this.#Da} L ${t} ${e}`;this.#ka=12;return this.#Da}const i=[];if(0===this.#ka){i.push(`M ${t} ${e}`);this.#ka=6}for(let t=this.#ka,e=this.#bn.length;t<e;t+=6){const[e,s,n,r,a,o]=this.#bn.slice(t,t+6).map(Outline.svgRound);i.push(`C${e} ${s} ${n} ${r} ${a} ${o}`)}this.#Da+=i.join(" ");this.#ka=this.#bn.length;return this.#Da}getOutlines(t,e,i,s){const n=this.#Pa.at(-1);n.line=new Float32Array(n.line);n.points=new Float32Array(n.points);this.#Er.build(this.#Pa,t,e,i,this.#Fi,this.#er,s);this.#Yn=null;this.#bn=null;this.#Pa=null;this.#Da=null;return this.#Er}get defaultSVGProperties(){return{root:{viewBox:"0 0 10000 10000"},rootClass:{draw:!0},bbox:[0,0,1,1]}}}class InkDrawOutline extends Outline{#pr;#La=0;#Wn;#Pa;#Ra;#Ia;#Oa;#Fi;#er;build(t,e,i,s,n,r,a){this.#Ra=e;this.#Ia=i;this.#Oa=s;this.#Fi=n;this.#er=r;this.#Wn=a??0;this.#Pa=t;this.#Na()}setLastElement(t){this.#Pa.push(t);return{path:{d:this.toSVGPath()}}}removeLastElement(){this.#Pa.pop();return{path:{d:this.toSVGPath()}}}toSVGPath(){const t=[];for(const{line:e}of this.#Pa){t.push(`M${Outline.svgRound(e[4])} ${Outline.svgRound(e[5])}`);if(6!==e.length)if(12!==e.length)for(let i=6,s=e.length;i<s;i+=6){const[s,n,r,a,o,l]=e.subarray(i,i+6).map(Outline.svgRound);t.push(`C${s} ${n} ${r} ${a} ${o} ${l}`)}else t.push(`L${Outline.svgRound(e[10])} ${Outline.svgRound(e[11])}`);else t.push("Z")}return t.join("")}serialize([t,e,i,s],n){const r=[],a=[],[o,l,h,c]=this.#Ba();let d,u,p,g,f,m,b,v,y;switch(this.#Fi){case 0:y=Outline._rescale;d=t;u=e+s;p=i;g=-s;f=t+o*i;m=e+(1-l-c)*s;b=t+(o+h)*i;v=e+(1-l)*s;break;case 90:y=Outline._rescaleAndSwap;d=t;u=e;p=i;g=s;f=t+l*i;m=e+o*s;b=t+(l+c)*i;v=e+(o+h)*s;break;case 180:y=Outline._rescale;d=t+i;u=e;p=-i;g=s;f=t+(1-o-h)*i;m=e+l*s;b=t+(1-o)*i;v=e+(l+c)*s;break;case 270:y=Outline._rescaleAndSwap;d=t+i;u=e+s;p=-i;g=-s;f=t+(1-l-c)*i;m=e+(1-o-h)*s;b=t+(1-l)*i;v=e+(1-o)*s}for(const{line:t,points:e}of this.#Pa){r.push(y(t,d,u,p,g,n?new Array(t.length):null));a.push(y(e,d,u,p,g,n?new Array(e.length):null))}return{lines:r,points:a,rect:[f,m,b,v]}}static deserialize(t,e,i,s,n,{paths:{lines:r,points:a},rotation:o,thickness:l}){const h=[];let c,d,u,p,g;switch(o){case 0:g=Outline._rescale;c=-t/i;d=e/s+1;u=1/i;p=-1/s;break;case 90:g=Outline._rescaleAndSwap;c=-e/s;d=-t/i;u=1/s;p=1/i;break;case 180:g=Outline._rescale;c=t/i+1;d=-e/s;u=-1/i;p=1/s;break;case 270:g=Outline._rescaleAndSwap;c=e/s+1;d=t/i+1;u=-1/s;p=-1/i}if(!r){r=[];for(const t of a){const e=t.length;if(2===e){r.push(new Float32Array([NaN,NaN,NaN,NaN,t[0],t[1]]));continue}if(4===e){r.push(new Float32Array([NaN,NaN,NaN,NaN,t[0],t[1],NaN,NaN,NaN,NaN,t[2],t[3]]));continue}const i=new Float32Array(3*(e-2));r.push(i);let[s,n,a,o]=t.subarray(0,4);i.set([NaN,NaN,NaN,NaN,s,n],0);for(let r=4;r<e;r+=2){const e=t[r],l=t[r+1];i.set(Outline.createBezierPoints(s,n,a,o,e,l),3*(r-2));[s,n,a,o]=[a,o,e,l]}}}for(let t=0,e=r.length;t<e;t++)h.push({line:g(r[t].map((t=>t??NaN)),c,d,u,p),points:g(a[t].map((t=>t??NaN)),c,d,u,p)});const f=new InkDrawOutline;f.build(h,i,s,1,o,l,n);return f}#Ha(t=this.#er){const e=this.#Wn+t/2*this.#Oa;return this.#Fi%180==0?[e/this.#Ra,e/this.#Ia]:[e/this.#Ia,e/this.#Ra]}#Ba(){const[t,e,i,s]=this.#pr,[n,r]=this.#Ha(0);return[t+n,e+r,i-2*n,s-2*r]}#Na(){const t=this.#pr=new Float32Array([1/0,1/0,-1/0,-1/0]);for(const{line:e}of this.#Pa){if(e.length<=12){for(let i=4,s=e.length;i<s;i+=6){const[s,n]=e.subarray(i,i+2);t[0]=Math.min(t[0],s);t[1]=Math.min(t[1],n);t[2]=Math.max(t[2],s);t[3]=Math.max(t[3],n)}continue}let i=e[4],s=e[5];for(let n=6,r=e.length;n<r;n+=6){const[r,a,o,l,h,c]=e.subarray(n,n+6);Util.bezierBoundingBox(i,s,r,a,o,l,h,c,t);i=h;s=c}}const[e,i]=this.#Ha();t[0]=Math.min(1,Math.max(0,t[0]-e));t[1]=Math.min(1,Math.max(0,t[1]-i));t[2]=Math.min(1,Math.max(0,t[2]+e));t[3]=Math.min(1,Math.max(0,t[3]+i));t[2]-=t[0];t[3]-=t[1]}get box(){return this.#pr}updateProperty(t,e){return"stroke-width"===t?this.#sa(e):null}#sa(t){const[e,i]=this.#Ha();this.#er=t;const[s,n]=this.#Ha(),[r,a]=[s-e,n-i],o=this.#pr;o[0]-=r;o[1]-=a;o[2]+=2*r;o[3]+=2*a;return o}updateParentDimensions([t,e],i){const[s,n]=this.#Ha();this.#Ra=t;this.#Ia=e;this.#Oa=i;const[r,a]=this.#Ha(),o=r-s,l=a-n,h=this.#pr;h[0]-=o;h[1]-=l;h[2]+=2*o;h[3]+=2*l;return h}updateRotation(t){this.#La=t;return{path:{transform:this.rotationTransform}}}get viewBox(){return this.#pr.map(Outline.svgRound).join(" ")}get defaultProperties(){const[t,e]=this.#pr;return{root:{viewBox:this.viewBox},path:{"transform-origin":`${Outline.svgRound(t)} ${Outline.svgRound(e)}`}}}get rotationTransform(){const[,,t,e]=this.#pr;let i=0,s=0,n=0,r=0,a=0,o=0;switch(this.#La){case 90:s=e/t;n=-t/e;a=t;break;case 180:i=-1;r=-1;a=t;o=e;break;case 270:s=-e/t;n=t/e;o=e;break;default:return""}return`matrix(${i} ${s} ${n} ${r} ${Outline.svgRound(a)} ${Outline.svgRound(o)})`}getPathResizingSVGProperties([t,e,i,s]){const[n,r]=this.#Ha(),[a,o,l,h]=this.#pr;if(Math.abs(l-n)<=Outline.PRECISION||Math.abs(h-r)<=Outline.PRECISION){const n=t+i/2-(a+l/2),r=e+s/2-(o+h/2);return{path:{"transform-origin":`${Outline.svgRound(t)} ${Outline.svgRound(e)}`,transform:`${this.rotationTransform} translate(${n} ${r})`}}}const c=(i-2*n)/(l-2*n),d=(s-2*r)/(h-2*r),u=l/i,p=h/s;return{path:{"transform-origin":`${Outline.svgRound(a)} ${Outline.svgRound(o)}`,transform:`${this.rotationTransform} scale(${u} ${p}) translate(${Outline.svgRound(n)} ${Outline.svgRound(r)}) scale(${c} ${d}) translate(${Outline.svgRound(-n)} ${Outline.svgRound(-r)})`}}}getPathResizedSVGProperties([t,e,i,s]){const[n,r]=this.#Ha(),a=this.#pr,[o,l,h,c]=a;a[0]=t;a[1]=e;a[2]=i;a[3]=s;if(Math.abs(h-n)<=Outline.PRECISION||Math.abs(c-r)<=Outline.PRECISION){const n=t+i/2-(o+h/2),r=e+s/2-(l+c/2);for(const{line:t,points:e}of this.#Pa){Outline._translate(t,n,r,t);Outline._translate(e,n,r,e)}return{root:{viewBox:this.viewBox},path:{"transform-origin":`${Outline.svgRound(t)} ${Outline.svgRound(e)}`,transform:this.rotationTransform||null,d:this.toSVGPath()}}}const d=(i-2*n)/(h-2*n),u=(s-2*r)/(c-2*r),p=-d*(o+n)+t+n,g=-u*(l+r)+e+r;if(1!==d||1!==u||0!==p||0!==g)for(const{line:t,points:e}of this.#Pa){Outline._rescale(t,p,g,d,u,t);Outline._rescale(e,p,g,d,u,e)}return{root:{viewBox:this.viewBox},path:{"transform-origin":`${Outline.svgRound(t)} ${Outline.svgRound(e)}`,transform:this.rotationTransform||null,d:this.toSVGPath()}}}getPathTranslatedSVGProperties([t,e],i){const[s,n]=i,r=this.#pr,a=t-r[0],o=e-r[1];if(this.#Ra===s&&this.#Ia===n)for(const{line:t,points:e}of this.#Pa){Outline._translate(t,a,o,t);Outline._translate(e,a,o,e)}else{const t=this.#Ra/s,e=this.#Ia/n;this.#Ra=s;this.#Ia=n;for(const{line:i,points:s}of this.#Pa){Outline._rescale(i,a,o,t,e,i);Outline._rescale(s,a,o,t,e,s)}r[2]*=t;r[3]*=e}r[0]=t;r[1]=e;return{root:{viewBox:this.viewBox},path:{d:this.toSVGPath(),"transform-origin":`${Outline.svgRound(t)} ${Outline.svgRound(e)}`}}}get defaultSVGProperties(){const t=this.#pr;return{root:{viewBox:this.viewBox},rootClass:{draw:!0},path:{d:this.toSVGPath(),"transform-origin":`${Outline.svgRound(t[0])} ${Outline.svgRound(t[1])}`,transform:this.rotationTransform||null},bbox:t}}}class InkDrawingOptions extends DrawingOptions{#Ua;constructor(t){super();this.#Ua=t;super.updateProperties({fill:"none",stroke:AnnotationEditor._defaultLineColor,"stroke-opacity":1,"stroke-width":1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-miterlimit":10})}updateSVGProperty(t,e){if("stroke-width"===t){e??=this["stroke-width"];e*=this.#Ua.realScale}super.updateSVGProperty(t,e)}clone(){const t=new InkDrawingOptions(this.#Ua);t.updateAll(this);return t}}class InkEditor extends DrawingEditor{static _type="ink";static _editorType=f.INK;static _defaultDrawingOptions=null;constructor(t){super({...t,name:"inkEditor"});this._willKeepAspectRatio=!0}static initialize(t,e){AnnotationEditor.initialize(t,e);this._defaultDrawingOptions=new InkDrawingOptions(e.viewParameters)}static getDefaultDrawingOptions(t){const e=this._defaultDrawingOptions.clone();e.updateProperties(t);return e}static get supportMultipleDrawings(){return!0}static get typesMap(){return shadow(this,"typesMap",new Map([[m.INK_THICKNESS,"stroke-width"],[m.INK_COLOR,"stroke"],[m.INK_OPACITY,"stroke-opacity"]]))}static createDrawerInstance(t,e,i,s,n){return new InkDrawOutliner(t,e,i,s,n,this._defaultDrawingOptions["stroke-width"])}static deserializeDraw(t,e,i,s,n,r){return InkDrawOutline.deserialize(t,e,i,s,n,r)}static async deserialize(t,e,i){let s=null;if(t instanceof InkAnnotationElement){const{data:{inkLists:e,rect:i,rotation:n,id:r,color:a,opacity:o,borderStyle:{rawWidth:l},popupRef:h},parent:{page:{pageNumber:c}}}=t;s=t={annotationType:f.INK,color:Array.from(a),thickness:l,opacity:o,paths:{points:e},boxes:null,pageIndex:c-1,rect:i.slice(0),rotation:n,id:r,deleted:!1,popupRef:h}}const n=await super.deserialize(t,e,i);n.annotationElementId=t.id||null;n._initialData=s;return n}onScaleChanging(){if(!this.parent)return;super.onScaleChanging();const{_drawId:t,_drawingOptions:e,parent:i}=this;e.updateSVGProperty("stroke-width");i.drawLayer.updateProperties(t,e.toSVGProperties())}static onScaleChangingWhenDrawing(){const t=this._currentParent;if(t){super.onScaleChangingWhenDrawing();this._defaultDrawingOptions.updateSVGProperty("stroke-width");t.drawLayer.updateProperties(this._currentDrawId,this._defaultDrawingOptions.toSVGProperties())}}createDrawingOptions({color:t,thickness:e,opacity:i}){this._drawingOptions=InkEditor.getDefaultDrawingOptions({stroke:Util.makeHexColor(...t),"stroke-width":e,"stroke-opacity":i})}serialize(t=!1){if(this.isEmpty())return null;if(this.deleted)return this.serializeDeleted();const{lines:e,points:i,rect:s}=this.serializeDraw(t),{_drawingOptions:{stroke:n,"stroke-opacity":r,"stroke-width":a}}=this,o={annotationType:f.INK,color:AnnotationEditor._colorManager.convert(n),opacity:r,thickness:a,paths:{lines:e,points:i},pageIndex:this.pageIndex,rect:s,rotation:this.rotation,structTreeParentId:this._structTreeParentId};if(t)return o;if(this.annotationElementId&&!this.#Gn(o))return null;o.id=this.annotationElementId;return o}#Gn(t){const{color:e,thickness:i,opacity:s,pageIndex:n}=this._initialData;return this._hasBeenMoved||this._hasBeenResized||t.color.some(((t,i)=>t!==e[i]))||t.thickness!==i||t.opacity!==s||t.pageIndex!==n}renderAnnotationElement(t){const{points:e,rect:i}=this.serializeDraw(!1);t.updateEdited({rect:i,thickness:this._drawingOptions["stroke-width"],points:e});return null}}class StampEditor extends AnnotationEditor{#za=null;#ja=null;#Ga=null;#Va=null;#$a=null;#Wa="";#qa=null;#Xa=null;#Ya=!1;#Ka=!1;static _type="stamp";static _editorType=f.STAMP;constructor(t){super({...t,name:"stampEditor"});this.#Va=t.bitmapUrl;this.#$a=t.bitmapFile}static initialize(t,e){AnnotationEditor.initialize(t,e)}static get supportedTypes(){return shadow(this,"supportedTypes",["apng","avif","bmp","gif","jpeg","png","svg+xml","webp","x-icon"].map((t=>`image/${t}`)))}static get supportedTypesStr(){return shadow(this,"supportedTypesStr",this.supportedTypes.join(","))}static isHandlingMimeForPasting(t){return this.supportedTypes.includes(t)}static paste(t,e){e.pasteEditor(f.STAMP,{bitmapFile:t.getAsFile()})}altTextFinish(){this._uiManager.useNewAltTextFlow&&(this.div.hidden=!1);super.altTextFinish()}get telemetryFinalData(){return{type:"stamp",hasAltText:!!this.altTextData?.altText}}static computeTelemetryFinalData(t){const e=t.get("hasAltText");return{hasAltText:e.get(!0)??0,hasNoAltText:e.get(!1)??0}}#Qa(t,e=!1){if(t){this.#za=t.bitmap;if(!e){this.#ja=t.id;this.#Ya=t.isSvg}t.file&&(this.#Wa=t.file.name);this.#Ja()}else this.remove()}#Za(){this.#Ga=null;this._uiManager.enableWaiting(!1);if(this.#qa)if(this._uiManager.useNewAltTextWhenAddingImage&&this._uiManager.useNewAltTextFlow&&this.#za){this._editToolbar.hide();this._uiManager.editAltText(this,!0)}else{if(!this._uiManager.useNewAltTextWhenAddingImage&&this._uiManager.useNewAltTextFlow&&this.#za){this._reportTelemetry({action:"pdfjs.image.image_added",data:{alt_text_modal:!1,alt_text_type:"empty"}});try{this.mlGuessAltText()}catch{}}this.div.focus()}}async mlGuessAltText(t=null,e=!0){if(this.hasAltTextData())return null;const{mlManager:i}=this._uiManager;if(!i)throw new Error("No ML.");if(!await i.isEnabledFor("altText"))throw new Error("ML isn't enabled for alt text.");const{data:s,width:n,height:r}=t||this.copyCanvas(null,null,!0).imageData,a=await i.guess({name:"altText",request:{data:s,width:n,height:r,channels:s.length/(n*r)}});if(!a)throw new Error("No response from the AI service.");if(a.error)throw new Error("Error from the AI service.");if(a.cancel)return null;if(!a.output)throw new Error("No valid response from the AI service.");const o=a.output;await this.setGuessedAltText(o);e&&!this.hasAltTextData()&&(this.altTextData={alt:o,decorative:!1});return o}#to(){if(this.#ja){this._uiManager.enableWaiting(!0);this._uiManager.imageManager.getFromId(this.#ja).then((t=>this.#Qa(t,!0))).finally((()=>this.#Za()));return}if(this.#Va){const t=this.#Va;this.#Va=null;this._uiManager.enableWaiting(!0);this.#Ga=this._uiManager.imageManager.getFromUrl(t).then((t=>this.#Qa(t))).finally((()=>this.#Za()));return}if(this.#$a){const t=this.#$a;this.#$a=null;this._uiManager.enableWaiting(!0);this.#Ga=this._uiManager.imageManager.getFromFile(t).then((t=>this.#Qa(t))).finally((()=>this.#Za()));return}const t=document.createElement("input");t.type="file";t.accept=StampEditor.supportedTypesStr;const e=this._uiManager._signal;this.#Ga=new Promise((i=>{t.addEventListener("change",(async()=>{if(t.files&&0!==t.files.length){this._uiManager.enableWaiting(!0);const e=await this._uiManager.imageManager.getFromFile(t.files[0]);this._reportTelemetry({action:"pdfjs.image.image_selected",data:{alt_text_modal:this._uiManager.useNewAltTextFlow}});this.#Qa(e)}else this.remove();i()}),{signal:e});t.addEventListener("cancel",(()=>{this.remove();i()}),{signal:e})})).finally((()=>this.#Za()));t.click()}remove(){if(this.#ja){this.#za=null;this._uiManager.imageManager.deleteId(this.#ja);this.#qa?.remove();this.#qa=null;if(this.#Xa){clearTimeout(this.#Xa);this.#Xa=null}}super.remove()}rebuild(){if(this.parent){super.rebuild();if(null!==this.div){this.#ja&&null===this.#qa&&this.#to();this.isAttachedToDOM||this.parent.add(this)}}else this.#ja&&this.#to()}onceAdded(t){this._isDraggable=!0;t&&this.div.focus()}isEmpty(){return!(this.#Ga||this.#za||this.#Va||this.#$a||this.#ja)}get isResizable(){return!0}render(){if(this.div)return this.div;let t,e;if(this.width){t=this.x;e=this.y}super.render();this.div.hidden=!0;this.div.setAttribute("role","figure");this.addAltTextButton();this.#za?this.#Ja():this.#to();if(this.width&&!this.annotationElementId){const[i,s]=this.parentDimensions;this.setAt(t*i,e*s,this.width*i,this.height*s)}this._uiManager.addShouldRescale(this);return this.div}_onResized(){this.onScaleChanging()}onScaleChanging(){if(!this.parent)return;null!==this.#Xa&&clearTimeout(this.#Xa);this.#Xa=setTimeout((()=>{this.#Xa=null;this.#eo()}),200)}#Ja(){const{div:t}=this;let{width:e,height:i}=this.#za;const[s,n]=this.pageDimensions,r=.75;if(this.width){e=this.width*s;i=this.height*n}else if(e>r*s||i>r*n){const t=Math.min(r*s/e,r*n/i);e*=t;i*=t}const[a,o]=this.parentDimensions;this.setDims(e*a/s,i*o/n);this._uiManager.enableWaiting(!1);const l=this.#qa=document.createElement("canvas");l.setAttribute("role","img");this.addContainer(l);this.width=e/s;this.height=i/n;this._initialOptions?.isCentered?this.center():this.fixAndSetPosition();this._initialOptions=null;this._uiManager.useNewAltTextWhenAddingImage&&this._uiManager.useNewAltTextFlow&&!this.annotationElementId||(t.hidden=!1);this.#eo();if(!this.#Ka){this.parent.addUndoableEditor(this);this.#Ka=!0}this._reportTelemetry({action:"inserted_image"});this.#Wa&&l.setAttribute("aria-label",this.#Wa)}copyCanvas(t,e,i=!1){t||(t=224);const{width:s,height:n}=this.#za,r=new OutputScale;let a=this.#za,o=s,l=n,h=null;if(e){if(s>e||n>e){const t=Math.min(e/s,e/n);o=Math.floor(s*t);l=Math.floor(n*t)}h=document.createElement("canvas");const t=h.width=Math.ceil(o*r.sx),i=h.height=Math.ceil(l*r.sy);this.#Ya||(a=this.#io(t,i));const c=h.getContext("2d");c.filter=this._uiManager.hcmFilter;let d="white",u="#cfcfd8";if("none"!==this._uiManager.hcmFilter)u="black";else if(window.matchMedia?.("(prefers-color-scheme: dark)").matches){d="#8f8f9d";u="#42414d"}const p=15,g=p*r.sx,f=p*r.sy,m=new OffscreenCanvas(2*g,2*f),b=m.getContext("2d");b.fillStyle=d;b.fillRect(0,0,2*g,2*f);b.fillStyle=u;b.fillRect(0,0,g,f);b.fillRect(g,f,g,f);c.fillStyle=c.createPattern(m,"repeat");c.fillRect(0,0,t,i);c.drawImage(a,0,0,a.width,a.height,0,0,t,i)}let c=null;if(i){let e,i;if(r.symmetric&&a.width<t&&a.height<t){e=a.width;i=a.height}else{a=this.#za;if(s>t||n>t){const r=Math.min(t/s,t/n);e=Math.floor(s*r);i=Math.floor(n*r);this.#Ya||(a=this.#io(e,i))}}const o=new OffscreenCanvas(e,i).getContext("2d",{willReadFrequently:!0});o.drawImage(a,0,0,a.width,a.height,0,0,e,i);c={width:e,height:i,data:o.getImageData(0,0,e,i).data}}return{canvas:h,width:o,height:l,imageData:c}}#io(t,e){const{width:i,height:s}=this.#za;let n=i,r=s,a=this.#za;for(;n>2*t||r>2*e;){const i=n,s=r;n>2*t&&(n=n>=16384?Math.floor(n/2)-1:Math.ceil(n/2));r>2*e&&(r=r>=16384?Math.floor(r/2)-1:Math.ceil(r/2));const o=new OffscreenCanvas(n,r);o.getContext("2d").drawImage(a,0,0,i,s,0,0,n,r);a=o.transferToImageBitmap()}return a}#eo(){const[t,e]=this.parentDimensions,{width:i,height:s}=this,n=new OutputScale,r=Math.ceil(i*t*n.sx),a=Math.ceil(s*e*n.sy),o=this.#qa;if(!o||o.width===r&&o.height===a)return;o.width=r;o.height=a;const l=this.#Ya?this.#za:this.#io(r,a),h=o.getContext("2d");h.filter=this._uiManager.hcmFilter;h.drawImage(l,0,0,l.width,l.height,0,0,r,a)}getImageForAltText(){return this.#qa}#so(t){if(t){if(this.#Ya){const t=this._uiManager.imageManager.getSvgUrl(this.#ja);if(t)return t}const t=document.createElement("canvas");({width:t.width,height:t.height}=this.#za);t.getContext("2d").drawImage(this.#za,0,0);return t.toDataURL()}if(this.#Ya){const[t,e]=this.pageDimensions,i=Math.round(this.width*t*PixelsPerInch.PDF_TO_CSS_UNITS),s=Math.round(this.height*e*PixelsPerInch.PDF_TO_CSS_UNITS),n=new OffscreenCanvas(i,s);n.getContext("2d").drawImage(this.#za,0,0,this.#za.width,this.#za.height,0,0,i,s);return n.transferToImageBitmap()}return structuredClone(this.#za)}static async deserialize(t,e,i){let s=null;if(t instanceof StampAnnotationElement){const{data:{rect:n,rotation:r,id:a,structParent:o,popupRef:l},container:h,parent:{page:{pageNumber:c}}}=t,d=h.querySelector("canvas"),u=i.imageManager.getFromCanvas(h.id,d);d.remove();const p=(await e._structTree.getAriaAttributes(`${st}${a}`))?.get("aria-label")||"";s=t={annotationType:f.STAMP,bitmapId:u.id,bitmap:u.bitmap,pageIndex:c-1,rect:n.slice(0),rotation:r,id:a,deleted:!1,accessibilityData:{decorative:!1,altText:p},isSvg:!1,structParent:o,popupRef:l}}const n=await super.deserialize(t,e,i),{rect:r,bitmap:a,bitmapUrl:o,bitmapId:l,isSvg:h,accessibilityData:c}=t;if(l&&i.imageManager.isValidId(l)){n.#ja=l;a&&(n.#za=a)}else n.#Va=o;n.#Ya=h;const[d,u]=n.pageDimensions;n.width=(r[2]-r[0])/d;n.height=(r[3]-r[1])/u;n.annotationElementId=t.id||null;c&&(n.altTextData=c);n._initialData=s;n.#Ka=!!s;return n}serialize(t=!1,e=null){if(this.isEmpty())return null;if(this.deleted)return this.serializeDeleted();const i={annotationType:f.STAMP,bitmapId:this.#ja,pageIndex:this.pageIndex,rect:this.getRect(0,0),rotation:this.rotation,isSvg:this.#Ya,structTreeParentId:this._structTreeParentId};if(t){i.bitmapUrl=this.#so(!0);i.accessibilityData=this.serializeAltText(!0);return i}const{decorative:s,altText:n}=this.serializeAltText(!1);!s&&n&&(i.accessibilityData={type:"Figure",alt:n});if(this.annotationElementId){const t=this.#Gn(i);if(t.isSame)return null;t.isSameAltText?delete i.accessibilityData:i.accessibilityData.structParent=this._initialData.structParent??-1}i.id=this.annotationElementId;if(null===e)return i;e.stamps||=new Map;const r=this.#Ya?(i.rect[2]-i.rect[0])*(i.rect[3]-i.rect[1]):null;if(e.stamps.has(this.#ja)){if(this.#Ya){const t=e.stamps.get(this.#ja);if(r>t.area){t.area=r;t.serialized.bitmap.close();t.serialized.bitmap=this.#so(!1)}}}else{e.stamps.set(this.#ja,{area:r,serialized:i});i.bitmap=this.#so(!1)}return i}#Gn(t){const{pageIndex:e,accessibilityData:{altText:i}}=this._initialData,s=t.pageIndex===e,n=(t.accessibilityData?.alt||"")===i;return{isSame:!this._hasBeenMoved&&!this._hasBeenResized&&s&&n,isSameAltText:n}}renderAnnotationElement(t){t.updateEdited({rect:this.getRect(0,0)});return null}}class AnnotationEditorLayer{#Cn;#no=!1;#ro=null;#ao=null;#oo=null;#lo=new Map;#ho=!1;#co=!1;#do=!1;#uo=null;#po=null;#go=null;#fo=null;#f;static _initialized=!1;static#z=new Map([FreeTextEditor,InkEditor,StampEditor,HighlightEditor].map((t=>[t._editorType,t])));constructor({uiManager:t,pageIndex:e,div:i,structTreeLayer:s,accessibilityManager:n,annotationLayer:r,drawLayer:a,textLayer:o,viewport:l,l10n:h}){const c=[...AnnotationEditorLayer.#z.values()];if(!AnnotationEditorLayer._initialized){AnnotationEditorLayer._initialized=!0;for(const e of c)e.initialize(h,t)}t.registerEditorTypes(c);this.#f=t;this.pageIndex=e;this.div=i;this.#Cn=n;this.#ro=r;this.viewport=l;this.#go=o;this.drawLayer=a;this._structTree=s;this.#f.addLayer(this)}get isEmpty(){return 0===this.#lo.size}get isInvisible(){return this.isEmpty&&this.#f.getMode()===f.NONE}updateToolbar(t){this.#f.updateToolbar(t)}updateMode(t=this.#f.getMode()){this.#mo();switch(t){case f.NONE:this.disableTextSelection();this.togglePointerEvents(!1);this.toggleAnnotationLayerPointerEvents(!0);this.disableClick();return;case f.INK:this.disableTextSelection();this.togglePointerEvents(!0);this.enableClick();break;case f.HIGHLIGHT:this.enableTextSelection();this.togglePointerEvents(!1);this.disableClick();break;default:this.disableTextSelection();this.togglePointerEvents(!0);this.enableClick()}this.toggleAnnotationLayerPointerEvents(!1);const{classList:e}=this.div;for(const i of AnnotationEditorLayer.#z.values())e.toggle(`${i._type}Editing`,t===i._editorType);this.div.hidden=!1}hasTextLayer(t){return t===this.#go?.div}setEditingState(t){this.#f.setEditingState(t)}addCommands(t){this.#f.addCommands(t)}cleanUndoStack(t){this.#f.cleanUndoStack(t)}toggleDrawing(t=!1){this.div.classList.toggle("drawing",!t)}togglePointerEvents(t=!1){this.div.classList.toggle("disabled",!t)}toggleAnnotationLayerPointerEvents(t=!1){this.#ro?.div.classList.toggle("disabled",!t)}async enable(){this.#do=!0;this.div.tabIndex=0;this.togglePointerEvents(!0);const t=new Set;for(const e of this.#lo.values()){e.enableEditing();e.show(!0);if(e.annotationElementId){this.#f.removeChangedExistingAnnotation(e);t.add(e.annotationElementId)}}if(!this.#ro){this.#do=!1;return}const e=this.#ro.getEditableAnnotations();for(const i of e){i.hide();if(this.#f.isDeletedAnnotationElement(i.data.id))continue;if(t.has(i.data.id))continue;const e=await this.deserialize(i);if(e){this.addOrRebuild(e);e.enableEditing()}}this.#do=!1}disable(){this.#co=!0;this.div.tabIndex=-1;this.togglePointerEvents(!1);const t=new Map,e=new Map;for(const i of this.#lo.values()){i.disableEditing();if(i.annotationElementId)if(null===i.serialize()){e.set(i.annotationElementId,i);this.getEditableAnnotation(i.annotationElementId)?.show();i.remove()}else t.set(i.annotationElementId,i)}if(this.#ro){const i=this.#ro.getEditableAnnotations();for(const s of i){const{id:i}=s.data;if(this.#f.isDeletedAnnotationElement(i))continue;let n=e.get(i);if(n){n.resetAnnotationElement(s);n.show(!1);s.show()}else{n=t.get(i);if(n){this.#f.addChangedExistingAnnotation(n);n.renderAnnotationElement(s)&&n.show(!1)}s.show()}}}this.#mo();this.isEmpty&&(this.div.hidden=!0);const{classList:i}=this.div;for(const t of AnnotationEditorLayer.#z.values())i.remove(`${t._type}Editing`);this.disableTextSelection();this.toggleAnnotationLayerPointerEvents(!0);this.#co=!1}getEditableAnnotation(t){return this.#ro?.getEditableAnnotation(t)||null}setActiveEditor(t){this.#f.getActive()!==t&&this.#f.setActiveEditor(t)}enableTextSelection(){this.div.tabIndex=-1;if(this.#go?.div&&!this.#fo){this.#fo=new AbortController;const t=this.#f.combinedSignal(this.#fo);this.#go.div.addEventListener("pointerdown",this.#bo.bind(this),{signal:t});this.#go.div.classList.add("highlighting")}}disableTextSelection(){this.div.tabIndex=0;if(this.#go?.div&&this.#fo){this.#fo.abort();this.#fo=null;this.#go.div.classList.remove("highlighting")}}#bo(t){this.#f.unselectAll();const{target:e}=t;if(e===this.#go.div||("img"===e.getAttribute("role")||e.classList.contains("endOfContent"))&&this.#go.div.contains(e)){const{isMac:e}=util_FeatureTest.platform;if(0!==t.button||t.ctrlKey&&e)return;this.#f.showAllEditors("highlight",!0,!0);this.#go.div.classList.add("free");this.toggleDrawing();HighlightEditor.startHighlighting(this,"ltr"===this.#f.direction,{target:this.#go.div,x:t.x,y:t.y});this.#go.div.addEventListener("pointerup",(()=>{this.#go.div.classList.remove("free");this.toggleDrawing(!0)}),{once:!0,signal:this.#f._signal});t.preventDefault()}}enableClick(){if(this.#ao)return;this.#ao=new AbortController;const t=this.#f.combinedSignal(this.#ao);this.div.addEventListener("pointerdown",this.pointerdown.bind(this),{signal:t});const e=this.pointerup.bind(this);this.div.addEventListener("pointerup",e,{signal:t});this.div.addEventListener("pointercancel",e,{signal:t})}disableClick(){this.#ao?.abort();this.#ao=null}attach(t){this.#lo.set(t.id,t);const{annotationElementId:e}=t;e&&this.#f.isDeletedAnnotationElement(e)&&this.#f.removeDeletedAnnotationElement(t)}detach(t){this.#lo.delete(t.id);this.#Cn?.removePointerInTextLayer(t.contentDiv);!this.#co&&t.annotationElementId&&this.#f.addDeletedAnnotationElement(t)}remove(t){this.detach(t);this.#f.removeEditor(t);t.div.remove();t.isAttachedToDOM=!1}changeParent(t){if(t.parent!==this){if(t.parent&&t.annotationElementId){this.#f.addDeletedAnnotationElement(t.annotationElementId);AnnotationEditor.deleteAnnotationElement(t);t.annotationElementId=null}this.attach(t);t.parent?.detach(t);t.setParent(this);if(t.div&&t.isAttachedToDOM){t.div.remove();this.div.append(t.div)}}}add(t){if(t.parent!==this||!t.isAttachedToDOM){this.changeParent(t);this.#f.addEditor(t);this.attach(t);if(!t.isAttachedToDOM){const e=t.render();this.div.append(e);t.isAttachedToDOM=!0}t.fixAndSetPosition();t.onceAdded(!this.#do);this.#f.addToAnnotationStorage(t);t._reportTelemetry(t.telemetryInitialData)}}moveEditorInDOM(t){if(!t.isAttachedToDOM)return;const{activeElement:e}=document;if(t.div.contains(e)&&!this.#oo){t._focusEventsAllowed=!1;this.#oo=setTimeout((()=>{this.#oo=null;if(t.div.contains(document.activeElement))t._focusEventsAllowed=!0;else{t.div.addEventListener("focusin",(()=>{t._focusEventsAllowed=!0}),{once:!0,signal:this.#f._signal});e.focus()}}),0)}t._structTreeParentId=this.#Cn?.moveElementInDOM(this.div,t.div,t.contentDiv,!0)}addOrRebuild(t){if(t.needsToBeRebuilt()){t.parent||=this;t.rebuild();t.show()}else this.add(t)}addUndoableEditor(t){this.addCommands({cmd:()=>t._uiManager.rebuild(t),undo:()=>{t.remove()},mustExec:!1})}getNextId(){return this.#f.getId()}get#vo(){return AnnotationEditorLayer.#z.get(this.#f.getMode())}combinedSignal(t){return this.#f.combinedSignal(t)}#yo(t){const e=this.#vo;return e?new e.prototype.constructor(t):null}canCreateNewEmptyEditor(){return this.#vo?.canCreateNewEmptyEditor()}pasteEditor(t,e){this.#f.updateToolbar(t);this.#f.updateMode(t);const{offsetX:i,offsetY:s}=this.#wo(),n=this.getNextId(),r=this.#yo({parent:this,id:n,x:i,y:s,uiManager:this.#f,isCentered:!0,...e});r&&this.add(r)}async deserialize(t){return await(AnnotationEditorLayer.#z.get(t.annotationType??t.annotationEditorType)?.deserialize(t,this,this.#f))||null}createAndAddNewEditor(t,e,i={}){const s=this.getNextId(),n=this.#yo({parent:this,id:s,x:t.offsetX,y:t.offsetY,uiManager:this.#f,isCentered:e,...i});n&&this.add(n);return n}#wo(){const{x:t,y:e,width:i,height:s}=this.div.getBoundingClientRect(),n=Math.max(0,t),r=Math.max(0,e),a=(n+Math.min(window.innerWidth,t+i))/2-t,o=(r+Math.min(window.innerHeight,e+s))/2-e,[l,h]=this.viewport.rotation%180==0?[a,o]:[o,a];return{offsetX:l,offsetY:h}}addNewEditor(){this.createAndAddNewEditor(this.#wo(),!0)}setSelected(t){this.#f.setSelected(t)}toggleSelected(t){this.#f.toggleSelected(t)}unselect(t){this.#f.unselect(t)}pointerup(t){const{isMac:e}=util_FeatureTest.platform;if(!(0!==t.button||t.ctrlKey&&e)&&t.target===this.div&&this.#ho){this.#ho=!1;this.#vo?.isDrawer&&this.#vo.supportMultipleDrawings||(this.#no?this.#f.getMode()!==f.STAMP?this.createAndAddNewEditor(t,!1):this.#f.unselectAll():this.#no=!0)}}pointerdown(t){this.#f.getMode()===f.HIGHLIGHT&&this.enableTextSelection();if(this.#ho){this.#ho=!1;return}const{isMac:e}=util_FeatureTest.platform;if(0!==t.button||t.ctrlKey&&e)return;if(t.target!==this.div)return;this.#ho=!0;if(this.#vo?.isDrawer){this.startDrawingSession(t);return}const i=this.#f.getActive();this.#no=!i||i.isEmpty()}startDrawingSession(t){this.div.focus();if(this.#uo){this.#vo.startDrawing(this,this.#f,!1,t);return}this.#f.setCurrentDrawingSession(this);this.#uo=new AbortController;const e=this.#f.combinedSignal(this.#uo);this.div.addEventListener("blur",(({relatedTarget:t})=>{if(t&&!this.div.contains(t)){this.#po=null;this.commitOrRemove()}}),{signal:e});this.#vo.startDrawing(this,this.#f,!1,t)}pause(t){if(t){const{activeElement:t}=document;this.div.contains(t)&&(this.#po=t)}else this.#po&&setTimeout((()=>{this.#po?.focus();this.#po=null}),0)}endDrawingSession(t=!1){if(!this.#uo)return null;this.#f.setCurrentDrawingSession(null);this.#uo.abort();this.#uo=null;this.#po=null;return this.#vo.endDrawing(t)}findNewParent(t,e,i){const s=this.#f.findParent(e,i);if(null===s||s===this)return!1;s.changeParent(t);return!0}commitOrRemove(){if(this.#uo){this.endDrawingSession();return!0}return!1}onScaleChanging(){this.#uo&&this.#vo.onScaleChangingWhenDrawing(this)}destroy(){this.commitOrRemove();if(this.#f.getActive()?.parent===this){this.#f.commitOrRemove();this.#f.setActiveEditor(null)}if(this.#oo){clearTimeout(this.#oo);this.#oo=null}for(const t of this.#lo.values()){this.#Cn?.removePointerInTextLayer(t.contentDiv);t.setParent(null);t.isAttachedToDOM=!1;t.div.remove()}this.div=null;this.#lo.clear();this.#f.removeLayer(this)}#mo(){for(const t of this.#lo.values())t.isEmpty()&&t.remove()}render({viewport:t}){this.viewport=t;setLayerDimensions(this.div,t);for(const t of this.#f.getEditors(this.pageIndex)){this.add(t);t.rebuild()}this.updateMode()}update({viewport:t}){this.#f.commitOrRemove();this.#mo();const e=this.viewport.rotation,i=t.rotation;this.viewport=t;setLayerDimensions(this.div,{rotation:i});if(e!==i)for(const t of this.#lo.values())t.rotate(i)}get pageDimensions(){const{pageWidth:t,pageHeight:e}=this.viewport.rawDims;return[t,e]}get scale(){return this.#f.viewParameters.realScale}}class DrawLayer{#nn=null;#y=0;#Ao=new Map;#xo=new Map;constructor({pageIndex:t}){this.pageIndex=t}setParent(t){if(this.#nn){if(this.#nn!==t){if(this.#Ao.size>0)for(const e of this.#Ao.values()){e.remove();t.append(e)}this.#nn=t}}else this.#nn=t}static get _svgFactory(){return shadow(this,"_svgFactory",new DOMSVGFactory)}static#_o(t,[e,i,s,n]){const{style:r}=t;r.top=100*i+"%";r.left=100*e+"%";r.width=100*s+"%";r.height=100*n+"%"}#Eo(){const t=DrawLayer._svgFactory.create(1,1,!0);this.#nn.append(t);t.setAttribute("aria-hidden",!0);return t}#So(t,e){const i=DrawLayer._svgFactory.createElement("clipPath");t.append(i);const s=`clip_${e}`;i.setAttribute("id",s);i.setAttribute("clipPathUnits","objectBoundingBox");const n=DrawLayer._svgFactory.createElement("use");i.append(n);n.setAttribute("href",`#${e}`);n.classList.add("clip");return s}#Co(t,e){for(const[i,s]of Object.entries(e))null===s?t.removeAttribute(i):t.setAttribute(i,s)}draw(t,e=!1,i=!1){const s=this.#y++,n=this.#Eo(),r=DrawLayer._svgFactory.createElement("defs");n.append(r);const a=DrawLayer._svgFactory.createElement("path");r.append(a);const o=`path_p${this.pageIndex}_${s}`;a.setAttribute("id",o);a.setAttribute("vector-effect","non-scaling-stroke");e&&this.#xo.set(s,a);const l=i?this.#So(r,o):null,h=DrawLayer._svgFactory.createElement("use");n.append(h);h.setAttribute("href",`#${o}`);this.updateProperties(n,t);this.#Ao.set(s,n);return{id:s,clipPathId:`url(#${l})`}}drawOutline(t,e){const i=this.#y++,s=this.#Eo(),n=DrawLayer._svgFactory.createElement("defs");s.append(n);const r=DrawLayer._svgFactory.createElement("path");n.append(r);const a=`path_p${this.pageIndex}_${i}`;r.setAttribute("id",a);r.setAttribute("vector-effect","non-scaling-stroke");let o;if(e){const t=DrawLayer._svgFactory.createElement("mask");n.append(t);o=`mask_p${this.pageIndex}_${i}`;t.setAttribute("id",o);t.setAttribute("maskUnits","objectBoundingBox");const e=DrawLayer._svgFactory.createElement("rect");t.append(e);e.setAttribute("width","1");e.setAttribute("height","1");e.setAttribute("fill","white");const s=DrawLayer._svgFactory.createElement("use");t.append(s);s.setAttribute("href",`#${a}`);s.setAttribute("stroke","none");s.setAttribute("fill","black");s.setAttribute("fill-rule","nonzero");s.classList.add("mask")}const l=DrawLayer._svgFactory.createElement("use");s.append(l);l.setAttribute("href",`#${a}`);o&&l.setAttribute("mask",`url(#${o})`);const h=l.cloneNode();s.append(h);l.classList.add("mainOutline");h.classList.add("secondaryOutline");this.updateProperties(s,t);this.#Ao.set(i,s);return i}finalizeDraw(t,e){this.#xo.delete(t);this.updateProperties(t,e)}updateProperties(t,e){if(!e)return;const{root:i,bbox:s,rootClass:n,path:r}=e,a="number"==typeof t?this.#Ao.get(t):t;if(a){i&&this.#Co(a,i);s&&DrawLayer.#_o(a,s);if(n){const{classList:t}=a;for(const[e,i]of Object.entries(n))t.toggle(e,i)}if(r){const t=a.firstChild.firstChild;this.#Co(t,r)}}}updateParent(t,e){if(e===this)return;const i=this.#Ao.get(t);if(i){e.#nn.append(i);this.#Ao.delete(t);e.#Ao.set(t,i)}}remove(t){this.#xo.delete(t);if(null!==this.#nn){this.#Ao.get(t).remove();this.#Ao.delete(t)}}destroy(){this.#nn=null;for(const t of this.#Ao.values())t.remove();this.#Ao.clear();this.#xo.clear()}}globalThis.pdfjsTestingUtils={HighlightOutliner};var Gt=__webpack_exports__.AbortException,Vt=__webpack_exports__.AnnotationEditorLayer,$t=__webpack_exports__.AnnotationEditorParamsType,Wt=__webpack_exports__.AnnotationEditorType,qt=__webpack_exports__.AnnotationEditorUIManager,Xt=__webpack_exports__.AnnotationLayer,Yt=__webpack_exports__.AnnotationMode,Kt=__webpack_exports__.ColorPicker,Qt=__webpack_exports__.DOMSVGFactory,Jt=__webpack_exports__.DrawLayer,Zt=__webpack_exports__.FeatureTest,te=__webpack_exports__.GlobalWorkerOptions,ee=__webpack_exports__.ImageKind,ie=__webpack_exports__.InvalidPDFException,se=__webpack_exports__.MissingPDFException,ne=__webpack_exports__.OPS,re=__webpack_exports__.OutputScale,ae=__webpack_exports__.PDFDataRangeTransport,oe=__webpack_exports__.PDFDateString,le=__webpack_exports__.PDFWorker,he=__webpack_exports__.PasswordResponses,ce=__webpack_exports__.PermissionFlag,de=__webpack_exports__.PixelsPerInch,ue=__webpack_exports__.RenderingCancelledException,pe=__webpack_exports__.TextLayer,ge=__webpack_exports__.TouchManager,fe=__webpack_exports__.UnexpectedResponseException,me=__webpack_exports__.Util,be=__webpack_exports__.VerbosityLevel,ve=__webpack_exports__.XfaLayer,ye=__webpack_exports__.build,we=__webpack_exports__.createValidAbsoluteUrl,Ae=__webpack_exports__.fetchData,xe=__webpack_exports__.getDocument,_e=__webpack_exports__.getFilenameFromUrl,Ee=__webpack_exports__.getPdfFilenameFromUrl,Se=__webpack_exports__.getXfaPageViewport,Ce=__webpack_exports__.isDataScheme,Te=__webpack_exports__.isPdfFile,Me=__webpack_exports__.noContextMenu,Pe=__webpack_exports__.normalizeUnicode,De=__webpack_exports__.setLayerDimensions,ke=__webpack_exports__.shadow,Re=__webpack_exports__.stopEvent,Ie=__webpack_exports__.version;export{Gt as AbortException,Vt as AnnotationEditorLayer,$t as AnnotationEditorParamsType,Wt as AnnotationEditorType,qt as AnnotationEditorUIManager,Xt as AnnotationLayer,Yt as AnnotationMode,Kt as ColorPicker,Qt as DOMSVGFactory,Jt as DrawLayer,Zt as FeatureTest,te as GlobalWorkerOptions,ee as ImageKind,ie as InvalidPDFException,se as MissingPDFException,ne as OPS,re as OutputScale,ae as PDFDataRangeTransport,oe as PDFDateString,le as PDFWorker,he as PasswordResponses,ce as PermissionFlag,de as PixelsPerInch,ue as RenderingCancelledException,pe as TextLayer,ge as TouchManager,fe as UnexpectedResponseException,me as Util,be as VerbosityLevel,ve as XfaLayer,ye as build,we as createValidAbsoluteUrl,Ae as fetchData,xe as getDocument,_e as getFilenameFromUrl,Ee as getPdfFilenameFromUrl,Se as getXfaPageViewport,Ce as isDataScheme,Te as isPdfFile,Me as noContextMenu,Pe as normalizeUnicode,De as setLayerDimensions,ke as shadow,Re as stopEvent,Ie as version};