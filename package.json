{"name": "invincible-saas", "version": "1.0.0", "description": "Invincible - The Ultimate Content Writing SaaS Platform", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:push": "prisma db push", "db:studio": "prisma studio", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev"}, "dependencies": {"@google-ai/generativelanguage": "^2.5.0", "@google/generative-ai": "^0.24.1", "@kaibanjs/tools": "^0.7.0", "@langchain/community": "^0.3.46", "@langchain/core": "^0.3.58", "@prisma/client": "^5.12.1", "@tailwindcss/typography": "^0.5.16", "@tiptap/extension-character-count": "^2.14.0", "@tiptap/extension-highlight": "^2.14.0", "@tiptap/extension-link": "^2.14.0", "@tiptap/extension-placeholder": "^2.14.0", "@tiptap/pm": "^2.14.0", "@tiptap/react": "^2.14.0", "@tiptap/starter-kit": "^2.14.0", "@types/marked": "^5.0.2", "@types/node": "^20", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.0", "autoprefixer": "^10.0.1", "axios": "^1.6.8", "cheerio": "^1.0.0-rc.12", "eslint": "^8", "eslint-config-next": "15.3.3", "framer-motion": "^11.0.28", "google-auth-library": "^9.7.0", "googleapis": "^134.0.0", "kaibanjs": "^0.21.1", "langchain": "^0.3.28", "lucide-react": "^0.515.0", "marked": "^15.0.12", "next": "15.3.3", "openai": "^4.38.5", "postcss": "^8", "prisma": "^5.12.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-markdown": "^9.0.1", "recharts": "^2.15.3", "tailwindcss": "^3.3.0", "typescript": "^5", "uuid": "^9.0.1", "zod": "^3.22.4"}, "devDependencies": {"@types/cheerio": "^0.22.35", "@types/uuid": "^9.0.8", "prisma": "^5.12.1"}, "keywords": ["content writing", "seo", "ai", "saas", "content optimization", "article generator"], "author": "Invincible Team", "license": "MIT"}