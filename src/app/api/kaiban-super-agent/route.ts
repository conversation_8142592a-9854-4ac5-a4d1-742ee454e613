/**
 * KaibanJS Super Agent API Endpoint
 * 
 * This API endpoint handles requests for the KaibanJS super agent workflow.
 * It processes content generation requests using the 7-phase workflow with
 * Gemini and Qwen models, integrated with Tavily search.
 */

import { NextRequest, NextResponse } from 'next/server';
import { KaibanSuperAgent, SuperAgentOptions, SuperAgentResult } from '@/lib/agents/kaiban-super-agent';

// API logging utility
const logAPI = (message: string, data?: any) => {
  const timestamp = new Date().toISOString();
  console.log(`🌐 [KAIBAN-API] ${timestamp}: ${message}`);
  if (data) {
    console.log(`📝 [KAIBAN-API-DATA]:`, JSON.stringify(data, null, 2));
  }
};

/**
 * Request body interface
 */
interface RequestBody {
  topic: string;
  contentType?: string;
  targetWordCount?: number;
  tone?: string;
  targetAudience?: string;
  maxPrimaryResults?: number;
  maxDeepResults?: number;
  enableFactChecking?: boolean;
  includeSourceCitations?: boolean;
}

/**
 * Response interface
 */
interface APIResponse {
  success: boolean;
  data?: SuperAgentResult;
  error?: string;
  timestamp: string;
}

/**
 * POST handler for KaibanJS Super Agent workflow
 */
export async function POST(request: NextRequest): Promise<NextResponse<APIResponse>> {
  const startTime = Date.now();
  
  try {
    logAPI('Received KaibanJS Super Agent request');

    // Parse request body
    const body: RequestBody = await request.json();
    
    // Validate required fields
    if (!body.topic || typeof body.topic !== 'string' || body.topic.trim().length === 0) {
      logAPI('Invalid request: missing or empty topic');
      return NextResponse.json({
        success: false,
        error: 'Topic is required and must be a non-empty string',
        timestamp: new Date().toISOString()
      }, { status: 400 });
    }

    // Validate environment variables
    if (!process.env.GEMINI_API_KEY) {
      logAPI('Configuration error: GEMINI_API_KEY not set');
      return NextResponse.json({
        success: false,
        error: 'Gemini API key not configured',
        timestamp: new Date().toISOString()
      }, { status: 500 });
    }

    if (!process.env.OPENROUTER_API_KEY) {
      logAPI('Configuration error: OPENROUTER_API_KEY not set');
      return NextResponse.json({
        success: false,
        error: 'OpenRouter API key not configured',
        timestamp: new Date().toISOString()
      }, { status: 500 });
    }

    if (!process.env.TAVILY_API_KEY) {
      logAPI('Configuration error: TAVILY_API_KEY not set');
      return NextResponse.json({
        success: false,
        error: 'Tavily API key not configured',
        timestamp: new Date().toISOString()
      }, { status: 500 });
    }

    // Prepare options
    const options: SuperAgentOptions = {
      topic: body.topic.trim(),
      contentType: (body.contentType as any) || 'article',
      targetWordCount: body.targetWordCount || 2000,
      tone: (body.tone as any) || 'professional',
      targetAudience: (body.targetAudience as any) || 'intermediate',
      maxPrimaryResults: body.maxPrimaryResults || 6,
      maxDeepResults: body.maxDeepResults || 4,
      enableFactChecking: body.enableFactChecking ?? true,
      includeSourceCitations: body.includeSourceCitations ?? true,
    };

    logAPI('Starting KaibanJS Super Agent workflow', {
      topic: options.topic,
      contentType: options.contentType,
      targetWordCount: options.targetWordCount,
      targetAudience: options.targetAudience
    });

    // Create progress callback for logging
    const onProgress = (phase: string, progress: number, message: string) => {
      logAPI(`Progress: ${phase} (${progress}%)`, { message });
    };

    // Initialize and execute the super agent
    const superAgent = new KaibanSuperAgent(onProgress);
    const result = await superAgent.execute(options);

    const totalTime = Date.now() - startTime;

    if (result.success) {
      logAPI('KaibanJS Super Agent workflow completed successfully', {
        executionTime: `${totalTime}ms`,
        wordCount: result.wordCount,
        sourcesUsed: result.sourcesUsed,
        qualityScore: result.qualityScore
      });

      return NextResponse.json({
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      });
    } else {
      logAPI('KaibanJS Super Agent workflow failed', {
        error: result.error,
        executionTime: `${totalTime}ms`
      });

      return NextResponse.json({
        success: false,
        error: result.error || 'Workflow execution failed',
        timestamp: new Date().toISOString()
      }, { status: 500 });
    }

  } catch (error) {
    const totalTime = Date.now() - startTime;
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    
    logAPI('KaibanJS Super Agent API error', {
      error: errorMessage,
      executionTime: `${totalTime}ms`,
      stack: error instanceof Error ? error.stack : undefined
    });

    return NextResponse.json({
      success: false,
      error: errorMessage,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

/**
 * GET handler for API status and configuration
 */
export async function GET(): Promise<NextResponse> {
  try {
    logAPI('Health check request received');

    const status = {
      service: 'KaibanJS Super Agent API',
      status: 'operational',
      version: '1.0.0',
      features: [
        '7-phase super agent workflow',
        'Gemini and Qwen model integration',
        'Tavily search integration',
        'Comprehensive content generation',
        'Quality assurance and fact-checking'
      ],
      configuration: {
        geminiConfigured: !!process.env.GEMINI_API_KEY,
        openrouterConfigured: !!process.env.OPENROUTER_API_KEY,
        tavilyConfigured: !!process.env.TAVILY_API_KEY,
        totalAgents: 7,
        totalTasks: 7,
        supportedContentTypes: [
          'article',
          'blog-post', 
          'research-paper',
          'comprehensive-guide',
          'case-study',
          'white-paper',
          'tutorial'
        ],
        supportedTones: [
          'professional',
          'casual',
          'academic',
          'conversational',
          'authoritative',
          'engaging',
          'technical'
        ],
        supportedAudiences: [
          'beginner',
          'intermediate',
          'expert',
          'general',
          'technical',
          'business'
        ]
      },
      timestamp: new Date().toISOString()
    };

    return NextResponse.json(status);

  } catch (error) {
    logAPI('Health check error', { error });
    
    return NextResponse.json({
      service: 'KaibanJS Super Agent API',
      status: 'error',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

/**
 * OPTIONS handler for CORS
 */
export async function OPTIONS(): Promise<NextResponse> {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
