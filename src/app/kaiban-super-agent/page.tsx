/**
 * KaibanJS Super Agent Page
 * 
 * This page provides the interface for the KaibanJS super agent workflow
 * with comprehensive content generation capabilities.
 */

import KaibanSuperAgent from '@/components/KaibanSuperAgent';

export default function KaibanSuperAgentPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <KaibanSuperAgent />
    </div>
  );
}

export const metadata = {
  title: 'KaibanJS Super Agent - Advanced Multi-Agent Content Generation',
  description: 'Generate high-quality content using KaibanJS framework with Gemini and Qwen models, powered by Tavily search for comprehensive research and analysis.',
};
