'use client';

import React, { useState, useRef } from 'react';
import { Sparkles, Play, Square, Download, Copy } from 'lucide-react';

/**
 * KaibanJS Super Agent UI Component
 *
 * Provides a user interface for the KaibanJS super agent workflow
 * with real-time progress tracking and result display.
 */

interface SuperAgentOptions {
  contentType: string;
  targetWordCount: number;
  tone: string;
  targetAudience: string;
  maxPrimaryResults: number;
  maxDeepResults: number;
  enableFactChecking: boolean;
  includeSourceCitations: boolean;
}

interface WorkflowProgress {
  phase: string;
  progress: number;
  message: string;
  timestamp: string;
}

interface WorkflowResult {
  success: boolean;
  content?: string;
  title?: string;
  wordCount?: number;
  sourcesUsed?: number;
  qualityScore?: number;
  executionTime?: number;
  error?: string;
  metadata?: {
    totalTasks: number;
    completedTasks: number;
    agentsUsed: string[];
    modelsUsed: string[];
    searchQueriesExecuted: number;
  };
}

export default function KaibanSuperAgent() {
  // State management
  const [topic, setTopic] = useState('');
  const [options, setOptions] = useState<SuperAgentOptions>({
    contentType: 'article',
    targetWordCount: 2000,
    tone: 'professional',
    targetAudience: 'intermediate',
    maxPrimaryResults: 6,
    maxDeepResults: 4,
    enableFactChecking: true,
    includeSourceCitations: true
  });

  const [isRunning, setIsRunning] = useState(false);
  const [progress, setProgress] = useState<WorkflowProgress | null>(null);
  const [result, setResult] = useState<WorkflowResult | null>(null);
  const [logs, setLogs] = useState<string[]>([]);

  const abortControllerRef = useRef<AbortController | null>(null);

  // Handle workflow execution
  const handleExecute = async () => {
    if (!topic.trim()) {
      alert('Please enter a topic');
      return;
    }

    setIsRunning(true);
    setResult(null);
    setLogs([]);
    abortControllerRef.current = new AbortController();

    try {
      setLogs(prev => [...prev, `🚀 Starting KaibanJS Super Agent workflow for: "${topic}"`]);

      // Simulate progress updates for the 7 phases
      const phases = [
        'Topic Analysis',
        'Content Strategy',
        'Primary Research',
        'Gap Analysis',
        'Deep Research',
        'Content Generation',
        'Quality Assurance'
      ];

      let currentPhase = 0;
      const progressInterval = setInterval(() => {
        if (currentPhase < phases.length && isRunning) {
          const progress = Math.round(((currentPhase + 1) / phases.length) * 100);
          setProgress({
            phase: phases[currentPhase],
            progress,
            message: `Processing ${phases[currentPhase]}...`,
            timestamp: new Date().toISOString()
          });
          setLogs(prev => [...prev, `📋 [${phases[currentPhase]}] Processing... (${progress}%)`]);
          currentPhase++;
        } else {
          clearInterval(progressInterval);
        }
      }, 25000); // 25 seconds per phase

      // Make API call
      const response = await fetch('/api/kaiban-super-agent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          topic,
          ...options
        }),
        signal: abortControllerRef.current.signal,
      });

      clearInterval(progressInterval);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        setResult(data.data);
        setProgress({
          phase: 'Completed',
          progress: 100,
          message: 'Workflow completed successfully!',
          timestamp: new Date().toISOString()
        });
        setLogs(prev => [...prev, '✅ KaibanJS Super Agent workflow completed successfully!']);
      } else {
        throw new Error(data.error || 'Workflow failed');
      }

    } catch (error: any) {
      if (error.name === 'AbortError') {
        setLogs(prev => [...prev, '⏹️ Workflow cancelled by user']);
        setProgress({
          phase: 'Cancelled',
          progress: 0,
          message: 'Workflow cancelled',
          timestamp: new Date().toISOString()
        });
      } else {
        console.error('KaibanJS Super Agent error:', error);
        setResult({
          success: false,
          error: error.message || 'An unexpected error occurred'
        });
        setLogs(prev => [...prev, `❌ Error: ${error.message || 'An unexpected error occurred'}`]);
      }
    } finally {
      setIsRunning(false);
    }
  };

  // Handle workflow cancellation
  const handleCancel = () => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    setIsRunning(false);
  };

  // Copy content to clipboard
  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      alert('Content copied to clipboard!');
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  // Download content as file
  const downloadContent = (content: string, filename: string) => {
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <div className="flex items-center justify-center gap-2">
          <Sparkles className="h-8 w-8 text-purple-600" />
          <h1 className="text-3xl font-bold text-gray-900">KaibanJS Super Agent</h1>
        </div>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Advanced multi-agent content generation using KaibanJS framework with Gemini and Qwen models,
          powered by Tavily search for comprehensive research and analysis.
        </p>
      </div>

      {/* Input Section */}
      <div className="bg-white rounded-lg shadow-sm border p-6 space-y-4">
        <h2 className="text-xl font-semibold text-gray-900">Content Generation Request</h2>

        {/* Topic Input */}
        <div>
          <label htmlFor="topic" className="block text-sm font-medium text-gray-700 mb-2">
            Topic *
          </label>
          <input
            id="topic"
            type="text"
            value={topic}
            onChange={(e) => setTopic(e.target.value)}
            placeholder="Enter your content topic..."
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            disabled={isRunning}
          />
        </div>

        {/* Options Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Content Type</label>
            <select
              value={options.contentType}
              onChange={(e) => setOptions(prev => ({ ...prev, contentType: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
              disabled={isRunning}
            >
              <option value="article">Article</option>
              <option value="blog-post">Blog Post</option>
              <option value="research-paper">Research Paper</option>
              <option value="comprehensive-guide">Comprehensive Guide</option>
              <option value="case-study">Case Study</option>
              <option value="white-paper">White Paper</option>
              <option value="tutorial">Tutorial</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Target Word Count</label>
            <input
              type="number"
              value={options.targetWordCount}
              onChange={(e) => setOptions(prev => ({ ...prev, targetWordCount: parseInt(e.target.value) || 2000 }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
              disabled={isRunning}
              min="500"
              max="10000"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Tone</label>
            <select
              value={options.tone}
              onChange={(e) => setOptions(prev => ({ ...prev, tone: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
              disabled={isRunning}
            >
              <option value="professional">Professional</option>
              <option value="casual">Casual</option>
              <option value="academic">Academic</option>
              <option value="conversational">Conversational</option>
              <option value="authoritative">Authoritative</option>
              <option value="engaging">Engaging</option>
              <option value="technical">Technical</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Target Audience</label>
            <select
              value={options.targetAudience}
              onChange={(e) => setOptions(prev => ({ ...prev, targetAudience: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
              disabled={isRunning}
            >
              <option value="beginner">Beginner</option>
              <option value="intermediate">Intermediate</option>
              <option value="expert">Expert</option>
              <option value="general">General</option>
              <option value="technical">Technical</option>
              <option value="business">Business</option>
            </select>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3">
          {!isRunning ? (
            <button
              onClick={handleExecute}
              disabled={!topic.trim()}
              className="flex items-center gap-2 px-6 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <Play className="h-4 w-4" />
              Start Workflow
            </button>
          ) : (
            <button
              onClick={handleCancel}
              className="flex items-center gap-2 px-6 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
            >
              <Square className="h-4 w-4" />
              Cancel
            </button>
          )}
        </div>
      </div>

      {/* Progress Section */}
      {(isRunning || progress) && (
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Workflow Progress</h3>

          {progress && (
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-gray-700">{progress.phase}</span>
                <span className="text-sm text-gray-500">{progress.progress}%</span>
              </div>

              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-purple-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${progress.progress}%` }}
                ></div>
              </div>

              <p className="text-sm text-gray-600">{progress.message}</p>
            </div>
          )}

          {/* Logs */}
          {logs.length > 0 && (
            <div className="mt-4">
              <h4 className="text-sm font-medium text-gray-700 mb-2">Execution Log</h4>
              <div className="bg-gray-50 rounded-md p-3 max-h-40 overflow-y-auto">
                {logs.map((log, index) => (
                  <div key={index} className="text-xs text-gray-600 font-mono">
                    {log}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Results Section */}
      {result && (
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold text-gray-900">
              {result.success ? 'Generated Content' : 'Workflow Error'}
            </h3>

            {result.success && result.content && (
              <div className="flex gap-2">
                <button
                  onClick={() => copyToClipboard(result.content!)}
                  className="flex items-center gap-1 px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
                >
                  <Copy className="h-3 w-3" />
                  Copy
                </button>
                <button
                  onClick={() => downloadContent(result.content!, `${result.title || 'content'}.txt`)}
                  className="flex items-center gap-1 px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
                >
                  <Download className="h-3 w-3" />
                  Download
                </button>
              </div>
            )}
          </div>

          {result.success ? (
            <div className="space-y-4">
              {/* Metadata */}
              {result.metadata && (
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4 bg-gray-50 rounded-md">
                  <div className="text-center">
                    <div className="text-lg font-semibold text-purple-600">{result.wordCount}</div>
                    <div className="text-xs text-gray-600">Words</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold text-purple-600">{result.sourcesUsed}</div>
                    <div className="text-xs text-gray-600">Sources</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold text-purple-600">{result.qualityScore}%</div>
                    <div className="text-xs text-gray-600">Quality</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold text-purple-600">
                      {result.executionTime ? Math.round(result.executionTime / 1000) : 0}s
                    </div>
                    <div className="text-xs text-gray-600">Time</div>
                  </div>
                </div>
              )}

              {/* Content */}
              {result.content && (
                <div className="prose max-w-none">
                  <div className="bg-gray-50 rounded-md p-4 max-h-96 overflow-y-auto">
                    <pre className="whitespace-pre-wrap text-sm text-gray-800 font-sans">
                      {result.content}
                    </pre>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="text-red-600 bg-red-50 p-4 rounded-md">
              <p className="font-medium">Error occurred during workflow execution:</p>
              <p className="text-sm mt-1">{result.error}</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}