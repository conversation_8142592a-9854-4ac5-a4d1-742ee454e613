/**
 * KaibanJS Agents Configuration for Super Agent Workflow
 * 
 * This file defines all the agents that will execute the super agent workflow
 * using KaibanJS framework with Gemini and Qwen models.
 * 
 * Following user preferences:
 * - Use Qwen model for high reasoning phases (1, 4, 7)
 * - Use Gemini model for other phases
 * - Double the max output and input tokens
 * - Use Tavily search for web research
 */

import { Agent } from 'kaibanjs';
import { kaibanTools } from './tools';

// Console logging utility for Kaiban Agents
const logKaibanAgent = (message: string, data?: any) => {
  const timestamp = new Date().toISOString();
  console.log(`🤖 [KAIBAN-AGENT] ${timestamp}: ${message}`);
  if (data) {
    console.log(`📝 [KAIBAN-AGENT-DATA]:`, data);
  }
};

/**
 * Model Configuration
 * Following user preferences for doubled tokens and specific model usage
 */
const QWEN_MODEL_CONFIG = {
  provider: 'openai',
  model: 'qwen/qwen3-235b-a22b-04-28',
  apiKey: process.env.OPENROUTER_API_KEY,
  baseURL: 'https://openrouter.ai/api/v1',
  maxTokens: 8000, // Doubled from typical 4000
  temperature: 0.3,
  defaultHeaders: {
    'HTTP-Referer': 'https://localhost:3000',
    'X-Title': 'KaibanJS Super Agent'
  }
};

const GEMINI_MODEL_CONFIG = {
  provider: 'google',
  model: 'gemini-2.0-flash',
  apiKey: process.env.GEMINI_API_KEY,
  maxTokens: 8000, // Doubled from typical 4000
  temperature: 0.7,
};

logKaibanAgent('Initializing KaibanJS Super Agent Team');

/**
 * Phase 1: Topic Analysis Agent (High Reasoning - Qwen)
 * Analyzes the input topic and creates comprehensive research strategy
 */
export const topicAnalysisAgent = new Agent({
  name: 'TopicAnalyst',
  role: 'Senior Research Strategist',
  goal: 'Analyze topics comprehensively and create detailed research strategies for content creation',
  background: 'Expert research strategist with deep knowledge across multiple domains, specializing in topic analysis and research planning.',
  llmConfig: QWEN_MODEL_CONFIG,
  tools: [], // No tools needed for analysis phase
});

/**
 * Phase 2: Content Strategy Agent (Gemini)
 * Develops content strategy based on topic analysis
 */
export const contentStrategyAgent = new Agent({
  name: 'ContentStrategist',
  role: 'Professional Content Strategist',
  goal: 'Develop comprehensive content strategies that align with audience needs and business objectives',
  background: 'Seasoned content strategist with expertise in audience analysis, content planning, and engagement optimization.',
  llmConfig: GEMINI_MODEL_CONFIG,
  tools: [], // No tools needed for strategy phase
});

/**
 * Phase 3: Primary Research Agent (Gemini + Tavily)
 * Conducts initial web research using Tavily search
 */
export const primaryResearchAgent = new Agent({
  name: 'PrimaryResearcher',
  role: 'Senior Research Analyst',
  goal: 'Conduct comprehensive primary research using advanced search techniques and reliable sources',
  background: 'Expert research analyst specializing in information gathering and source evaluation using advanced search strategies.',
  llmConfig: GEMINI_MODEL_CONFIG,
  tools: kaibanTools, // Includes Tavily search tool
});

/**
 * Phase 4: Gap Analysis Agent (High Reasoning - Qwen)
 * Analyzes research gaps and determines additional research needs
 */
export const gapAnalysisAgent = new Agent({
  name: 'GapAnalyst',
  role: 'Research Quality Analyst',
  goal: 'Identify content gaps and determine additional research requirements for comprehensive coverage',
  background: 'Meticulous research quality analyst with expertise in identifying information gaps and ensuring comprehensive topic coverage.',
  llmConfig: QWEN_MODEL_CONFIG,
  tools: [], // No tools needed for analysis phase
});

/**
 * Phase 5: Deep Research Agent (Gemini + Tavily)
 * Conducts targeted deep research based on gap analysis
 */
export const deepResearchAgent = new Agent({
  name: 'DeepResearcher',
  role: 'Specialized Research Expert',
  goal: 'Conduct targeted deep research to fill identified gaps and enhance content comprehensiveness',
  background: 'Specialized research expert who excels at finding specific, detailed information to fill knowledge gaps.',
  llmConfig: GEMINI_MODEL_CONFIG,
  tools: kaibanTools, // Includes Tavily search tool
});

/**
 * Phase 6: Content Generation Agent (Gemini)
 * Generates high-quality content based on research
 */
export const contentGenerationAgent = new Agent({
  name: 'ContentWriter',
  role: 'Expert Content Creator',
  goal: 'Create engaging, well-structured, and authoritative content based on comprehensive research',
  background: 'Expert content creator with exceptional writing skills and deep understanding of audience engagement.',
  llmConfig: GEMINI_MODEL_CONFIG,
  tools: [], // No tools needed for content generation
});

/**
 * Phase 7: Quality Assurance Agent (High Reasoning - Qwen)
 * Reviews and optimizes the final content
 */
export const qualityAssuranceAgent = new Agent({
  name: 'QualityAssurance',
  role: 'Senior Content Quality Analyst',
  goal: 'Ensure content meets highest quality standards through comprehensive review and optimization',
  background: 'Senior content quality analyst with expertise in fact-checking, readability optimization, and content enhancement.',
  llmConfig: QWEN_MODEL_CONFIG,
  tools: [], // No tools needed for QA phase
});

/**
 * Export all agents as a team
 */
export const superAgentTeam = [
  topicAnalysisAgent,
  contentStrategyAgent,
  primaryResearchAgent,
  gapAnalysisAgent,
  deepResearchAgent,
  contentGenerationAgent,
  qualityAssuranceAgent,
];

logKaibanAgent('Super Agent Team created successfully', {
  totalAgents: superAgentTeam.length,
  qwenAgents: ['TopicAnalyst', 'GapAnalyst', 'QualityAssurance'],
  geminiAgents: ['ContentStrategist', 'PrimaryResearcher', 'DeepResearcher', 'ContentWriter'],
  agentsWithTools: ['PrimaryResearcher', 'DeepResearcher'],
  maxTokens: 8000,
  status: 'ready'
});

/**
 * Agent configuration for easy reference
 */
export const agentConfig = {
  agents: superAgentTeam,
  agentNames: [
    'TopicAnalyst',
    'ContentStrategist', 
    'PrimaryResearcher',
    'GapAnalyst',
    'DeepResearcher',
    'ContentWriter',
    'QualityAssurance'
  ],
  totalAgents: superAgentTeam.length,
  modelDistribution: {
    llama: 3, // High reasoning phases
    gemini: 4 // Other phases
  }
};
