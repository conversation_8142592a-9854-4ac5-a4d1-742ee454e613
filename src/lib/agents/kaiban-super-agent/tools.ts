/**
 * KaibanJS Tools Configuration
 *
 * This file configures the Tavily search tool for KaibanJS agents
 * following the official KaibanJS documentation pattern.
 */

import { TavilySearchResults } from '@kaibanjs/tools';

// Console logging utility for Kaiban Tools
const logKaibanTool = (message: string, data?: any) => {
  const timestamp = new Date().toISOString();
  console.log(`🔧 [KAIBAN-TOOL] ${timestamp}: ${message}`);
  if (data) {
    console.log(`📝 [KAIBAN-TOOL-DATA]:`, data);
  }
};

/**
 * Tavily Search Tool Configuration
 *
 * Following the official KaibanJS Tavily search tool implementation pattern:
 * - Import TavilySearchResults from @kaibanjs/tools
 * - Instantiate with apiKey and maxResults parameters
 * - Add to agent's tools array
 */
logKaibanTool('Initializing Tavily Search Tool');

export const tavilySearchTool = new TavilySearchResults({
  apiKey: process.env.TAVILY_API_KEY || '',
  maxResults: 10, // Maximum number of search results to return
});

logKaibanTool('Tavily Search Tool configured', {
  maxResults: 10,
  apiKeyConfigured: !!process.env.TAVILY_API_KEY,
  toolType: 'KaibanJS TavilySearchResults'
});

/**
 * Tool validation function
 */
export const validateTavilyTool = (): boolean => {
  const isValid = !!process.env.TAVILY_API_KEY;

  if (!isValid) {
    logKaibanTool('❌ Tavily API key not configured', {
      error: 'TAVILY_API_KEY environment variable is missing',
      solution: 'Add your Tavily API key to the .env.local file',
      documentation: 'https://docs.kaibanjs.com/tools-docs/kaibanjs-tools/Tavily'
    });
  } else {
    logKaibanTool('✅ Tavily tool validation passed');
  }

  return isValid;
};

/**
 * Export tools array for easy import in agents
 */
export const kaibanTools = [tavilySearchTool];

logKaibanTool('KaibanJS Tools initialization complete', {
  totalTools: kaibanTools.length,
  toolNames: ['TavilySearchResults'],
  toolSource: '@kaibanjs/tools',
  status: 'ready'
});
